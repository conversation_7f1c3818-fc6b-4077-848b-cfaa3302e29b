# Extensions for GDevelop

These are the "official" extensions directly bundled with GDevelop.

## Writing your extensions or contributing to an existing one 😎

Most extensions can be written in JavaScript.

**[Read this README](https://github.com/4ian/GDevelop/blob/master/newIDE/README-extensions.md)** to learn everything about writing extensions for GDevelop.

## License

- Extensions are provided under the MIT License: see [LICENSE.md](LICENSE.md) file.
- External libraries can be used by extensions (Box2D, Dlib or SPARK for example). See their licenses in their directories.
