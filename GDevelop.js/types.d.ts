// Automatically generated by GDevelop.js/scripts/generate-dts.js
type float = number;

declare class EmscriptenObject {
  /** The object's index in the WASM memory, and thus its unique identifier. */
  ptr: number;

  /**
   * Call this to free the object's underlying memory. It may not be used afterwards.
   *
   * **Call with care** - if the object owns some other objects, those will also be destroyed,
   * or if this object is owned by another object that does not expect it to be externally deleted
   * (e.g. it is a child of a map), objects will be put in an invalid state that will most likely
   * crash the app.
   *
   * If the object is owned by your code, you should still call this method when adequate, as
   * otherwise the memory will never be freed, causing a memory leak, which is to be avoided.
   */
  delete(): void;
}

export enum Variable_Type {
  Unknown = 0,
  MixedTypes = 1,
  String = 2,
  Number = 3,
  Boolean = 4,
  Structure = 5,
  Array = 6,
}

export enum VariablesContainer_SourceType {
  Unknown = 0,
  Global = 1,
  Scene = 2,
  Object = 3,
  Local = 4,
  ExtensionGlobal = 5,
  ExtensionScene = 6,
  Parameters = 7,
  Properties = 8,
}

export enum ObjectsContainer_SourceType {
  Unknown = 0,
  Global = 1,
  Scene = 2,
  Object = 3,
  Function = 4,
}

export enum ObjectsContainersList_VariableExistence {
  DoesNotExist = 0,
  Exists = 1,
  GroupIsEmpty = 2,
  ExistsOnlyOnSomeObjectsOfTheGroup = 3,
}

export enum CustomObjectConfiguration_EdgeAnchor {
  NoAnchor = 0,
  MinEdge = 1,
  MaxEdge = 2,
  Proportional = 3,
  Center = 4,
}

export enum QuickCustomization_Visibility {
  Default = 0,
  Visible = 1,
  Hidden = 2,
}

export enum ProjectDiagnostic_ErrorType {
  UndeclaredVariable = 0,
  MissingBehavior = 1,
  UnknownObject = 2,
  MismatchedObjectType = 3,
}

export enum ExpressionCompletionDescription_CompletionKind {
  Object = 0,
  BehaviorWithPrefix = 1,
  ExpressionWithPrefix = 2,
  Variable = 3,
  TextWithPrefix = 4,
  Property = 5,
  Parameter = 6,
}

export enum EventsFunction_FunctionType {
  Action = 0,
  Condition = 1,
  Expression = 2,
  ExpressionAndCondition = 3,
  ActionWithOperator = 4,
}

export enum EventsFunctionsContainer_FunctionOwner {
  Extension = 0,
  Behavior = 1,
  Object = 2,
}

export enum ParticleEmitterObject_RendererType {
  Point = 0,
  Line = 1,
  Quad = 2,
}

export class VectorString extends EmscriptenObject {
  constructor();
  push_back(str: string): void;
  resize(size: number): void;
  size(): number;
  at(index: number): string;
  set(index: number, str: string): void;
  clear(): void;
  toJSArray(): Array<string>;
}

export class VectorPlatformExtension extends EmscriptenObject {
  size(): number;
  at(index: number): PlatformExtension;
}

export class VectorDependencyMetadata extends EmscriptenObject {
  size(): number;
  at(index: number): DependencyMetadata;
}

export class VectorSourceFileMetadata extends EmscriptenObject {
  size(): number;
  at(index: number): SourceFileMetadata;
}

export class VectorInt extends EmscriptenObject {
  size(): number;
  at(index: number): number;
}

export class VectorVariable extends EmscriptenObject {
  size(): number;
  at(index: number): Variable;
}

export class VectorObjectFolderOrObject extends EmscriptenObject {
  size(): number;
  at(index: number): ObjectFolderOrObject;
}

export class VectorScreenshot extends EmscriptenObject {
  size(): number;
  at(index: number): Screenshot;
}

export class MapStringString extends EmscriptenObject {
  constructor();
  get(name: string): string;
  set(name: string, str: string): void;
  has(name: string): boolean;
  keys(): VectorString;
}

export class MapStringBoolean extends EmscriptenObject {
  constructor();
  get(name: string): boolean;
  set(name: string, value: boolean): void;
  has(name: string): boolean;
  keys(): VectorString;
}

export class MapStringDouble extends EmscriptenObject {
  constructor();
  get(name: string): number;
  set(name: string, value: number): void;
  has(name: string): number;
  keys(): VectorString;
}

export class MapStringVariable extends EmscriptenObject {
  get(name: string): Variable;
  set(name: string, prop: Variable): void;
  has(name: string): boolean;
  keys(): VectorString;
}

export class MapStringExpressionMetadata extends EmscriptenObject {
  get(name: string): ExpressionMetadata;
  set(name: string, prop: ExpressionMetadata): void;
  has(name: string): boolean;
  keys(): VectorString;
}

export class MapStringInstructionMetadata extends EmscriptenObject {
  get(name: string): InstructionMetadata;
  set(name: string, prop: InstructionMetadata): void;
  has(name: string): boolean;
  keys(): VectorString;
}

export class MapStringEventMetadata extends EmscriptenObject {
  get(name: string): EventMetadata;
  set(name: string, prop: EventMetadata): void;
  has(name: string): boolean;
  keys(): VectorString;
}

export class MapExtensionProperties extends EmscriptenObject {
  get(name: string): MapStringPropertyDescriptor;
  set(name: string, prop: MapStringPropertyDescriptor): void;
  has(name: string): boolean;
  keys(): VectorString;
}

export class SetString extends EmscriptenObject {
  constructor();
  toNewVectorString(): VectorString;
}

export class ProjectHelper extends EmscriptenObject {
  static createNewGDJSProject(): Project;
  static initializePlatforms(): void;
  static sanityCheckBehaviorProperty(behavior: Behavior, propertyName: string, newValue: string): string;
  static sanityCheckBehaviorsSharedDataProperty(behavior: BehaviorsSharedData, propertyName: string, newValue: string): string;
  static sanityCheckObjectProperty(configuration: ObjectConfiguration, propertyName: string, newValue: string): string;
  static sanityCheckObjectInitialInstanceProperty(configuration: ObjectConfiguration, propertyName: string, newValue: string): string;
}

export class EventsVariablesFinder extends EmscriptenObject {
  constructor();
  static findAllGlobalVariables(platform: Platform, project: Project): SetString;
  static findAllLayoutVariables(platform: Platform, project: Project, layout: Layout): SetString;
  static findAllObjectVariables(platform: Platform, project: Project, layout: Layout, objectName: string): SetString;
}

export class EventsIdentifiersFinder extends EmscriptenObject {
  constructor();
  static findAllIdentifierExpressions(platform: Platform, project: Project, layout: Layout, identifierType: string, contextObjectName: string): SetString;
}

export class EventsFunctionSelfCallChecker extends EmscriptenObject {
  static isFreeFunctionOnlyCallingItself(project: Project, extension: EventsFunctionsExtension, eventsFunction: EventsFunction): boolean;
  static isBehaviorFunctionOnlyCallingItself(project: Project, extension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, eventsFunction: EventsFunction): boolean;
  static isObjectFunctionOnlyCallingItself(project: Project, extension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, eventsFunction: EventsFunction): boolean;
}

export class InstructionOrExpressionGroupMetadata extends EmscriptenObject {
  constructor();
  setIcon(icon: string): InstructionOrExpressionGroupMetadata;
  getIcon(): string;
}

export class VersionWrapper extends EmscriptenObject {
  static major(): number;
  static minor(): number;
  static build(): number;
  static revision(): number;
  static fullString(): string;
  static status(): string;
}

export class Platform extends EmscriptenObject {
  getName(): string;
  getFullName(): string;
  getSubtitle(): string;
  getDescription(): string;
  getInstructionOrExpressionGroupMetadata(name: string): InstructionOrExpressionGroupMetadata;
  isExtensionLoaded(name: string): boolean;
  removeExtension(name: string): void;
  reloadBuiltinExtensions(): void;
  getAllPlatformExtensions(): VectorPlatformExtension;
}

export class JsPlatform extends EmscriptenObject {
  static get(): JsPlatform;
  addNewExtension(extension: PlatformExtension): void;
  getName(): string;
  getFullName(): string;
  getSubtitle(): string;
  getDescription(): string;
  getInstructionOrExpressionGroupMetadata(name: string): InstructionOrExpressionGroupMetadata;
  isExtensionLoaded(name: string): boolean;
  removeExtension(name: string): void;
  reloadBuiltinExtensions(): void;
  getAllPlatformExtensions(): VectorPlatformExtension;
}

export class PairStringVariable extends EmscriptenObject {
  constructor();
  getName(): string;
  getVariable(): Variable;
}

export class VariableInstructionSwitcher extends EmscriptenObject {
  static isSwitchableVariableInstruction(instructionType: string): boolean;
  static isSwitchableObjectVariableInstruction(instructionType: string): boolean;
  static getSwitchableVariableInstructionIdentifier(instructionType: string): string;
  static getSwitchableInstructionVariableType(instructionType: string): Variable_Type;
  static switchVariableInstructionType(instruction: Instruction, variableType: Variable_Type): void;
  static getVariableTypeFromParameters(platform: Platform, projectScopedContainers: ProjectScopedContainers, instruction: Instruction): Variable_Type;
  static switchBetweenUnifiedInstructionIfNeeded(platform: Platform, projectScopedContainers: ProjectScopedContainers, instruction: Instruction): void;
}

export class Variable extends EmscriptenObject {
  constructor();
  static isPrimitive(type: Variable_Type): boolean;
  getType(): Variable_Type;
  castTo(type: string): void;
  setString(str: string): void;
  getString(): string;
  setValue(val: number): void;
  getValue(): number;
  setBool(val: boolean): void;
  getBool(): boolean;
  hasMixedValues(): boolean;
  setFolded(val: boolean): void;
  isFolded(): boolean;
  getChildrenCount(): number;
  contains(variableToSearch: Variable, recursive: boolean): boolean;
  hasChild(str: string): boolean;
  getChild(str: string): Variable;
  removeChild(name: string): void;
  renameChild(oldName: string, newName: string): boolean;
  getAllChildrenNames(): VectorString;
  removeRecursively(variableToRemove: Variable): void;
  getAtIndex(index: number): Variable;
  pushNew(): Variable;
  removeAtIndex(index: number): void;
  getAllChildrenArray(): VectorVariable;
  moveChildInArray(oldIndex: number, newIndex: number): void;
  insertAtIndex(variable: Variable, index: number): boolean;
  insertChild(name: string, variable: Variable): boolean;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
  resetPersistentUuid(): Variable;
  clearPersistentUuid(): Variable;
}

export class VariablesContainer extends EmscriptenObject {
  constructor(sourceType: VariablesContainer_SourceType);
  getSourceType(): VariablesContainer_SourceType;
  has(name: string): boolean;
  get(name: string): Variable;
  getAt(index: number): Variable;
  getNameAt(index: number): string;
  insert(name: string, variable: Variable, index: number): Variable;
  insertNew(name: string, index: number): Variable;
  remove(name: string): void;
  rename(oldName: string, newName: string): boolean;
  swap(firstIndex: number, secondIndex: number): void;
  move(oldIndex: number, newIndex: number): void;
  getPosition(name: string): number;
  count(): number;
  clear(): void;
  removeRecursively(variableToRemove: Variable): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
  resetPersistentUuid(): VariablesContainer;
  clearPersistentUuid(): VariablesContainer;
}

export class VariablesContainersList extends EmscriptenObject {
  has(name: string): boolean;
  get(name: string): Variable;
  getVariablesContainerFromVariableOrPropertyOrParameterName(variableName: string): VariablesContainer;
  getVariablesContainerFromVariableOrPropertyName(variableName: string): VariablesContainer;
  getVariablesContainerFromVariableNameOnly(variableName: string): VariablesContainer;
  getVariablesContainer(index: number): VariablesContainer;
  getVariablesContainersCount(): number;
}

export class ObjectGroup extends EmscriptenObject {
  constructor();
  getName(): string;
  setName(name: string): void;
  addObject(objectName: string): void;
  removeObject(objectName: string): void;
  find(objectName: string): boolean;
  getAllObjectsNames(): VectorString;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class ObjectVariableHelper extends EmscriptenObject {
  static mergeVariableContainers(objectsContainersList: ObjectsContainersList, objectGroup: ObjectGroup): VariablesContainer;
  static fillAnyVariableBetweenObjects(globalObjectsContainer: ObjectsContainer, objectsContainer: ObjectsContainer, objectGroup: ObjectGroup): void;
  static applyChangesToVariants(eventsBasedObject: EventsBasedObject, objectName: string, changeset: VariablesChangeset): void;
}

export class EventsBasedObjectVariantHelper extends EmscriptenObject {
  static complyVariantsToEventsBasedObject(project: Project, eventsBasedObject: EventsBasedObject): void;
}

export class ObjectGroupsContainer extends EmscriptenObject {
  constructor();
  has(name: string): boolean;
  insert(objectGroup: ObjectGroup, position: number): ObjectGroup;
  insertNew(name: string, position: number): ObjectGroup;
  count(): number;
  get(name: string): ObjectGroup;
  getAt(index: number): ObjectGroup;
  clear(): void;
  remove(name: string): void;
  getPosition(name: string): number;
  rename(oldName: string, newName: string): boolean;
  move(oldIndex: number, newIndex: number): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class PlatformSpecificAssets extends EmscriptenObject {
  constructor();
  has(platform: string, name: string): boolean;
  get(platform: string, name: string): string;
  remove(platform: string, name: string): void;
  set(platform: string, name: string, resourceName: string): void;
  exposeResources(worker: ArbitraryResourceWorker): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class LoadingScreen extends EmscriptenObject {
  constructor();
  isGDevelopLogoShownDuringLoadingScreen(): boolean;
  showGDevelopLogoDuringLoadingScreen(show: boolean): LoadingScreen;
  getGDevelopLogoStyle(): string;
  setGDevelopLogoStyle(value: string): LoadingScreen;
  getBackgroundImageResourceName(): string;
  setBackgroundImageResourceName(value: string): LoadingScreen;
  getBackgroundColor(): number;
  setBackgroundColor(value: number): LoadingScreen;
  getBackgroundFadeInDuration(): number;
  setBackgroundFadeInDuration(value: number): LoadingScreen;
  getMinDuration(): number;
  setMinDuration(value: number): LoadingScreen;
  getLogoAndProgressFadeInDuration(): number;
  setLogoAndProgressFadeInDuration(value: number): LoadingScreen;
  getLogoAndProgressLogoFadeInDelay(): number;
  setLogoAndProgressLogoFadeInDelay(value: number): LoadingScreen;
  getShowProgressBar(): boolean;
  setShowProgressBar(value: boolean): LoadingScreen;
  getProgressBarMaxWidth(): number;
  setProgressBarMaxWidth(value: number): LoadingScreen;
  getProgressBarMinWidth(): number;
  setProgressBarMinWidth(value: number): LoadingScreen;
  getProgressBarWidthPercent(): number;
  setProgressBarWidthPercent(value: number): LoadingScreen;
  getProgressBarHeight(): number;
  setProgressBarHeight(value: number): LoadingScreen;
  getProgressBarColor(): number;
  setProgressBarColor(value: number): LoadingScreen;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class Watermark extends EmscriptenObject {
  constructor();
  isGDevelopWatermarkShown(): boolean;
  showGDevelopWatermark(show: boolean): Watermark;
  getPlacement(): string;
  setPlacement(value: string): Watermark;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class ObjectFolderOrObject extends EmscriptenObject {
  constructor();
  isFolder(): boolean;
  isRootFolder(): boolean;
  getObject(): gdObject;
  getFolderName(): string;
  setFolderName(name: string): void;
  hasObjectNamed(name: string): boolean;
  getObjectNamed(name: string): ObjectFolderOrObject;
  getChildrenCount(): number;
  getChildAt(pos: number): ObjectFolderOrObject;
  getObjectChild(name: string): ObjectFolderOrObject;
  getChildPosition(child: ObjectFolderOrObject): number;
  getParent(): ObjectFolderOrObject;
  insertNewFolder(name: string, newPosition: number): ObjectFolderOrObject;
  moveObjectFolderOrObjectToAnotherFolder(objectFolderOrObject: ObjectFolderOrObject, newParentFolder: ObjectFolderOrObject, newPosition: number): void;
  moveChild(oldIndex: number, newIndex: number): void;
  removeFolderChild(childToRemove: ObjectFolderOrObject): void;
  isADescendantOf(otherObjectFolderOrObject: ObjectFolderOrObject): boolean;
  getQuickCustomizationVisibility(): QuickCustomization_Visibility;
  setQuickCustomizationVisibility(visibility: QuickCustomization_Visibility): void;
}

export class ObjectsContainer extends EmscriptenObject {
  constructor(sourceType: ObjectsContainer_SourceType);
  getSourceType(): ObjectsContainer_SourceType;
  insertNewObject(project: Project, type: string, name: string, pos: number): gdObject;
  insertNewObjectInFolder(project: Project, type: string, name: string, folder: ObjectFolderOrObject, pos: number): gdObject;
  insertObject(obj: gdObject, pos: number): gdObject;
  hasObjectNamed(name: string): boolean;
  getObject(name: string): gdObject;
  getObjectAt(pos: number): gdObject;
  getObjectPosition(name: string): number;
  removeObject(name: string): void;
  moveObject(oldIndex: number, newIndex: number): void;
  moveObjectFolderOrObjectToAnotherContainerInFolder(objectFolderOrObject: ObjectFolderOrObject, newObjectsContainer: ObjectsContainer, parentObjectFolderOrObject: ObjectFolderOrObject, newPosition: number): void;
  getObjectsCount(): number;
  getRootFolder(): ObjectFolderOrObject;
  getAllObjectFolderOrObjects(): VectorObjectFolderOrObject;
  getObjectGroups(): ObjectGroupsContainer;
  getTypeOfBehavior(layout: ObjectsContainer, name: string, searchInGroups: boolean): string;
  getTypeOfObject(layout: ObjectsContainer, name: string, searchInGroups: boolean): string;
  getBehaviorsOfObject(layout: ObjectsContainer, name: string, searchInGroups: boolean): VectorString;
  isDefaultBehavior(layout: ObjectsContainer, objectOrGroupName: string, behaviorName: string, searchInGroups: boolean): boolean;
  getTypeOfBehaviorInObjectOrGroup(layout: ObjectsContainer, objectOrGroupName: string, behaviorName: string, searchInGroups: boolean): string;
  getBehaviorNamesInObjectOrGroup(layout: ObjectsContainer, objectOrGroupName: string, behaviorType: string, searchInGroups: boolean): VectorString;
}

export class Project extends EmscriptenObject {
  constructor();
  setName(name: string): void;
  getName(): string;
  getCategories(): VectorString;
  setDescription(description: string): void;
  getDescription(): string;
  setVersion(authorName: string): void;
  getVersion(): string;
  setAuthor(authorName: string): void;
  getAuthor(): string;
  getAuthorIds(): VectorString;
  getAuthorUsernames(): VectorString;
  isPlayableWithKeyboard(): boolean;
  setPlayableWithKeyboard(playable: boolean): void;
  isPlayableWithGamepad(): boolean;
  setPlayableWithGamepad(playable: boolean): void;
  isPlayableWithMobile(): boolean;
  setPlayableWithMobile(playable: boolean): void;
  setPackageName(packageName: string): void;
  getPackageName(): string;
  setTemplateSlug(templateSlug: string): void;
  getTemplateSlug(): string;
  setOrientation(orientation: string): void;
  getOrientation(): string;
  setProjectUuid(projectUuid: string): void;
  getProjectUuid(): string;
  resetProjectUuid(): void;
  setProjectFile(file: string): void;
  getProjectFile(): string;
  setGameResolutionSize(width: number, height: number): void;
  getGameResolutionWidth(): number;
  getGameResolutionHeight(): number;
  getAdaptGameResolutionAtRuntime(): boolean;
  setAdaptGameResolutionAtRuntime(adaptGameResolutionAtRuntime: boolean): void;
  setScaleMode(scaleMode: string): void;
  getScaleMode(): string;
  setPixelsRounding(pixelsRounding: boolean): void;
  getPixelsRounding(): boolean;
  setSizeOnStartupMode(orientation: string): void;
  getSizeOnStartupMode(): string;
  setAntialiasingMode(antialiasingMode: string): void;
  getAntialiasingMode(): string;
  setAntialisingEnabledOnMobile(pixelsRounding: boolean): void;
  isAntialisingEnabledOnMobile(): boolean;
  getMaximumFPS(): number;
  setMaximumFPS(fps: number): void;
  getMinimumFPS(): number;
  setMinimumFPS(fps: number): void;
  setFolderProject(enable: boolean): void;
  isFolderProject(): boolean;
  setUseDeprecatedZeroAsDefaultZOrder(enable: boolean): void;
  getUseDeprecatedZeroAsDefaultZOrder(): boolean;
  setLastCompilationDirectory(path: string): void;
  getLastCompilationDirectory(): string;
  getExtensionProperties(): ExtensionProperties;
  addPlatform(platform: Platform): void;
  getCurrentPlatform(): Platform;
  getPlatformSpecificAssets(): PlatformSpecificAssets;
  getLoadingScreen(): LoadingScreen;
  getWatermark(): Watermark;
  hasLayoutNamed(name: string): boolean;
  getLayout(name: string): Layout;
  getLayoutAt(index: number): Layout;
  moveLayout(oldIndex: number, newIndex: number): void;
  swapLayouts(first: number, second: number): void;
  getLayoutsCount(): number;
  insertNewLayout(name: string, position: number): Layout;
  removeLayout(name: string): void;
  setFirstLayout(name: string): void;
  getFirstLayout(): string;
  getLayoutPosition(name: string): number;
  hasExternalEventsNamed(name: string): boolean;
  getExternalEvents(name: string): ExternalEvents;
  getExternalEventsAt(index: number): ExternalEvents;
  moveExternalEvents(oldIndex: number, newIndex: number): void;
  swapExternalEvents(first: number, second: number): void;
  getExternalEventsCount(): number;
  insertNewExternalEvents(name: string, position: number): ExternalEvents;
  removeExternalEvents(name: string): void;
  getExternalEventsPosition(name: string): number;
  hasExternalLayoutNamed(name: string): boolean;
  getExternalLayout(name: string): ExternalLayout;
  getExternalLayoutAt(index: number): ExternalLayout;
  moveExternalLayout(oldIndex: number, newIndex: number): void;
  swapExternalLayouts(first: number, second: number): void;
  getExternalLayoutsCount(): number;
  insertNewExternalLayout(name: string, position: number): ExternalLayout;
  removeExternalLayout(name: string): void;
  getExternalLayoutPosition(name: string): number;
  hasEventsFunctionsExtensionNamed(name: string): boolean;
  getEventsFunctionsExtension(name: string): EventsFunctionsExtension;
  getEventsFunctionsExtensionAt(index: number): EventsFunctionsExtension;
  moveEventsFunctionsExtension(oldIndex: number, newIndex: number): void;
  swapEventsFunctionsExtensions(first: number, second: number): void;
  getEventsFunctionsExtensionsCount(): number;
  insertNewEventsFunctionsExtension(name: string, position: number): EventsFunctionsExtension;
  insertEventsFunctionsExtension(eventsFunctionsExtension: EventsFunctionsExtension, position: number): EventsFunctionsExtension;
  removeEventsFunctionsExtension(name: string): void;
  getEventsFunctionsExtensionPosition(name: string): number;
  unserializeAndInsertExtensionsFrom(eventsFunctionsExtensionsElement: SerializerElement): void;
  hasEventsBasedBehavior(type: string): boolean;
  getEventsBasedBehavior(type: string): EventsBasedBehavior;
  hasEventsBasedObject(type: string): boolean;
  getEventsBasedObject(type: string): EventsBasedObject;
  getVariables(): VariablesContainer;
  getObjects(): ObjectsContainer;
  getResourcesManager(): ResourcesManager;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
  getWholeProjectDiagnosticReport(): WholeProjectDiagnosticReport;
  static isNameSafe(name: string): boolean;
  static getSafeName(name: string): string;
}

export class ObjectsContainersList extends EmscriptenObject {
  static makeNewObjectsContainersListForProjectAndLayout(project: Project, layout: Layout): ObjectsContainersList;
  static makeNewObjectsContainersListForContainers(globalObjectsContainer: ObjectsContainer, objectsContainer: ObjectsContainer): ObjectsContainersList;
  getTypeOfObject(objectName: string): string;
  getTypeOfBehavior(name: string, searchInGroups: boolean): string;
  getBehaviorsOfObject(objectOrGroupName: string, searchInGroups: boolean): VectorString;
  getBehaviorNamesInObjectOrGroup(objectOrGroupName: string, behaviorType: string, searchInGroups: boolean): VectorString;
  getAnimationNamesOfObject(name: string): VectorString;
  getTypeOfBehaviorInObjectOrGroup(objectOrGroupName: string, behaviorName: string, searchInGroups: boolean): string;
  hasObjectOrGroupNamed(name: string): boolean;
  hasObjectNamed(name: string): boolean;
  hasObjectOrGroupWithVariableNamed(objectName: string, variableName: string): ObjectsContainersList_VariableExistence;
  getObjectsContainerSourceType(objectOrGroupName: string): ObjectsContainer_SourceType;
  getObjectsContainer(index: number): ObjectsContainer;
  getObjectsContainersCount(): number;
}

export class ProjectScopedContainers extends EmscriptenObject {
  static makeNewProjectScopedContainersForProjectAndLayout(project: Project, layout: Layout): ProjectScopedContainers;
  static makeNewProjectScopedContainersForProject(project: Project): ProjectScopedContainers;
  static makeNewProjectScopedContainersForEventsFunctionsExtension(project: Project, eventsFunctionsExtension: EventsFunctionsExtension): ProjectScopedContainers;
  static makeNewProjectScopedContainersForFreeEventsFunction(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsFunction: EventsFunction, parameterObjectsContainer: ObjectsContainer, parameterVariablesContainer: VariablesContainer): ProjectScopedContainers;
  static makeNewProjectScopedContainersForBehaviorEventsFunction(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, eventsFunction: EventsFunction, parameterObjectsContainer: ObjectsContainer, parameterVariablesContainer: VariablesContainer, propertyVariablesContainer: VariablesContainer): ProjectScopedContainers;
  static makeNewProjectScopedContainersForObjectEventsFunction(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, eventsFunction: EventsFunction, parameterObjectsContainer: ObjectsContainer, parameterVariablesContainer: VariablesContainer, propertyVariablesContainer: VariablesContainer): ProjectScopedContainers;
  static makeNewProjectScopedContainersForEventsBasedObject(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, outputObjectsContainer: ObjectsContainer): ProjectScopedContainers;
  static makeNewProjectScopedContainersWithLocalVariables(projectScopedContainers: ProjectScopedContainers, event: BaseEvent): ProjectScopedContainers;
  addPropertiesContainer(propertiesContainer: PropertiesContainer): ProjectScopedContainers;
  addParameters(parameters: ParameterMetadataContainer): ProjectScopedContainers;
  getObjectsContainersList(): ObjectsContainersList;
  getVariablesContainersList(): VariablesContainersList;
}

export class ExtensionProperties extends EmscriptenObject {
  getValue(extension: string, property: string): string;
  setValue(extension: string, property: string, newValue: string): void;
  hasProperty(extension: string, property: string): boolean;
  getAllExtensionProperties(extension: string, project: Project): MapStringPropertyDescriptor;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class Behavior extends EmscriptenObject {
  constructor();
  clone(): Behavior;
  setName(name: string): void;
  getName(): string;
  getTypeName(): string;
  getProperties(): MapStringPropertyDescriptor;
  updateProperty(name: string, value: string): boolean;
  initializeContent(): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
  isFolded(): boolean;
  setFolded(folded: boolean): void;
  isDefaultBehavior(): boolean;
  getPropertiesQuickCustomizationVisibilities(): QuickCustomizationVisibilitiesContainer;
}

export class BehaviorJsImplementation extends Behavior {
  constructor();
  getProperties(behaviorContent: SerializerElement): MapStringPropertyDescriptor;
  updateProperty(behaviorContent: SerializerElement, name: string, value: string): boolean;
  initializeContent(behaviorContent: SerializerElement): void;
}

export class BehaviorsSharedData extends EmscriptenObject {
  constructor();
  setName(name: string): void;
  getName(): string;
  getTypeName(): string;
  getProperties(): MapStringPropertyDescriptor;
  updateProperty(name: string, value: string): boolean;
  initializeContent(): void;
  getPropertiesQuickCustomizationVisibilities(): QuickCustomizationVisibilitiesContainer;
}

export class BehaviorSharedDataJsImplementation extends BehaviorsSharedData {
  constructor();
  getProperties(behaviorSharedDataContent: SerializerElement): MapStringPropertyDescriptor;
  updateProperty(behaviorSharedDataContent: SerializerElement, name: string, value: string): boolean;
  initializeContent(behaviorSharedDataContent: SerializerElement): void;
}

export class ObjectConfiguration extends EmscriptenObject {
  constructor();
  clone(): UniquePtrObjectConfiguration;
  getType(): string;
  setType(typeName: string): void;
  getProperties(): MapStringPropertyDescriptor;
  updateProperty(name: string, value: string): boolean;
  getInitialInstanceProperties(instance: InitialInstance): MapStringPropertyDescriptor;
  updateInitialInstanceProperty(instance: InitialInstance, name: string, value: string): boolean;
  exposeResources(worker: ArbitraryResourceWorker): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
  getAnimationsCount(): number;
  getAnimationName(index: number): string;
}

export class UniquePtrObjectConfiguration extends EmscriptenObject {
  get(): ObjectConfiguration;
  release(): ObjectConfiguration;
}

export class gdObject extends EmscriptenObject {
  constructor(name: string, type: string, configuration: ObjectConfiguration);
  clone(): UniquePtrObject;
  setName(name: string): void;
  getName(): string;
  setAssetStoreId(assetStoreId: string): void;
  getAssetStoreId(): string;
  setType(type: string): void;
  getType(): string;
  getConfiguration(): ObjectConfiguration;
  getVariables(): VariablesContainer;
  getEffects(): EffectsContainer;
  getAllBehaviorNames(): VectorString;
  hasBehaviorNamed(name: string): boolean;
  addNewBehavior(project: Project, type: string, name: string): Behavior;
  getBehavior(name: string): Behavior;
  removeBehavior(name: string): void;
  renameBehavior(oldName: string, name: string): boolean;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
  resetPersistentUuid(): gdObject;
  clearPersistentUuid(): gdObject;
}

export class UniquePtrObject extends EmscriptenObject {
  get(): gdObject;
  release(): gdObject;
}

export class ObjectJsImplementation extends ObjectConfiguration {
  constructor();
  clone(): UniquePtrObjectConfiguration;
  getProperties(): MapStringPropertyDescriptor;
  updateProperty(name: string, value: string): boolean;
  getInitialInstanceProperties(instance: InitialInstance): MapStringPropertyDescriptor;
  updateInitialInstanceProperty(instance: InitialInstance, name: string, value: string): boolean;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
  content: Record<string, any>;
}

export class CustomObjectConfiguration extends ObjectConfiguration {
  clone(): UniquePtrObjectConfiguration;
  getVariantName(): string;
  setVariantName(name: string): void;
  isForcedToOverrideEventsBasedObjectChildrenConfiguration(): boolean;
  isMarkedAsOverridingEventsBasedObjectChildrenConfiguration(): boolean;
  setMarkedAsOverridingEventsBasedObjectChildrenConfiguration(isOverridingEventsBasedObjectChildrenConfiguration: boolean): void;
  clearChildrenConfiguration(): void;
  getChildObjectConfiguration(objectName: string): ObjectConfiguration;
  getProperties(): MapStringPropertyDescriptor;
  updateProperty(name: string, value: string): boolean;
  getInitialInstanceProperties(instance: InitialInstance): MapStringPropertyDescriptor;
  updateInitialInstanceProperty(instance: InitialInstance, name: string, value: string): boolean;
  getAnimations(): SpriteAnimationList;
  isChildObjectFolded(childName: string): boolean;
  setChildObjectFolded(childName: string, folded: boolean): void;
  static getEdgeAnchorFromString(value: string): CustomObjectConfiguration_EdgeAnchor;
}

export class Layout extends EmscriptenObject {
  constructor();
  setName(name: string): void;
  getName(): string;
  setBackgroundColor(r: number, g: number, b: number): void;
  getBackgroundColorRed(): number;
  getBackgroundColorGreen(): number;
  getBackgroundColorBlue(): number;
  setWindowDefaultTitle(name: string): void;
  getWindowDefaultTitle(): string;
  getInitialInstances(): InitialInstancesContainer;
  getVariables(): VariablesContainer;
  getObjects(): ObjectsContainer;
  getEvents(): EventsList;
  getLayers(): LayersContainer;
  updateBehaviorsSharedData(project: Project): void;
  getAllBehaviorSharedDataNames(): VectorString;
  hasBehaviorSharedData(behaviorName: string): boolean;
  getBehaviorSharedData(behaviorName: string): BehaviorsSharedData;
  insertNewLayer(name: string, position: number): void;
  insertLayer(layer: Layer, position: number): void;
  getLayer(name: string): Layer;
  getLayerAt(pos: number): Layer;
  hasLayerNamed(name: string): boolean;
  removeLayer(name: string): void;
  getLayersCount(): number;
  swapLayers(firstLayerIndex: number, secondLayerIndex: number): void;
  moveLayer(oldIndex: number, newIndex: number): void;
  getAssociatedEditorSettings(): EditorSettings;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
  setStopSoundsOnStartup(enable: boolean): void;
  stopSoundsOnStartup(): boolean;
}

export class ExternalEvents extends EmscriptenObject {
  constructor();
  setName(name: string): void;
  getName(): string;
  getAssociatedLayout(): string;
  setAssociatedLayout(name: string): void;
  getEvents(): EventsList;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
}

export class ExternalLayout extends EmscriptenObject {
  constructor();
  setName(name: string): void;
  getName(): string;
  setAssociatedLayout(name: string): void;
  getAssociatedLayout(): string;
  getInitialInstances(): InitialInstancesContainer;
  getAssociatedEditorSettings(): EditorSettings;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class Effect extends EmscriptenObject {
  constructor();
  setName(name_: string): void;
  getName(): string;
  setEffectType(effectType_: string): void;
  getEffectType(): string;
  setFolded(val: boolean): void;
  isFolded(): boolean;
  setDoubleParameter(name: string, value: number): void;
  getDoubleParameter(name: string): number;
  setStringParameter(name: string, value: string): void;
  getStringParameter(name: string): string;
  setBooleanParameter(name: string, value: boolean): void;
  getBooleanParameter(name: string): boolean;
  getAllDoubleParameters(): MapStringDouble;
  getAllStringParameters(): MapStringString;
  getAllBooleanParameters(): MapStringBoolean;
  clearParameters(): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class EffectsContainer extends EmscriptenObject {
  constructor();
  hasEffectNamed(name: string): boolean;
  getEffect(name: string): Effect;
  getEffectAt(index: number): Effect;
  getEffectPosition(name: string): number;
  getEffectsCount(): number;
  insertNewEffect(name: string, position: number): Effect;
  insertEffect(theEffect: Effect, position: number): void;
  removeEffect(name: string): void;
  swapEffects(firstEffectIndex: number, secondEffectIndex: number): void;
  moveEffect(oldIndex: number, newIndex: number): void;
  clear(): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class Layer extends EmscriptenObject {
  constructor();
  setName(name: string): void;
  getName(): string;
  setRenderingType(renderingType: string): void;
  getRenderingType(): string;
  setCameraType(cameraType: string): void;
  getCameraType(): string;
  setDefaultCameraBehavior(defaultCameraBehavior: string): void;
  getDefaultCameraBehavior(): string;
  setVisibility(visible: boolean): void;
  getVisibility(): boolean;
  setLocked(isLocked: boolean): void;
  isLocked(): boolean;
  setLightingLayer(lightingLayer: boolean): void;
  isLightingLayer(): boolean;
  setFollowBaseLayerCamera(followBaseLayerCamera: boolean): void;
  isFollowingBaseLayerCamera(): boolean;
  getCamera3DNearPlaneDistance(): number;
  setCamera3DNearPlaneDistance(distance: number): void;
  getCamera3DFarPlaneDistance(): number;
  setCamera3DFarPlaneDistance(distance: number): void;
  getCamera3DFieldOfView(): number;
  setCamera3DFieldOfView(angle: number): void;
  setAmbientLightColor(r: number, g: number, b: number): void;
  getAmbientLightColorRed(): number;
  getAmbientLightColorGreen(): number;
  getAmbientLightColorBlue(): number;
  getEffects(): EffectsContainer;
  getCameraCount(): number;
  setCameraCount(cameraCount: number): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class LayersContainer extends EmscriptenObject {
  insertNewLayer(name: string, position: number): void;
  insertLayer(layer: Layer, position: number): void;
  getLayer(name: string): Layer;
  getLayerAt(pos: number): Layer;
  hasLayerNamed(name: string): boolean;
  removeLayer(name: string): void;
  getLayersCount(): number;
  swapLayers(firstLayerIndex: number, secondLayerIndex: number): void;
  moveLayer(oldIndex: number, newIndex: number): void;
  serializeLayersTo(element: SerializerElement): void;
  unserializeLayersFrom(element: SerializerElement): void;
}

export class PropertyDescriptor extends EmscriptenObject {
  constructor(propValue: string);
  setValue(value: string): PropertyDescriptor;
  getValue(): string;
  setType(type: string): PropertyDescriptor;
  getType(): string;
  setLabel(label: string): PropertyDescriptor;
  getLabel(): string;
  setDescription(label: string): PropertyDescriptor;
  getDescription(): string;
  setGroup(label: string): PropertyDescriptor;
  getGroup(): string;
  addExtraInfo(type: string): PropertyDescriptor;
  setExtraInfo(info: VectorString): PropertyDescriptor;
  getExtraInfo(): VectorString;
  setHidden(enable: boolean): PropertyDescriptor;
  isHidden(): boolean;
  setDeprecated(enable: boolean): PropertyDescriptor;
  isDeprecated(): boolean;
  setAdvanced(enable: boolean): PropertyDescriptor;
  isAdvanced(): boolean;
  getMeasurementUnit(): MeasurementUnit;
  setMeasurementUnit(measurementUnit: MeasurementUnit): PropertyDescriptor;
  hasImpactOnOtherProperties(): boolean;
  setHasImpactOnOtherProperties(enable: boolean): PropertyDescriptor;
  getQuickCustomizationVisibility(): QuickCustomization_Visibility;
  setQuickCustomizationVisibility(visibility: QuickCustomization_Visibility): PropertyDescriptor;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
  serializeValuesTo(element: SerializerElement): void;
  unserializeValuesFrom(element: SerializerElement): void;
}

export class MeasurementUnit extends EmscriptenObject {
  constructor(name: string, label: string, description: string);
  getName(): string;
  getLabel(): string;
  getDescription(): string;
  getElementsWithWords(): string;
  getElementsCount(): number;
  getElementPower(elementIndex: number): number;
  getElementBaseUnit(elementIndex: number): MeasurementBaseUnit;
  isUndefined(): boolean;
  static applyTranslation(): void;
  static getUndefined(): MeasurementUnit;
  static getDimensionless(): MeasurementUnit;
  static getDegreeAngle(): MeasurementUnit;
  static getSecond(): MeasurementUnit;
  static getPixel(): MeasurementUnit;
  static getPixelSpeed(): MeasurementUnit;
  static getPixelAcceleration(): MeasurementUnit;
  static getAngularSpeed(): MeasurementUnit;
  static getNewton(): MeasurementUnit;
  static getDefaultMeasurementUnitsCount(): number;
  static getDefaultMeasurementUnitAtIndex(index: number): MeasurementUnit;
  static getDefaultMeasurementUnitByName(name: string): MeasurementUnit;
  static hasDefaultMeasurementUnitNamed(name: string): boolean;
}

export class MeasurementBaseUnit extends EmscriptenObject {
  constructor(name: string, symbol: string, quantity: string);
  getName(): string;
  getSymbol(): string;
  getQuantity(): string;
}

export class NamedPropertyDescriptor extends PropertyDescriptor {
  constructor();
  setName(name: string): PropertyDescriptor;
  getName(): string;
}

export class MapStringPropertyDescriptor extends EmscriptenObject {
  constructor();
  getOrCreate(name: string): PropertyDescriptor;
  get(name: string): PropertyDescriptor;
  set(name: string, prop: PropertyDescriptor): void;
  has(name: string): boolean;
  keys(): VectorString;
}

export class MapStringSerializerValue extends EmscriptenObject {
  constructor();
  getOrCreate(name: string): SerializerValue;
  get(name: string): SerializerValue;
  set(name: string, prop: SerializerValue): void;
  has(name: string): boolean;
  keys(): VectorString;
}

export class VectorPairStringSharedPtrSerializerElement extends EmscriptenObject {
  size(): number;
  getString(id: number): string;
  getSharedPtrSerializerElement(id: number): SharedPtrSerializerElement;
}

export class Resource extends EmscriptenObject {
  constructor();
  clone(): Resource;
  setName(name: string): void;
  getName(): string;
  setKind(kind: string): void;
  getKind(): string;
  isUserAdded(): boolean;
  setUserAdded(yes: boolean): void;
  useFile(): boolean;
  setFile(file: string): void;
  getFile(): string;
  setMetadata(metadata: string): void;
  getMetadata(): string;
  setOrigin(originName: string, originIdentifier: string): void;
  getOriginName(): string;
  getOriginIdentifier(): string;
  getProperties(): MapStringPropertyDescriptor;
  updateProperty(name: string, value: string): boolean;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class ResourcesManager extends EmscriptenObject {
  constructor();
  getAllResourceNames(): VectorString;
  findFilesNotInResources(filesToCheck: VectorString): VectorString;
  hasResource(name: string): boolean;
  getResource(name: string): Resource;
  getResourceNameWithOrigin(originName: string, originIdentifier: string): string;
  getResourceNameWithFile(file: string): string;
  addResource(res: Resource): boolean;
  removeResource(name: string): void;
  renameResource(oldName: string, name: string): void;
  getResourcePosition(name: string): number;
  moveResourceUpInList(oldName: string): boolean;
  moveResourceDownInList(oldName: string): boolean;
  moveResource(oldIndex: number, newIndex: number): void;
}

export class ImageResource extends Resource {
  constructor();
  isSmooth(): boolean;
  setSmooth(enable: boolean): void;
}

export class AudioResource extends Resource {
  constructor();
}

export class FontResource extends Resource {
  constructor();
}

export class BitmapFontResource extends Resource {
  constructor();
}

export class VideoResource extends Resource {
  constructor();
}

export class JsonResource extends Resource {
  constructor();
}

export class SpineResource extends JsonResource {
  constructor();
}

export class TilemapResource extends Resource {
  constructor();
}

export class TilesetResource extends Resource {
  constructor();
}

export class Model3DResource extends Resource {
  constructor();
}

export class AtlasResource extends Resource {
  constructor();
}

export class JavaScriptResource extends Resource {
  constructor();
}

export class InitialInstance extends EmscriptenObject {
  constructor();
  setObjectName(name: string): void;
  getObjectName(): string;
  getX(): number;
  setX(x: number): void;
  getY(): number;
  setY(y: number): void;
  getZ(): number;
  setZ(z: number): void;
  getAngle(): number;
  setAngle(angle: number): void;
  getRotationX(): number;
  setRotationX(rotationX: number): void;
  getRotationY(): number;
  setRotationY(rotationY: number): void;
  isLocked(): boolean;
  setLocked(lock: boolean): void;
  isSealed(): boolean;
  setSealed(seal: boolean): void;
  shouldKeepRatio(): boolean;
  setShouldKeepRatio(keepRatio: boolean): void;
  getZOrder(): number;
  setZOrder(zOrder: number): void;
  getOpacity(): number;
  setOpacity(opacity: number): void;
  getLayer(): string;
  setLayer(layer: string): void;
  isFlippedX(): boolean;
  setFlippedX(flippedX: boolean): void;
  isFlippedY(): boolean;
  setFlippedY(flippedY: boolean): void;
  isFlippedZ(): boolean;
  setFlippedZ(flippedZ: boolean): void;
  setHasCustomSize(enable: boolean): void;
  hasCustomSize(): boolean;
  setHasCustomDepth(enable: boolean): void;
  hasCustomDepth(): boolean;
  setCustomWidth(width: number): void;
  getCustomWidth(): number;
  setCustomHeight(height: number): void;
  getCustomHeight(): number;
  setCustomDepth(depth: number): void;
  getCustomDepth(): number;
  resetPersistentUuid(): InitialInstance;
  updateCustomProperty(name: string, value: string, globalObjectsContainer: ObjectsContainer, objectsContainer: ObjectsContainer): void;
  getCustomProperties(globalObjectsContainer: ObjectsContainer, objectsContainer: ObjectsContainer): MapStringPropertyDescriptor;
  getRawDoubleProperty(name: string): number;
  getRawStringProperty(name: string): string;
  setRawDoubleProperty(name: string, value: number): void;
  setRawStringProperty(name: string, value: string): void;
  getVariables(): VariablesContainer;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class InitialInstancesContainer extends EmscriptenObject {
  constructor();
  clone(): InitialInstancesContainer;
  getInstancesCount(): number;
  iterateOverInstances(func: InitialInstanceFunctor): void;
  iterateOverInstancesWithZOrdering(func: InitialInstanceFunctor, layer: string): void;
  moveInstancesToLayer(fromLayer: string, toLayer: string): void;
  removeAllInstancesOnLayer(layer: string): void;
  removeInitialInstancesOfObject(obj: string): void;
  hasInstancesOfObject(objectName: string): boolean;
  isInstancesCountOfObjectGreaterThan(objectName: string, minInstanceCount: number): boolean;
  someInstancesAreOnLayer(layer: string): boolean;
  renameInstancesOfObject(oldName: string, newName: string): void;
  removeInstance(inst: InitialInstance): void;
  getLayerInstancesCount(layerName: string): number;
  insertNewInitialInstance(): InitialInstance;
  insertInitialInstance(inst: InitialInstance): InitialInstance;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class HighestZOrderFinder extends EmscriptenObject {
  constructor();
  restrictSearchToLayer(layer: string): void;
  getHighestZOrder(): number;
  getLowestZOrder(): number;
  reset(): void;
  getInstancesCount(): number;
}

export class InitialInstanceFunctor extends EmscriptenObject {}

export class InitialInstanceJSFunctorWrapper extends EmscriptenObject {}

export class InitialInstanceJSFunctor extends InitialInstanceJSFunctorWrapper {
  constructor();
  invoke(instance: InitialInstance): void;
}

export class SerializerValue extends EmscriptenObject {
  getBool(): boolean;
  getString(): string;
  getInt(): number;
  getDouble(): number;
  getRawString(): string;
  isBoolean(): boolean;
  isString(): boolean;
  isInt(): boolean;
  isDouble(): boolean;
}

export class SerializerElement extends EmscriptenObject {
  constructor();
  setBoolValue(value: boolean): void;
  setStringValue(value: string): void;
  setIntValue(value: number): void;
  setDoubleValue(value: number): void;
  getValue(): SerializerValue;
  getBoolValue(): boolean;
  getStringValue(): string;
  getIntValue(): number;
  getDoubleValue(): number;
  isValueUndefined(): boolean;
  setBoolAttribute(name: string, value: boolean): SerializerElement;
  setStringAttribute(name: string, value: string): SerializerElement;
  setIntAttribute(name: string, value: number): SerializerElement;
  setDoubleAttribute(name: string, value: number): SerializerElement;
  getBoolAttribute(name: string): boolean;
  getStringAttribute(name: string): string;
  getIntAttribute(name: string): number;
  getDoubleAttribute(name: string): number;
  considerAsArray(): void;
  consideredAsArray(): boolean;
  addChild(str: string): SerializerElement;
  getChild(str: string): SerializerElement;
  setChild(str: string, element: SerializerElement): void;
  hasChild(str: string): boolean;
  getAllChildren(): VectorPairStringSharedPtrSerializerElement;
  getAllAttributes(): MapStringSerializerValue;
}

export class SharedPtrSerializerElement extends EmscriptenObject {
  get(): SerializerElement;
  reset(): void;
}

export class Serializer extends EmscriptenObject {
  static toJSON(element: SerializerElement): string;
  static fromJSON(json: string): SerializerElement;
  static fromJSObject(object: Object): gdSerializerElement;
  static toJSObject(element: gdSerializerElement): any;
}

export class ObjectAssetSerializer extends EmscriptenObject {
  static serializeTo(project: Project, obj: gdObject, objectFullName: string, element: SerializerElement, usedResourceNames: VectorString): void;
}

export class InstructionsList extends EmscriptenObject {
  constructor();
  insert(instr: Instruction, pos: number): Instruction;
  insertInstructions(list: InstructionsList, begin: number, end: number, pos: number): void;
  size(): number;
  set(index: number, instr: Instruction): void;
  contains(instr: Instruction): boolean;
  get(index: number): Instruction;
  remove(instr: Instruction): void;
  removeAt(index: number): void;
  clear(): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
}

export class Instruction extends EmscriptenObject {
  constructor();
  instruction(): Instruction;
  setType(type: string): void;
  getType(): string;
  setInverted(inverted: boolean): void;
  isInverted(): boolean;
  setAwaited(awaited: boolean): void;
  isAwaited(): boolean;
  setParameter(id: number, value: string): void;
  getParameter(id: number): Expression;
  setParametersCount(count: number): void;
  getParametersCount(): number;
  getSubInstructions(): InstructionsList;
}

export class Expression extends EmscriptenObject {
  getPlainString(): string;
  getRootNode(): ExpressionNode;
}

export class VectorPairStringTextFormatting extends EmscriptenObject {
  size(): number;
  getString(id: number): string;
  getTextFormatting(id: number): TextFormatting;
}

export class TextFormatting extends EmscriptenObject {
  getUserData(): number;
}

export class InstructionSentenceFormatter extends EmscriptenObject {
  static get(): InstructionSentenceFormatter;
  getAsFormattedText(instr: Instruction, metadata: InstructionMetadata): VectorPairStringTextFormatting;
}

export class ParameterOptions extends EmscriptenObject {
  setDescription(description: string): ParameterOptions;
  setTypeExtraInfo(typeExtraInfo: string): ParameterOptions;
  static makeNewOptions(): ParameterOptions;
}

export class AbstractFunctionMetadata extends EmscriptenObject {
  addParameter(type: string, description: string, optionalObjectType?: string, parameterIsOptional?: boolean): AbstractFunctionMetadata;
  addCodeOnlyParameter(type: string, supplementaryInformation: string): AbstractFunctionMetadata;
  setDefaultValue(defaultValue: string): AbstractFunctionMetadata;
  setParameterLongDescription(longDescription: string): AbstractFunctionMetadata;
  setParameterExtraInfo(extraInfo: string): AbstractFunctionMetadata;
  setHidden(): AbstractFunctionMetadata;
  setPrivate(): AbstractFunctionMetadata;
  setFunctionName(functionName: string): AbstractFunctionMetadata;
  setIncludeFile(includeFile: string): AbstractFunctionMetadata;
  addIncludeFile(includeFile: string): AbstractFunctionMetadata;
  getIncludeFiles(): VectorString;
}

export class InstructionMetadata extends AbstractFunctionMetadata {
  constructor();
  getFullName(): string;
  getDescription(): string;
  getSentence(): string;
  getGroup(): string;
  getIconFilename(): string;
  getSmallIconFilename(): string;
  getHelpPath(): string;
  canHaveSubInstructions(): boolean;
  getParameter(index: number): ParameterMetadata;
  getParametersCount(): number;
  getParameters(): ParameterMetadataContainer;
  getUsageComplexity(): number;
  isHidden(): boolean;
  isPrivate(): boolean;
  isAsync(): boolean;
  isOptionallyAsync(): boolean;
  isRelevantForLayoutEvents(): boolean;
  isRelevantForFunctionEvents(): boolean;
  isRelevantForAsynchronousFunctionEvents(): boolean;
  isRelevantForCustomObjectEvents(): boolean;
  setCanHaveSubInstructions(): InstructionMetadata;
  setHelpPath(helpPath: string): InstructionMetadata;
  setHidden(): InstructionMetadata;
  setPrivate(): InstructionMetadata;
  setRelevantForLayoutEventsOnly(): InstructionMetadata;
  setRelevantForFunctionEventsOnly(): InstructionMetadata;
  setRelevantForAsynchronousFunctionEventsOnly(): InstructionMetadata;
  setRelevantForCustomObjectEventsOnly(): InstructionMetadata;
  addParameter(type: string, description: string, optionalObjectType?: string, parameterIsOptional?: boolean): InstructionMetadata;
  addCodeOnlyParameter(type: string, supplementaryInformation: string): InstructionMetadata;
  setDefaultValue(defaultValue: string): InstructionMetadata;
  setParameterLongDescription(longDescription: string): InstructionMetadata;
  setParameterExtraInfo(extraInfo: string): InstructionMetadata;
  useStandardOperatorParameters(type: string, options: ParameterOptions): InstructionMetadata;
  useStandardRelationalOperatorParameters(type: string, options: ParameterOptions): InstructionMetadata;
  markAsSimple(): InstructionMetadata;
  markAsAdvanced(): InstructionMetadata;
  markAsComplex(): InstructionMetadata;
  getCodeExtraInformation(): InstructionMetadata;
  setFunctionName(functionName_: string): InstructionMetadata;
  setAsyncFunctionName(functionName_: string): InstructionMetadata;
  getFunctionName(): string;
  getAsyncFunctionName(): string;
  setManipulatedType(type_: string): InstructionMetadata;
  setGetter(getter: string): InstructionMetadata;
  setMutators(mutators: MapStringString): InstructionMetadata;
  setIncludeFile(includeFile: string): InstructionMetadata;
  addIncludeFile(includeFile: string): InstructionMetadata;
  getIncludeFiles(): VectorString;
}

export class ExpressionMetadata extends AbstractFunctionMetadata {
  constructor(returnType: string, extensionNamespace: string, name: string, fullname: string, description: string, group: string, smallicon: string);
  getReturnType(): string;
  getFullName(): string;
  getDescription(): string;
  getGroup(): string;
  getSmallIconFilename(): string;
  getHelpPath(): string;
  isShown(): boolean;
  isPrivate(): boolean;
  isRelevantForLayoutEvents(): boolean;
  isRelevantForFunctionEvents(): boolean;
  isRelevantForAsynchronousFunctionEvents(): boolean;
  isRelevantForCustomObjectEvents(): boolean;
  getParameter(id: number): ParameterMetadata;
  getParametersCount(): number;
  getParameters(): ParameterMetadataContainer;
  setHidden(): ExpressionMetadata;
  setPrivate(): ExpressionMetadata;
  setRelevantForLayoutEventsOnly(): ExpressionMetadata;
  setRelevantForFunctionEventsOnly(): ExpressionMetadata;
  setRelevantForAsynchronousFunctionEventsOnly(): ExpressionMetadata;
  setRelevantForCustomObjectEventsOnly(): ExpressionMetadata;
  addParameter(type: string, description: string, optionalObjectType?: string, parameterIsOptional?: boolean): ExpressionMetadata;
  addCodeOnlyParameter(type: string, supplementaryInformation: string): ExpressionMetadata;
  setDefaultValue(defaultValue: string): ExpressionMetadata;
  setParameterLongDescription(longDescription: string): ExpressionMetadata;
  setParameterExtraInfo(extraInfo: string): ExpressionMetadata;
  getCodeExtraInformation(): ExpressionMetadata;
  setFunctionName(functionName: string): ExpressionMetadata;
  getFunctionName(): string;
  setStatic(): ExpressionMetadata;
  setIncludeFile(includeFile: string): ExpressionMetadata;
  addIncludeFile(includeFile: string): ExpressionMetadata;
  getIncludeFiles(): VectorString;
}

export class MultipleInstructionMetadata extends AbstractFunctionMetadata {
  addParameter(type: string, description: string, optionalObjectType?: string, parameterIsOptional?: boolean): MultipleInstructionMetadata;
  addCodeOnlyParameter(type: string, supplementaryInformation: string): MultipleInstructionMetadata;
  setDefaultValue(defaultValue: string): MultipleInstructionMetadata;
  setParameterLongDescription(longDescription: string): MultipleInstructionMetadata;
  setParameterExtraInfo(extraInfo: string): MultipleInstructionMetadata;
  useStandardParameters(type: string, options: ParameterOptions): MultipleInstructionMetadata;
  setHidden(): MultipleInstructionMetadata;
  setFunctionName(functionName: string): MultipleInstructionMetadata;
  setGetter(getter: string): MultipleInstructionMetadata;
  setIncludeFile(includeFile: string): MultipleInstructionMetadata;
  addIncludeFile(includeFile: string): MultipleInstructionMetadata;
  getIncludeFiles(): VectorString;
  markAsSimple(): MultipleInstructionMetadata;
  markAsAdvanced(): MultipleInstructionMetadata;
  markAsComplex(): MultipleInstructionMetadata;
  setPrivate(): MultipleInstructionMetadata;
}

export class DependencyMetadata extends EmscriptenObject {
  constructor();
  getName(): string;
  setName(name_: string): DependencyMetadata;
  getExportName(): string;
  setExportName(exportName_: string): DependencyMetadata;
  getVersion(): string;
  setVersion(version_: string): DependencyMetadata;
  getDependencyType(): string;
  setDependencyType(dependencyType_: string): DependencyMetadata;
  setExtraSetting(settingName: string, settingValue: PropertyDescriptor): DependencyMetadata;
  getAllExtraSettings(): MapStringPropertyDescriptor;
  onlyIfSomeExtraSettingsNonEmpty(): DependencyMetadata;
  onlyIfOtherDependencyIsExported(otherDependency: string): DependencyMetadata;
  copyFrom(dependencyMetadata: DependencyMetadata): void;
}

export class SourceFileMetadata extends EmscriptenObject {
  constructor();
  getResourceName(): string;
  setResourceName(resourceName_: string): SourceFileMetadata;
  getIncludePosition(): string;
  setIncludePosition(includePosition_: string): SourceFileMetadata;
}

export class ParameterMetadata extends EmscriptenObject {
  constructor();
  getType(): string;
  setType(type_: string): ParameterMetadata;
  getName(): string;
  setName(name_: string): ParameterMetadata;
  getExtraInfo(): string;
  setExtraInfo(extraInfo_: string): ParameterMetadata;
  isOptional(): boolean;
  setOptional(optional_: boolean): ParameterMetadata;
  getDescription(): string;
  setDescription(description_: string): ParameterMetadata;
  getLongDescription(): string;
  setLongDescription(longDescription_: string): ParameterMetadata;
  isCodeOnly(): boolean;
  setCodeOnly(codeOnly_: boolean): ParameterMetadata;
  getDefaultValue(): string;
  setDefaultValue(defaultValue_: string): ParameterMetadata;
  setValueTypeMetadata(type: ValueTypeMetadata): ParameterMetadata;
  getValueTypeMetadata(): ValueTypeMetadata;
  static isObject(param: string): boolean;
  static isBehavior(param: string): boolean;
  static isExpression(type_: string, parameterType: string): boolean;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class ValueTypeMetadata extends EmscriptenObject {
  constructor();
  getName(): string;
  setName(name_: string): ValueTypeMetadata;
  getExtraInfo(): string;
  setExtraInfo(extraInfo_: string): ValueTypeMetadata;
  isOptional(): boolean;
  setOptional(optional_: boolean): ValueTypeMetadata;
  getDefaultValue(): string;
  setDefaultValue(defaultValue_: string): ValueTypeMetadata;
  isObject(): boolean;
  isBehavior(): boolean;
  isNumber(): boolean;
  isString(): boolean;
  isVariable(): boolean;
  static isTypeObject(parameterType: string): boolean;
  static isTypeBehavior(parameterType: string): boolean;
  static isTypeExpression(type: string, parameterType: string): boolean;
  static getPrimitiveValueType(parameterType: string): string;
  static convertPropertyTypeToValueType(propertyType: string): string;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class ParameterMetadataContainer extends EmscriptenObject {
  insertNewParameter(name: string, pos: number): ParameterMetadata;
  insertParameter(parameterMetadata: ParameterMetadata, pos: number): ParameterMetadata;
  hasParameterNamed(name: string): boolean;
  getParameter(name: string): ParameterMetadata;
  getParameterAt(pos: number): ParameterMetadata;
  removeParameter(name: string): void;
  moveParameter(oldIndex: number, newIndex: number): void;
  getParametersCount(): number;
  getParameterPosition(parameterMetadata: ParameterMetadata): number;
  clearParameters(): void;
  addNewParameter(name: string): ParameterMetadata;
}

export class ParameterMetadataTools extends EmscriptenObject {
  static parametersToObjectsContainer(project: Project, parameters: ParameterMetadataContainer, outputObjectsContainer: ObjectsContainer): void;
  static getObjectParameterIndexFor(parameters: ParameterMetadataContainer, parameterIndex: number): number;
}

export class ObjectMetadata extends EmscriptenObject {
  getName(): string;
  getFullName(): string;
  getDescription(): string;
  getIconFilename(): string;
  getHelpPath(): string;
  getCategoryFullName(): string;
  setCategoryFullName(categoryFullName: string): ObjectMetadata;
  addScopedCondition(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): InstructionMetadata;
  addScopedAction(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): InstructionMetadata;
  addCondition(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): InstructionMetadata;
  addAction(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): InstructionMetadata;
  addExpression(name: string, fullname: string, description: string, group: string, smallicon: string): ExpressionMetadata;
  addStrExpression(name: string, fullname: string, description: string, group: string, smallicon: string): ExpressionMetadata;
  addExpressionAndCondition(type: string, name: string, fullname: string, description: string, sentenceName: string, group: string, icon: string): MultipleInstructionMetadata;
  addExpressionAndConditionAndAction(type: string, name: string, fullname: string, description: string, sentenceName: string, group: string, icon: string): MultipleInstructionMetadata;
  getAllActions(): MapStringInstructionMetadata;
  getAllConditions(): MapStringInstructionMetadata;
  getAllExpressions(): MapStringExpressionMetadata;
  getAllStrExpressions(): MapStringExpressionMetadata;
  setIncludeFile(includeFile: string): ObjectMetadata;
  addIncludeFile(includeFile: string): ObjectMetadata;
  getDefaultBehaviors(): SetString;
  hasDefaultBehavior(behaviorType: string): boolean;
  addDefaultBehavior(behaviorType: string): ObjectMetadata;
  isPrivate(): boolean;
  setPrivate(): ObjectMetadata;
  setHidden(): ObjectMetadata;
  isHidden(): boolean;
  markAsRenderedIn3D(): ObjectMetadata;
  isRenderedIn3D(): boolean;
  setOpenFullEditorLabel(label: string): ObjectMetadata;
  getOpenFullEditorLabel(): string;
}

export class QuickCustomization extends EmscriptenObject {
  static Default = 0;
  static Visible = 1;
  static Hidden = 2;
}

export class QuickCustomizationVisibilitiesContainer extends EmscriptenObject {
  set(name: string, visibility: QuickCustomization_Visibility): void;
  get(name: string): QuickCustomization_Visibility;
}

export class Screenshot extends EmscriptenObject {
  getDelayTimeInSeconds(): number;
  setDelayTimeInSeconds(delayTimeInSeconds: number): void;
  getSignedUrl(): string;
  setSignedUrl(signedUrl: string): void;
  getPublicUrl(): string;
  setPublicUrl(publicUrl: string): void;
}

export class CaptureOptions extends EmscriptenObject {
  addScreenshot(screenshot: Screenshot): void;
  clearScreenshots(): void;
  getScreenshots(): VectorScreenshot;
}

export class BehaviorMetadata extends EmscriptenObject {
  getName(): string;
  getFullName(): string;
  getDefaultName(): string;
  getDescription(): string;
  getGroup(): string;
  getIconFilename(): string;
  getHelpPath(): string;
  addScopedCondition(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): InstructionMetadata;
  addScopedAction(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): InstructionMetadata;
  addCondition(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): InstructionMetadata;
  addAction(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): InstructionMetadata;
  addExpression(name: string, fullname: string, description: string, group: string, smallicon: string): ExpressionMetadata;
  addStrExpression(name: string, fullname: string, description: string, group: string, smallicon: string): ExpressionMetadata;
  addExpressionAndCondition(type: string, name: string, fullname: string, description: string, sentenceName: string, group: string, icon: string): MultipleInstructionMetadata;
  addExpressionAndConditionAndAction(type: string, name: string, fullname: string, description: string, sentenceName: string, group: string, icon: string): MultipleInstructionMetadata;
  addDuplicatedAction(newActionName: string, copiedActionName: string): InstructionMetadata;
  addDuplicatedCondition(newConditionName: string, copiedConditionName: string): InstructionMetadata;
  addDuplicatedExpression(newExpressionName: string, copiedExpressionName: string): ExpressionMetadata;
  addDuplicatedStrExpression(newExpressionName: string, copiedExpressionName: string): ExpressionMetadata;
  getAllActions(): MapStringInstructionMetadata;
  getAllConditions(): MapStringInstructionMetadata;
  getAllExpressions(): MapStringExpressionMetadata;
  getAllStrExpressions(): MapStringExpressionMetadata;
  setIncludeFile(includeFile: string): BehaviorMetadata;
  addIncludeFile(includeFile: string): BehaviorMetadata;
  addRequiredFile(resourceFile: string): BehaviorMetadata;
  setObjectType(objectType: string): BehaviorMetadata;
  getObjectType(): string;
  getRequiredBehaviorTypes(): VectorString;
  isPrivate(): boolean;
  setPrivate(): BehaviorMetadata;
  isHidden(): boolean;
  setHidden(): BehaviorMetadata;
  getQuickCustomizationVisibility(): QuickCustomization_Visibility;
  setQuickCustomizationVisibility(visibility: QuickCustomization_Visibility): BehaviorMetadata;
  setOpenFullEditorLabel(label: string): BehaviorMetadata;
  getOpenFullEditorLabel(): string;
  get(): Behavior;
  getSharedDataInstance(): BehaviorsSharedData;
  getProperties(): MapStringPropertyDescriptor;
  getSharedProperties(): MapStringPropertyDescriptor;
}

export class EffectMetadata extends EmscriptenObject {
  setFullName(fullName: string): EffectMetadata;
  setDescription(description: string): EffectMetadata;
  setHelpPath(helpPath: string): EffectMetadata;
  setIncludeFile(includeFile: string): EffectMetadata;
  addIncludeFile(includeFile: string): EffectMetadata;
  markAsNotWorkingForObjects(): EffectMetadata;
  markAsOnlyWorkingFor2D(): EffectMetadata;
  markAsOnlyWorkingFor3D(): EffectMetadata;
  markAsUnique(): EffectMetadata;
  getType(): string;
  getFullName(): string;
  getDescription(): string;
  getHelpPath(): string;
  isMarkedAsNotWorkingForObjects(): boolean;
  isMarkedAsOnlyWorkingFor2D(): boolean;
  isMarkedAsOnlyWorkingFor3D(): boolean;
  isMarkedAsUnique(): boolean;
  getProperties(): MapStringPropertyDescriptor;
}

export class EventMetadata extends EmscriptenObject {
  getFullName(): string;
  getDescription(): string;
  getGroup(): string;
}

export class PlatformExtension extends EmscriptenObject {
  constructor();
  setExtensionInformation(name: string, fullname: string, description: string, author: string, license: string): PlatformExtension;
  setExtensionHelpPath(helpPath: string): PlatformExtension;
  setIconUrl(iconUrl: string): PlatformExtension;
  setCategory(category: string): PlatformExtension;
  addInstructionOrExpressionGroupMetadata(name: string): InstructionOrExpressionGroupMetadata;
  markAsDeprecated(): void;
  getTags(): VectorString;
  setTags(csvTags: string): PlatformExtension;
  addExpressionAndCondition(type: string, name: string, fullname: string, description: string, sentenceName: string, group: string, icon: string): MultipleInstructionMetadata;
  addExpressionAndConditionAndAction(type: string, name: string, fullname: string, description: string, sentenceName: string, group: string, icon: string): MultipleInstructionMetadata;
  addCondition(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): InstructionMetadata;
  addAction(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): InstructionMetadata;
  addExpression(name: string, fullname: string, description: string, group: string, smallicon: string): ExpressionMetadata;
  addStrExpression(name: string, fullname: string, description: string, group: string, smallicon: string): ExpressionMetadata;
  addDependency(): DependencyMetadata;
  addBehavior(name: string, fullname: string, defaultName: string, description: string, group: string, icon24x24: string, className: string, instance: Behavior, sharedDatasInstance: BehaviorsSharedData): BehaviorMetadata;
  addObject(name: string, fullname: string, description: string, icon24x24: string, instance: ObjectConfiguration): ObjectMetadata;
  addEffect(name: string): EffectMetadata;
  registerProperty(name: string): PropertyDescriptor;
  getFullName(): string;
  getName(): string;
  getCategory(): string;
  getDescription(): string;
  getAuthor(): string;
  getLicense(): string;
  getHelpPath(): string;
  getIconUrl(): string;
  getNameSpace(): string;
  addDuplicatedAction(newActionName: string, copiedActionName: string): InstructionMetadata;
  addDuplicatedCondition(newConditionName: string, copiedConditionName: string): InstructionMetadata;
  addDuplicatedExpression(newExpressionName: string, copiedExpressionName: string): ExpressionMetadata;
  addDuplicatedStrExpression(newExpressionName: string, copiedExpressionName: string): ExpressionMetadata;
  getExtensionObjectsTypes(): VectorString;
  getBehaviorsTypes(): VectorString;
  getExtensionEffectTypes(): VectorString;
  getObjectMetadata(type: string): ObjectMetadata;
  getBehaviorMetadata(type: string): BehaviorMetadata;
  getEffectMetadata(type: string): EffectMetadata;
  getAllEvents(): MapStringEventMetadata;
  getAllActions(): MapStringInstructionMetadata;
  getAllConditions(): MapStringInstructionMetadata;
  getAllExpressions(): MapStringExpressionMetadata;
  getAllStrExpressions(): MapStringExpressionMetadata;
  getAllActionsForObject(objectType: string): MapStringInstructionMetadata;
  getAllConditionsForObject(objectType: string): MapStringInstructionMetadata;
  getAllExpressionsForObject(objectType: string): MapStringExpressionMetadata;
  getAllStrExpressionsForObject(objectType: string): MapStringExpressionMetadata;
  getAllActionsForBehavior(autoType: string): MapStringInstructionMetadata;
  getAllConditionsForBehavior(autoType: string): MapStringInstructionMetadata;
  getAllExpressionsForBehavior(autoType: string): MapStringExpressionMetadata;
  getAllStrExpressionsForBehavior(autoType: string): MapStringExpressionMetadata;
  getAllProperties(): MapStringPropertyDescriptor;
  getAllDependencies(): VectorDependencyMetadata;
  getAllSourceFiles(): VectorSourceFileMetadata;
  static getNamespaceSeparator(): string;
  static getBehaviorFullType(extensionName: string, behaviorName: string): string;
  static getObjectFullType(extensionName: string, objectName: string): string;
  static getExtensionFromFullObjectType(type: string): string;
  static getObjectNameFromFullObjectType(type: string): string;
}

export class EventsList extends EmscriptenObject {
  constructor();
  insertEvent(event: BaseEvent, pos: number): BaseEvent;
  insertNewEvent(project: Project, type: string, pos: number): BaseEvent;
  insertEvents(list: EventsList, begin: number, end: number, pos: number): void;
  getEventAt(pos: number): BaseEvent;
  removeEventAt(pos: number): void;
  removeEvent(event: BaseEvent): void;
  getEventsCount(): number;
  contains(event: BaseEvent, recursive: boolean): boolean;
  moveEventToAnotherEventsList(eventToMove: BaseEvent, newEventsList: EventsList, newPosition: number): boolean;
  isEmpty(): boolean;
  clear(): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
}

export class BaseEvent extends EmscriptenObject {
  constructor();
  clone(): BaseEvent;
  getType(): string;
  setType(type: string): void;
  isExecutable(): boolean;
  canHaveSubEvents(): boolean;
  hasSubEvents(): boolean;
  getSubEvents(): EventsList;
  canHaveVariables(): boolean;
  hasVariables(): boolean;
  getVariables(): VariablesContainer;
  isDisabled(): boolean;
  setDisabled(disable: boolean): void;
  isFolded(): boolean;
  setFolded(folded: boolean): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
}

export class StandardEvent extends BaseEvent {
  constructor();
  getConditions(): InstructionsList;
  getActions(): InstructionsList;
}

export class RepeatEvent extends BaseEvent {
  constructor();
  getConditions(): InstructionsList;
  getActions(): InstructionsList;
  setRepeatExpressionPlainString(expr: string): void;
  getRepeatExpression(): Expression;
}

export class WhileEvent extends BaseEvent {
  constructor();
  getConditions(): InstructionsList;
  getWhileConditions(): InstructionsList;
  getActions(): InstructionsList;
}

export class ForEachEvent extends BaseEvent {
  constructor();
  setObjectToPick(objects: string): void;
  getObjectToPick(): string;
  getConditions(): InstructionsList;
  getActions(): InstructionsList;
}

export class ForEachChildVariableEvent extends BaseEvent {
  constructor();
  getConditions(): InstructionsList;
  getActions(): InstructionsList;
  getIterableVariableName(): string;
  getKeyIteratorVariableName(): string;
  getValueIteratorVariableName(): string;
  setIterableVariableName(newName: string): void;
  setKeyIteratorVariableName(newName: string): void;
  setValueIteratorVariableName(newName: string): void;
}

export class CommentEvent extends BaseEvent {
  constructor();
  getComment(): string;
  setComment(type: string): void;
  setBackgroundColor(r: number, g: number, b: number): void;
  getBackgroundColorRed(): number;
  getBackgroundColorGreen(): number;
  getBackgroundColorBlue(): number;
  setTextColor(r: number, g: number, b: number): void;
  getTextColorRed(): number;
  getTextColorGreen(): number;
  getTextColorBlue(): number;
}

export class GroupEvent extends BaseEvent {
  constructor();
  setName(name: string): void;
  getName(): string;
  setBackgroundColor(r: number, g: number, b: number): void;
  getBackgroundColorR(): number;
  getBackgroundColorG(): number;
  getBackgroundColorB(): number;
  setSource(source: string): void;
  getSource(): string;
  getCreationParameters(): VectorString;
  getCreationTimestamp(): number;
  setCreationTimestamp(ts: number): void;
}

export class LinkEvent extends BaseEvent {
  constructor();
  setTarget(name: string): void;
  getTarget(): string;
  getIncludeConfig(): number;
  setIncludeAllEvents(): void;
  setIncludeEventsGroup(source: string): void;
  getEventsGroupName(): string;
  setIncludeStartAndEnd(start: number, end: number): void;
  getIncludeStart(): number;
  getIncludeEnd(): number;
}

export class EventsRemover extends EmscriptenObject {
  constructor();
  addEventToRemove(eventToRemove: BaseEvent): void;
  addInstructionToRemove(instructionToRemove: Instruction): void;
  launch(events: EventsList): void;
}

export class EventsListUnfolder extends EmscriptenObject {
  static unfoldWhenContaining(list: EventsList, eventToContain: BaseEvent): void;
  static foldAll(list: EventsList): void;
  static unfoldToLevel(list: EventsList, maxLevel: number, currentLevel?: number): void;
}

export class EventsSearchResult extends EmscriptenObject {
  isEventsListValid(): boolean;
  getEventsList(): EventsList;
  getPositionInList(): number;
  isEventValid(): boolean;
  getEvent(): BaseEvent;
}

export class VectorEventsSearchResult extends EmscriptenObject {
  constructor();
  vectorEventsSearchResult(): VectorEventsSearchResult;
  push_back(result: EventsSearchResult): void;
  resize(size: number): void;
  size(): number;
  at(index: number): EventsSearchResult;
  set(index: number, result: EventsSearchResult): void;
  clear(): void;
}

export class EventsRefactorer extends EmscriptenObject {
  static renameObjectInEvents(platform: Platform, projectScopedContainers: ProjectScopedContainers, events: EventsList, targetedObjectsContainer: ObjectsContainer, oldName: string, newName: string): void;
  static replaceStringInEvents(project: ObjectsContainer, layout: ObjectsContainer, events: EventsList, toReplace: string, newString: string, matchCase: boolean, inConditions: boolean, inActions: boolean, inEventStrings: boolean): VectorEventsSearchResult;
  static searchInEvents(platform: Platform, events: EventsList, search: string, matchCase: boolean, inConditions: boolean, inActions: boolean, inEventStrings: boolean, inEventSentences: boolean): VectorEventsSearchResult;
}

export class UnfilledRequiredBehaviorPropertyProblem extends EmscriptenObject {
  getSourceProject(): Project;
  getSourceObject(): gdObject;
  getSourceBehaviorContent(): Behavior;
  getSourcePropertyName(): string;
  getExpectedBehaviorTypeName(): string;
}

export class VectorUnfilledRequiredBehaviorPropertyProblem extends EmscriptenObject {
  size(): number;
  at(index: number): UnfilledRequiredBehaviorPropertyProblem;
}

export class ProjectBrowserHelper extends EmscriptenObject {
  static exposeProjectEvents(project: Project, worker: ArbitraryEventsWorker): void;
  static exposeProjectObjects(project: Project, worker: ArbitraryObjectsWorker): void;
}

export class ResourceExposer extends EmscriptenObject {
  static exposeWholeProjectResources(project: Project, worker: ArbitraryResourceWorker): void;
}

export class VariablesChangeset extends EmscriptenObject {
  hasRemovedVariables(): boolean;
  clearRemovedVariables(): VariablesChangeset;
}

export class WholeProjectRefactorer extends EmscriptenObject {
  static computeChangesetForVariablesContainer(oldSerializedVariablesContainer: SerializerElement, newVariablesContainer: VariablesContainer): VariablesChangeset;
  static applyRefactoringForVariablesContainer(project: Project, newVariablesContainer: VariablesContainer, changeset: VariablesChangeset, originalSerializedVariables: SerializerElement): void;
  static applyRefactoringForObjectVariablesContainer(project: Project, objectVariablesContainer: VariablesContainer, initialInstancesContainer: InitialInstancesContainer, objectName: string, changeset: VariablesChangeset, originalSerializedVariables: SerializerElement): void;
  static applyRefactoringForGroupVariablesContainer(project: Project, globalObjectsContainer: ObjectsContainer, objectsContainer: ObjectsContainer, initialInstancesContainer: InitialInstancesContainer, groupVariablesContainer: VariablesContainer, objectGroup: ObjectGroup, changeset: VariablesChangeset, originalSerializedVariables: SerializerElement): void;
  static renameEventsFunctionsExtension(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, oldName: string, newName: string): void;
  static updateExtensionNameInEventsBasedBehavior(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, sourceExtensionName: string): void;
  static updateExtensionNameInEventsBasedObject(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, sourceExtensionName: string): void;
  static renameEventsFunction(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, oldName: string, newName: string): void;
  static renameBehaviorEventsFunction(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, oldName: string, newName: string): void;
  static renameObjectEventsFunction(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, oldName: string, newName: string): void;
  static renameParameter(project: Project, projectScopedContainers: ProjectScopedContainers, eventsFunction: EventsFunction, parameterObjectsContainer: ObjectsContainer, oldName: string, newName: string): void;
  static changeParameterType(project: Project, projectScopedContainers: ProjectScopedContainers, eventsFunction: EventsFunction, parameterObjectsContainer: ObjectsContainer, parameterName: string): void;
  static moveEventsFunctionParameter(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, functionName: string, oldIndex: number, newIndex: number): void;
  static moveBehaviorEventsFunctionParameter(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, functionName: string, oldIndex: number, newIndex: number): void;
  static moveObjectEventsFunctionParameter(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, functionName: string, oldIndex: number, newIndex: number): void;
  static renameEventsBasedBehaviorProperty(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, oldName: string, newName: string): void;
  static renameEventsBasedBehaviorSharedProperty(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, oldName: string, newName: string): void;
  static changeEventsBasedBehaviorPropertyType(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, propertyName: string): void;
  static renameEventsBasedObjectProperty(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, oldName: string, newName: string): void;
  static changeEventsBasedObjectPropertyType(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, propertyName: string): void;
  static renameEventsBasedBehavior(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, oldName: string, newName: string): void;
  static updateBehaviorNameInEventsBasedBehavior(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, sourceBehaviorName: string): void;
  static renameEventsBasedObject(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, oldName: string, newName: string): void;
  static updateObjectNameInEventsBasedObject(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, sourceObjectName: string): void;
  static renameLayout(project: Project, oldName: string, newName: string): void;
  static renameExternalLayout(project: Project, oldName: string, newName: string): void;
  static renameExternalEvents(project: Project, oldName: string, newName: string): void;
  static renameLayerInScene(project: Project, scene: Layout, oldName: string, newName: string): void;
  static renameLayerInEventsBasedObject(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, oldName: string, newName: string): void;
  static renameLayerEffectInScene(project: Project, scene: Layout, layer: Layer, oldName: string, newName: string): void;
  static renameLayerEffectInEventsBasedObject(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, layer: Layer, oldName: string, newName: string): void;
  static renameObjectAnimationInScene(project: Project, scene: Layout, gdObject: gdObject, oldName: string, newName: string): void;
  static renameObjectAnimationInEventsBasedObject(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, gdObject: gdObject, oldName: string, newName: string): void;
  static renameObjectPointInScene(project: Project, scene: Layout, gdObject: gdObject, oldName: string, newName: string): void;
  static renameObjectPointInEventsBasedObject(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, gdObject: gdObject, oldName: string, newName: string): void;
  static renameObjectEffectInScene(project: Project, scene: Layout, gdObject: gdObject, oldName: string, newName: string): void;
  static renameObjectEffectInEventsBasedObject(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, gdObject: gdObject, oldName: string, newName: string): void;
  static objectOrGroupRenamedInScene(project: Project, scene: Layout, oldName: string, newName: string, isObjectGroup: boolean): void;
  static objectRemovedInScene(project: Project, scene: Layout, objectName: string): void;
  static behaviorsAddedToObjectInScene(project: Project, scene: Layout, objectName: string): void;
  static objectOrGroupRenamedInEventsFunction(project: Project, projectScopedContainers: ProjectScopedContainers, eventsFunction: EventsFunction, parameterObjectsContainer: ObjectsContainer, oldName: string, newName: string, isObjectGroup: boolean): void;
  static objectRemovedInEventsFunction(project: Project, eventsFunction: EventsFunction, objectName: string): void;
  static objectOrGroupRenamedInEventsBasedObject(project: Project, projectScopedContainers: ProjectScopedContainers, eventsBasedObject: EventsBasedObject, oldName: string, newName: string, isObjectGroup: boolean): void;
  static objectRemovedInEventsBasedObject(project: Project, eventsBasedObject: EventsBasedObject, objectName: string): void;
  static globalObjectOrGroupRenamed(project: Project, oldName: string, newName: string, isObjectGroup: boolean): void;
  static globalObjectRemoved(project: Project, objectName: string): void;
  static behaviorsAddedToGlobalObject(project: Project, objectName: string): void;
  static getAllObjectTypesUsingEventsBasedBehavior(project: Project, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior): SetString;
  static ensureBehaviorEventsFunctionsProperParameters(eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior): void;
  static ensureObjectEventsFunctionsProperParameters(eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject): void;
  static addBehaviorAndRequiredBehaviors(project: Project, obj: gdObject, behaviorType: string, behaviorName: string): void;
  static addRequiredBehaviorsFor(project: Project, obj: gdObject, behaviorName: string): void;
  static findDependentBehaviorNames(project: Project, obj: gdObject, behaviorName: string): VectorString;
  static findInvalidRequiredBehaviorProperties(project: Project): VectorUnfilledRequiredBehaviorPropertyProblem;
  static getBehaviorsWithType(obj: gdObject, type: string): VectorString;
  static fixInvalidRequiredBehaviorProperties(project: Project): boolean;
  static removeLayerInScene(project: Project, scene: Layout, layerName: string): void;
  static mergeLayersInScene(project: Project, scene: Layout, originLayerName: string, targetLayerName: string): void;
  static removeLayerInEventsBasedObject(eventsBasedObject: EventsBasedObject, layerName: string): void;
  static mergeLayersInEventsBasedObject(eventsBasedObject: EventsBasedObject, originLayerName: string, targetLayerName: string): void;
  static getLayoutAndExternalLayoutLayerInstancesCount(project: Project, layout: Layout, layerName: string): number;
  static renameLeaderboards(project: Project, leaderboardIdMap: MapStringString): void;
  static findAllLeaderboardIds(project: Project): SetString;
}

export class ObjectTools extends EmscriptenObject {
  static isBehaviorCompatibleWithObject(platform: Platform, objectType: string, behaviorType: string): boolean;
}

export class EventsBasedObjectDependencyFinder extends EmscriptenObject {
  static isDependentFromEventsBasedObject(project: Project, eventsBasedObject: EventsBasedObject, dependency: EventsBasedObject): boolean;
}

export class PropertyFunctionGenerator extends EmscriptenObject {
  static generateBehaviorGetterAndSetter(project: Project, extension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, property: NamedPropertyDescriptor, isSharedProperties: boolean): void;
  static generateObjectGetterAndSetter(project: Project, extension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, property: NamedPropertyDescriptor): void;
  static canGenerateGetterAndSetter(eventsBasedBehavior: AbstractEventsBasedEntity, property: NamedPropertyDescriptor): boolean;
  static generateConditionSkeleton(project: Project, eventFunction: EventsFunction): void;
}

export class UsedExtensionsResult extends EmscriptenObject {
  getUsedExtensions(): SetString;
}

export class UsedExtensionsFinder extends EmscriptenObject {
  static scanProject(project: Project): UsedExtensionsResult;
}

export class ExampleExtensionUsagesFinder extends EmscriptenObject {
  static getUsedExtensions(project: Project): SetString;
}

export class InstructionsCountEvaluator extends EmscriptenObject {
  static scanProject(project: Project): number;
}

export class ExtensionAndBehaviorMetadata extends EmscriptenObject {
  getExtension(): PlatformExtension;
  getMetadata(): BehaviorMetadata;
}

export class ExtensionAndObjectMetadata extends EmscriptenObject {
  getExtension(): PlatformExtension;
  getMetadata(): ObjectMetadata;
}

export class ExtensionAndEffectMetadata extends EmscriptenObject {
  getExtension(): PlatformExtension;
  getMetadata(): EffectMetadata;
}

export class ExtensionAndInstructionMetadata extends EmscriptenObject {
  getExtension(): PlatformExtension;
  getMetadata(): InstructionMetadata;
}

export class ExtensionAndExpressionMetadata extends EmscriptenObject {
  getExtension(): PlatformExtension;
  getMetadata(): ExpressionMetadata;
}

export class MetadataProvider extends EmscriptenObject {
  static getExtensionAndBehaviorMetadata(p: Platform, type: string): ExtensionAndBehaviorMetadata;
  static getExtensionAndObjectMetadata(p: Platform, type: string): ExtensionAndObjectMetadata;
  static getExtensionAndEffectMetadata(p: Platform, type: string): ExtensionAndEffectMetadata;
  static getExtensionAndActionMetadata(p: Platform, type: string): ExtensionAndInstructionMetadata;
  static getExtensionAndConditionMetadata(p: Platform, type: string): ExtensionAndInstructionMetadata;
  static getExtensionAndExpressionMetadata(p: Platform, type: string): ExtensionAndExpressionMetadata;
  static getExtensionAndObjectExpressionMetadata(p: Platform, objectType: string, type: string): ExtensionAndExpressionMetadata;
  static getExtensionAndBehaviorExpressionMetadata(p: Platform, autoType: string, type: string): ExtensionAndExpressionMetadata;
  static getExtensionAndStrExpressionMetadata(p: Platform, type: string): ExtensionAndExpressionMetadata;
  static getExtensionAndObjectStrExpressionMetadata(p: Platform, objectType: string, type: string): ExtensionAndExpressionMetadata;
  static getExtensionAndBehaviorStrExpressionMetadata(p: Platform, autoType: string, type: string): ExtensionAndExpressionMetadata;
  static getBehaviorMetadata(p: Platform, type: string): BehaviorMetadata;
  static getObjectMetadata(p: Platform, type: string): ObjectMetadata;
  static getEffectMetadata(p: Platform, type: string): EffectMetadata;
  static getActionMetadata(p: Platform, type: string): InstructionMetadata;
  static getConditionMetadata(p: Platform, type: string): InstructionMetadata;
  static getExpressionMetadata(p: Platform, type: string): ExpressionMetadata;
  static getObjectExpressionMetadata(p: Platform, objectType: string, type: string): ExpressionMetadata;
  static getBehaviorExpressionMetadata(p: Platform, autoType: string, type: string): ExpressionMetadata;
  static getStrExpressionMetadata(p: Platform, type: string): ExpressionMetadata;
  static getObjectStrExpressionMetadata(p: Platform, objectType: string, type: string): ExpressionMetadata;
  static getBehaviorStrExpressionMetadata(p: Platform, autoType: string, type: string): ExpressionMetadata;
  static isBadExpressionMetadata(metadata: ExpressionMetadata): boolean;
  static isBadInstructionMetadata(metadata: InstructionMetadata): boolean;
  static isBadBehaviorMetadata(metadata: BehaviorMetadata): boolean;
  static isBadObjectMetadata(metadata: ObjectMetadata): boolean;
}

export class ProjectDiagnostic extends EmscriptenObject {
  getType(): ProjectDiagnostic_ErrorType;
  getMessage(): string;
  getActualValue(): string;
  getExpectedValue(): string;
  getObjectName(): string;
}

export class DiagnosticReport extends EmscriptenObject {
  constructor();
  get(index: number): ProjectDiagnostic;
  count(): number;
  getSceneName(): string;
}

export class WholeProjectDiagnosticReport extends EmscriptenObject {
  get(index: number): DiagnosticReport;
  count(): number;
  hasAnyIssue(): boolean;
}

export class ExpressionParserError extends EmscriptenObject {
  getMessage(): string;
  getStartPosition(): number;
  getEndPosition(): number;
}

export class VectorExpressionParserError extends EmscriptenObject {
  size(): number;
  at(index: number): ExpressionParserError;
}

export class ExpressionParser2NodeWorker extends EmscriptenObject {}

export class ExpressionValidator extends EmscriptenObject {
  constructor(platform: Platform, projectScopedContainers: ProjectScopedContainers, rootType: string, extraInfo: string);
  getAllErrors(): VectorExpressionParserError;
  getFatalErrors(): VectorExpressionParserError;
}

export class ExpressionCompletionDescription extends EmscriptenObject {
  getCompletionKind(): ExpressionCompletionDescription_CompletionKind;
  getType(): string;
  getVariableType(): Variable_Type;
  getVariableScope(): VariablesContainer_SourceType;
  getPrefix(): string;
  getCompletion(): string;
  getObjectName(): string;
  getBehaviorName(): string;
  isExact(): boolean;
  isLastParameter(): boolean;
  getReplacementStartPosition(): number;
  getReplacementEndPosition(): number;
  getParameterMetadata(): ParameterMetadata;
  hasObjectConfiguration(): boolean;
  getObjectConfiguration(): ObjectConfiguration;
  toString(): string;
}

export class VectorExpressionCompletionDescription extends EmscriptenObject {
  size(): number;
  at(index: number): ExpressionCompletionDescription;
}

export class ExpressionCompletionFinder extends EmscriptenObject {
  static getCompletionDescriptionsFor(platform: Platform, projectScopedContainers: ProjectScopedContainers, rootType: string, node: ExpressionNode, location: number): VectorExpressionCompletionDescription;
  getCompletionDescriptions(): VectorExpressionCompletionDescription;
}

export class ExpressionNodeLocationFinder extends EmscriptenObject {
  static getNodeAtPosition(node: ExpressionNode, searchedPosition: number): ExpressionNode;
}

export class ExpressionTypeFinder extends EmscriptenObject {
  static getType(platform: Platform, projectScopedContainers: ProjectScopedContainers, rootType: string, node: ExpressionNode): string;
}

export class ExpressionNode extends EmscriptenObject {
  visit(worker: ExpressionParser2NodeWorker): void;
}

export class UniquePtrExpressionNode extends EmscriptenObject {
  get(): ExpressionNode;
}

export class ExpressionParser2 extends EmscriptenObject {
  constructor();
  parseExpression(expression: string): UniquePtrExpressionNode;
}

export class EventsFunction extends EmscriptenObject {
  constructor();
  clone(): EventsFunction;
  setDescription(description: string): EventsFunction;
  getDescription(): string;
  setName(name: string): EventsFunction;
  getName(): string;
  setFullName(fullName: string): EventsFunction;
  getFullName(): string;
  setSentence(sentence: string): EventsFunction;
  getSentence(): string;
  setGroup(group: string): EventsFunction;
  getGroup(): string;
  setGetterName(group: string): EventsFunction;
  getGetterName(): string;
  setExpressionType(type: ValueTypeMetadata): EventsFunction;
  getExpressionType(): ValueTypeMetadata;
  setPrivate(isPrivate: boolean): EventsFunction;
  isPrivate(): boolean;
  setAsync(isAsync: boolean): EventsFunction;
  isAsync(): boolean;
  isAction(): boolean;
  isExpression(): boolean;
  isCondition(): boolean;
  setFunctionType(type: EventsFunction_FunctionType): EventsFunction;
  getFunctionType(): EventsFunction_FunctionType;
  getEvents(): EventsList;
  getParameters(): ParameterMetadataContainer;
  getParametersForEvents(functionsContainer: EventsFunctionsContainer): ParameterMetadataContainer;
  getObjectGroups(): ObjectGroupsContainer;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
}

export class EventsFunctionsContainer extends EmscriptenObject {
  insertNewEventsFunction(name: string, pos: number): EventsFunction;
  insertEventsFunction(eventsFunction: EventsFunction, pos: number): EventsFunction;
  hasEventsFunctionNamed(name: string): boolean;
  getEventsFunction(name: string): EventsFunction;
  getEventsFunctionAt(pos: number): EventsFunction;
  removeEventsFunction(name: string): void;
  moveEventsFunction(oldIndex: number, newIndex: number): void;
  getEventsFunctionsCount(): number;
  getEventsFunctionPosition(eventsFunction: EventsFunction): number;
}

export class AbstractEventsBasedEntity extends EmscriptenObject {
  getEventsFunctions(): EventsFunctionsContainer;
  getPropertyDescriptors(): PropertiesContainer;
  getName(): string;
  getFullName(): string;
  getDescription(): string;
  isPrivate(): boolean;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
}

export class EventsBasedBehavior extends AbstractEventsBasedEntity {
  constructor();
  setName(name: string): EventsBasedBehavior;
  setFullName(fullName: string): EventsBasedBehavior;
  setDescription(description: string): EventsBasedBehavior;
  setPrivate(isPrivate: boolean): EventsBasedBehavior;
  setObjectType(fullName: string): EventsBasedBehavior;
  getObjectType(): string;
  setQuickCustomizationVisibility(visibility: QuickCustomization_Visibility): EventsBasedBehavior;
  getQuickCustomizationVisibility(): QuickCustomization_Visibility;
  getSharedPropertyDescriptors(): PropertiesContainer;
  static getPropertyActionName(propertyName: string): string;
  static getPropertyConditionName(propertyName: string): string;
  static getPropertyExpressionName(propertyName: string): string;
  static getPropertyToggleActionName(propertyName: string): string;
  static getSharedPropertyActionName(propertyName: string): string;
  static getSharedPropertyConditionName(propertyName: string): string;
  static getSharedPropertyExpressionName(propertyName: string): string;
  static getSharedPropertyToggleActionName(propertyName: string): string;
}

export class EventsBasedBehaviorsList extends EmscriptenObject {
  insertNew(name: string, pos: number): EventsBasedBehavior;
  insert(item: EventsBasedBehavior, pos: number): EventsBasedBehavior;
  has(name: string): boolean;
  get(name: string): EventsBasedBehavior;
  getAt(pos: number): EventsBasedBehavior;
  remove(name: string): void;
  move(oldIndex: number, newIndex: number): void;
  getCount(): number;
  getPosition(item: EventsBasedBehavior): number;
  size(): number;
  at(index: number): EventsBasedBehavior;
}

export class EventsBasedObject extends AbstractEventsBasedEntity {
  constructor();
  setName(name: string): EventsBasedObject;
  setFullName(fullName: string): EventsBasedObject;
  setDescription(description: string): EventsBasedObject;
  setPrivate(isPrivate: boolean): EventsBasedObject;
  setDefaultName(defaultName: string): EventsBasedObject;
  getDefaultName(): string;
  markAsRenderedIn3D(isRenderedIn3D: boolean): EventsBasedObject;
  isRenderedIn3D(): boolean;
  markAsAnimatable(isAnimatable: boolean): EventsBasedObject;
  isAnimatable(): boolean;
  markAsTextContainer(isTextContainer: boolean): EventsBasedObject;
  isTextContainer(): boolean;
  markAsInnerAreaFollowingParentSize(value: boolean): EventsBasedObject;
  isInnerAreaFollowingParentSize(): boolean;
  makAsUsingLegacyInstancesRenderer(value: boolean): EventsBasedObject;
  isUsingLegacyInstancesRenderer(): boolean;
  getDefaultVariant(): EventsBasedObjectVariant;
  getVariants(): EventsBasedObjectVariantsContainer;
  getInitialInstances(): InitialInstancesContainer;
  getLayers(): LayersContainer;
  getObjects(): ObjectsContainer;
  getAreaMinX(): number;
  getAreaMinY(): number;
  getAreaMinZ(): number;
  getAreaMaxX(): number;
  getAreaMaxY(): number;
  getAreaMaxZ(): number;
  setAreaMinX(value: number): void;
  setAreaMinY(value: number): void;
  setAreaMinZ(value: number): void;
  setAreaMaxX(value: number): void;
  setAreaMaxY(value: number): void;
  setAreaMaxZ(value: number): void;
  static getPropertyActionName(propertyName: string): string;
  static getPropertyConditionName(propertyName: string): string;
  static getPropertyExpressionName(propertyName: string): string;
  static getPropertyToggleActionName(propertyName: string): string;
}

export class EventsBasedObjectVariant extends EmscriptenObject {
  constructor();
  getName(): string;
  setName(name: string): EventsBasedObjectVariant;
  getInitialInstances(): InitialInstancesContainer;
  getLayers(): LayersContainer;
  getObjects(): ObjectsContainer;
  getAreaMinX(): number;
  getAreaMinY(): number;
  getAreaMinZ(): number;
  getAreaMaxX(): number;
  getAreaMaxY(): number;
  getAreaMaxZ(): number;
  setAreaMinX(value: number): void;
  setAreaMinY(value: number): void;
  setAreaMinZ(value: number): void;
  setAreaMaxX(value: number): void;
  setAreaMaxY(value: number): void;
  setAreaMaxZ(value: number): void;
  setAssetStoreAssetId(assetStoreAssetId: string): void;
  getAssetStoreAssetId(): string;
  setAssetStoreOriginalName(assetStoreOriginalName: string): void;
  getAssetStoreOriginalName(): string;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
}

export class EventsBasedObjectVariantsContainer extends EmscriptenObject {
  insertNewVariant(name: string, pos: number): EventsBasedObjectVariant;
  insertVariant(variant: EventsBasedObjectVariant, pos: number): EventsBasedObjectVariant;
  hasVariantNamed(name: string): boolean;
  getVariant(name: string): EventsBasedObjectVariant;
  getVariantAt(pos: number): EventsBasedObjectVariant;
  removeVariant(name: string): void;
  moveVariant(oldIndex: number, newIndex: number): void;
  getVariantsCount(): number;
  getVariantPosition(variant: EventsBasedObjectVariant): number;
}

export class EventsBasedObjectsList extends EmscriptenObject {
  insertNew(name: string, pos: number): EventsBasedObject;
  insert(item: EventsBasedObject, pos: number): EventsBasedObject;
  has(name: string): boolean;
  get(name: string): EventsBasedObject;
  getAt(pos: number): EventsBasedObject;
  remove(name: string): void;
  move(oldIndex: number, newIndex: number): void;
  getCount(): number;
  getPosition(item: EventsBasedObject): number;
  size(): number;
  at(index: number): EventsBasedObject;
}

export class PropertiesContainer extends EmscriptenObject {
  constructor(owner: EventsFunctionsContainer_FunctionOwner);
  insertNew(name: string, pos: number): NamedPropertyDescriptor;
  insert(item: NamedPropertyDescriptor, pos: number): NamedPropertyDescriptor;
  has(name: string): boolean;
  get(name: string): NamedPropertyDescriptor;
  getAt(pos: number): NamedPropertyDescriptor;
  remove(name: string): void;
  move(oldIndex: number, newIndex: number): void;
  getCount(): number;
  getPosition(item: NamedPropertyDescriptor): number;
  size(): number;
  at(index: number): NamedPropertyDescriptor;
}

export class EventsFunctionsExtension extends EmscriptenObject {
  constructor();
  setNamespace(namespace_: string): EventsFunctionsExtension;
  getNamespace(): string;
  setVersion(version: string): EventsFunctionsExtension;
  getVersion(): string;
  setShortDescription(shortDescription: string): EventsFunctionsExtension;
  getShortDescription(): string;
  setDescription(description: string): EventsFunctionsExtension;
  getDescription(): string;
  setName(name: string): EventsFunctionsExtension;
  getName(): string;
  setFullName(fullName: string): EventsFunctionsExtension;
  getFullName(): string;
  setCategory(category: string): EventsFunctionsExtension;
  getCategory(): string;
  getTags(): VectorString;
  getAuthorIds(): VectorString;
  setAuthor(author: string): EventsFunctionsExtension;
  getAuthor(): string;
  setPreviewIconUrl(previewIconUrl: string): EventsFunctionsExtension;
  getPreviewIconUrl(): string;
  setIconUrl(iconUrl: string): EventsFunctionsExtension;
  getIconUrl(): string;
  setHelpPath(helpPath: string): EventsFunctionsExtension;
  getHelpPath(): string;
  setOrigin(originName: string, originIdentifier: string): void;
  getOriginName(): string;
  getOriginIdentifier(): string;
  addDependency(): DependencyMetadata;
  removeDependencyAt(index: number): void;
  getAllDependencies(): VectorDependencyMetadata;
  addSourceFile(): SourceFileMetadata;
  removeSourceFileAt(index: number): void;
  getAllSourceFiles(): VectorSourceFileMetadata;
  getEventsFunctions(): EventsFunctionsContainer;
  getGlobalVariables(): VariablesContainer;
  getSceneVariables(): VariablesContainer;
  getEventsBasedBehaviors(): EventsBasedBehaviorsList;
  getEventsBasedObjects(): EventsBasedObjectsList;
  serializeTo(element: SerializerElement): void;
  serializeToExternal(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
  static isExtensionLifecycleEventsFunction(eventsFunctionName: string): boolean;
}

export class AbstractFileSystem extends EmscriptenObject {}

export class AbstractFileSystemJS extends AbstractFileSystem {
  constructor();
  mkDir(dir: string): void;
  dirExists(dir: string): void;
  clearDir(dir: string): void;
  getTempDir(): string;
  fileNameFrom(dir: string): string;
  dirNameFrom(dir: string): string;
  isAbsolute(fn: string): boolean;
  copyFile(src: string, dest: string): void;
  writeToFile(fn: string, content: string): void;
  readFile(fn: string): string;
  readDir(dir: string): VectorString;
  fileExists(fn: string): boolean;
}

export class ProjectResourcesAdder extends EmscriptenObject {
  static getAllUseless(project: Project, resourceType: string): VectorString;
  static removeAllUseless(project: Project, resourceType: string): void;
}

export class ArbitraryEventsWorker extends EmscriptenObject {
  launch(events: EventsList): void;
}

export class ArbitraryObjectsWorker extends EmscriptenObject {
  launch(container: ObjectsContainer): void;
}

export class EventsParametersLister extends EmscriptenObject {
  constructor(project: Project);
  getParametersAndTypes(): MapStringString;
  launch(events: EventsList): void;
}

export class EventsPositionFinder extends EmscriptenObject {
  constructor();
  getPositions(): VectorInt;
  addEventToSearch(event: BaseEvent): void;
  launch(events: EventsList): void;
}

export class EventsTypesLister extends EmscriptenObject {
  constructor(project: Project);
  getAllEventsTypes(): VectorString;
  getAllConditionsTypes(): VectorString;
  getAllActionsTypes(): VectorString;
  launch(events: EventsList): void;
}

export class InstructionsTypeRenamer extends EmscriptenObject {
  constructor(project: Project, oldType: string, newType: string);
  launch(events: EventsList): void;
}

export class EventsContext extends EmscriptenObject {
  getReferencedObjectOrGroupNames(): SetString;
  getObjectNames(): SetString;
  getBehaviorNamesOfObjectOrGroup(objectOrGroupName: string): SetString;
}

export class EventsContextAnalyzer extends EmscriptenObject {
  constructor(platform: Platform);
  getEventsContext(): EventsContext;
  launch(events: EventsList, projectScopedContainers: ProjectScopedContainers): void;
}

export class ArbitraryResourceWorker extends EmscriptenObject {}

export class ArbitraryResourceWorkerJS extends ArbitraryResourceWorker {
  constructor(resourcesManager: ResourcesManager);
  exposeImage(image: string): void;
  exposeShader(shader: string): void;
  exposeFile(file: string): void;
}

export class ResourcesMergingHelper extends ArbitraryResourceWorker {
  constructor(resourcesManager: ResourcesManager, fs: AbstractFileSystem);
  setBaseDirectory(basePath: string): void;
  getAllResourcesOldAndNewFilename(): MapStringString;
}

export class ResourcesRenamer extends ArbitraryResourceWorker {
  constructor(resourcesManager: ResourcesManager, oldToNewNames: MapStringString);
}

export class ProjectResourcesCopier extends EmscriptenObject {
  static copyAllResourcesTo(project: Project, fs: AbstractFileSystem, destinationDirectory: string, updateOriginalProject: boolean, preserveAbsoluteFilenames: boolean, preserveDirectoryStructure: boolean): boolean;
}

export class ObjectsUsingResourceCollector extends EmscriptenObject {
  constructor(resourcesManager: ResourcesManager, resourceName: string);
  getObjectNames(): VectorString;
  launch(container: ObjectsContainer): void;
}

export class ResourcesInUseHelper extends ArbitraryResourceWorker {
  constructor(resourcesManager: ResourcesManager);
  getAllResources(): VectorString;
  getAllImages(): SetString;
  getAllAudios(): SetString;
  getAllFonts(): SetString;
  getAllBitmapFonts(): SetString;
  getAll(resourceType: string): SetString;
}

export class EditorSettings extends EmscriptenObject {
  constructor();
  serializeTo(element: SerializerElement): void;
  unserializeFrom(element: SerializerElement): void;
}

export class Point extends EmscriptenObject {
  constructor(name: string);
  setName(name: string): void;
  getName(): string;
  setXY(x: number, y: number): void;
  getX(): number;
  setX(x: number): void;
  getY(): number;
  setY(y: number): void;
}

export class VectorPoint extends EmscriptenObject {
  constructor();
  push_back(pt: Point): void;
  size(): number;
  at(index: number): Point;
  set(index: number, pt: Point): void;
  clear(): void;
}

export class Polygon2d extends EmscriptenObject {
  constructor();
  getVertices(): VectorVector2f;
  move(x: number, y: number): void;
  rotate(angle: number): void;
  isConvex(): boolean;
  computeCenter(): Vector2f;
  static createRectangle(width: number, height: number): Polygon2d;
}

export class VectorPolygon2d extends EmscriptenObject {
  constructor();
  push_back(polygon: Polygon2d): void;
  size(): number;
  at(index: number): Polygon2d;
  set(index: number, polygon: Polygon2d): void;
  removeFromVectorPolygon2d(index: number): void;
  clear(): void;
}

export class Sprite extends EmscriptenObject {
  constructor();
  setImageName(name: string): void;
  getImageName(): string;
  getOrigin(): Point;
  getCenter(): Point;
  isDefaultCenterPoint(): boolean;
  setDefaultCenterPoint(defaultPoint: boolean): void;
  getAllNonDefaultPoints(): VectorPoint;
  addPoint(point: Point): void;
  delPoint(name: string): void;
  getPoint(name: string): Point;
  hasPoint(name: string): boolean;
  isFullImageCollisionMask(): boolean;
  setFullImageCollisionMask(enabled: boolean): void;
  getCustomCollisionMask(): VectorPolygon2d;
  setCustomCollisionMask(collisionMask: VectorPolygon2d): void;
}

export class Direction extends EmscriptenObject {
  constructor();
  addSprite(sprite: Sprite): void;
  getSprite(index: number): Sprite;
  getSpritesCount(): number;
  hasNoSprites(): boolean;
  removeSprite(index: number): void;
  removeAllSprites(): void;
  isLooping(): boolean;
  setLoop(enable: boolean): void;
  getTimeBetweenFrames(): number;
  getSpriteNames(): VectorString;
  setTimeBetweenFrames(time: number): void;
  swapSprites(first: number, second: number): void;
  moveSprite(oldIndex: number, newIndex: number): void;
  setMetadata(metadata: string): void;
  getMetadata(): string;
}

export class Animation extends EmscriptenObject {
  constructor();
  setName(name: string): void;
  getName(): string;
  setDirectionsCount(count: number): void;
  getDirectionsCount(): number;
  getDirection(index: number): Direction;
  setDirection(direction: Direction, index: number): void;
  hasNoDirections(): boolean;
  useMultipleDirections(): boolean;
  setUseMultipleDirections(enable: boolean): void;
}

export class SpriteAnimationList extends EmscriptenObject {
  constructor();
  addAnimation(animation: Animation): void;
  getAnimation(index: number): Animation;
  getAnimationsCount(): number;
  removeAnimation(index: number): void;
  removeAllAnimations(): void;
  hasNoAnimations(): boolean;
  swapAnimations(first: number, second: number): void;
  moveAnimation(oldIndex: number, newIndex: number): void;
  adaptCollisionMaskAutomatically(): boolean;
  setAdaptCollisionMaskAutomatically(adaptCollisionMaskAutomatically: boolean): void;
}

export class SpriteObject extends ObjectConfiguration {
  constructor();
  getAnimations(): SpriteAnimationList;
  setUpdateIfNotVisible(updateIfNotVisible: boolean): void;
  getUpdateIfNotVisible(): boolean;
  setPreScale(value: number): void;
  getPreScale(): number;
}

export class Model3DAnimation extends EmscriptenObject {
  constructor();
  setName(name: string): void;
  getName(): string;
  setSource(name: string): void;
  getSource(): string;
  setShouldLoop(shouldLoop: boolean): void;
  shouldLoop(): boolean;
}

export class Model3DObjectConfiguration extends ObjectConfiguration {
  constructor();
  addAnimation(animation: Model3DAnimation): void;
  getAnimation(index: number): Model3DAnimation;
  hasAnimationNamed(name: string): boolean;
  getAnimationsCount(): number;
  removeAnimation(index: number): void;
  removeAllAnimations(): void;
  hasNoAnimations(): boolean;
  swapAnimations(first: number, second: number): void;
  moveAnimation(oldIndex: number, newIndex: number): void;
  getWidth(): number;
  getHeight(): number;
  getDepth(): number;
  getRotationX(): number;
  getRotationY(): number;
  getRotationZ(): number;
  getModelResourceName(): string;
  getMaterialType(): string;
  getOriginLocation(): string;
  getCenterLocation(): string;
  shouldKeepAspectRatio(): boolean;
}

export class SpineAnimation extends EmscriptenObject {
  constructor();
  setName(name: string): void;
  getName(): string;
  setSource(name: string): void;
  getSource(): string;
  setShouldLoop(shouldLoop: boolean): void;
  shouldLoop(): boolean;
}

export class SpineObjectConfiguration extends ObjectConfiguration {
  constructor();
  addAnimation(animation: SpineAnimation): void;
  getAnimation(index: number): SpineAnimation;
  hasAnimationNamed(name: string): boolean;
  getAnimationsCount(): number;
  removeAnimation(index: number): void;
  removeAllAnimations(): void;
  hasNoAnimations(): boolean;
  swapAnimations(first: number, second: number): void;
  moveAnimation(oldIndex: number, newIndex: number): void;
  getScale(): number;
  getSpineResourceName(): string;
}

export class Vector2f extends EmscriptenObject {
  constructor();
  x: float;
  y: float;
}

export class VectorVector2f extends EmscriptenObject {
  constructor();
  push_back(pt: Vector2f): void;
  size(): number;
  at(index: number): Vector2f;
  set(index: number, pt: Vector2f): void;
  removeFromVectorVector2f(index: number): void;
  moveVector2fInVector(oldIndex: number, newIndex: number): void;
  clear(): void;
}

export class TextObject extends ObjectConfiguration {
  constructor();
  setText(string: string): void;
  getText(): string;
  setCharacterSize(size: number): void;
  getCharacterSize(): number;
  setFontName(string: string): void;
  getFontName(): string;
  isBold(): boolean;
  setBold(enable: boolean): void;
  isItalic(): boolean;
  setItalic(enable: boolean): void;
  isUnderlined(): boolean;
  setUnderlined(enable: boolean): void;
  setColor(color: string): void;
  getColor(): string;
  setTextAlignment(textAlignment: string): void;
  getTextAlignment(): string;
  setVerticalTextAlignment(value: string): void;
  getVerticalTextAlignment(): string;
  setOutlineEnabled(enable: boolean): void;
  isOutlineEnabled(): boolean;
  setOutlineThickness(value: number): void;
  getOutlineThickness(): number;
  setOutlineColor(color: string): void;
  getOutlineColor(): string;
  setShadowEnabled(enable: boolean): void;
  isShadowEnabled(): boolean;
  setShadowColor(color: string): void;
  getShadowColor(): string;
  setShadowOpacity(value: number): void;
  getShadowOpacity(): number;
  setShadowAngle(value: number): void;
  getShadowAngle(): number;
  setShadowDistance(value: number): void;
  getShadowDistance(): number;
  setShadowBlurRadius(value: number): void;
  getShadowBlurRadius(): number;
}

export class TiledSpriteObject extends ObjectConfiguration {
  constructor();
  setTexture(texture: string): void;
  getTexture(): string;
  setWidth(width: number): void;
  getWidth(): number;
  setHeight(height: number): void;
  getHeight(): number;
}

export class PanelSpriteObject extends ObjectConfiguration {
  constructor();
  getLeftMargin(): number;
  setLeftMargin(newMargin: number): void;
  getTopMargin(): number;
  setTopMargin(newMargin: number): void;
  getRightMargin(): number;
  setRightMargin(newMargin: number): void;
  getBottomMargin(): number;
  setBottomMargin(newMargin: number): void;
  isTiled(): boolean;
  setTiled(enable: boolean): void;
  setTexture(texture: string): void;
  getTexture(): string;
  setWidth(width: number): void;
  getWidth(): number;
  setHeight(height: number): void;
  getHeight(): number;
}

export class ShapePainterObject extends ObjectConfiguration {
  constructor();
  setCoordinatesAbsolute(): void;
  setCoordinatesRelative(): void;
  areCoordinatesAbsolute(): boolean;
  setClearBetweenFrames(value: boolean): void;
  isClearedBetweenFrames(): boolean;
  setOutlineSize(size: number): void;
  getOutlineSize(): number;
  setOutlineColor(color: string): void;
  getOutlineColor(): string;
  setOutlineOpacity(val: number): void;
  getOutlineOpacity(): number;
  setFillColor(color: string): void;
  getFillColor(): string;
  setFillOpacity(val: number): void;
  getFillOpacity(): number;
  getAntialiasing(): string;
  setAntialiasing(value: string): void;
}

export class TextEntryObject extends ObjectConfiguration {
  constructor();
}

export class ParticleEmitterObject extends ObjectConfiguration {
  constructor();
  setRendererType(type: ParticleEmitterObject_RendererType): void;
  getRendererType(): ParticleEmitterObject_RendererType;
  setParticleTexture(resourceName: string): void;
  getParticleTexture(): string;
  setRendererParam1(newValue: number): void;
  getRendererParam1(): number;
  setRendererParam2(newValue: number): void;
  getRendererParam2(): number;
  isRenderingAdditive(): boolean;
  setRenderingAdditive(): void;
  setRenderingAlpha(): void;
  setMaxParticleNb(newValue: number): void;
  getMaxParticleNb(): number;
  setTank(newValue: number): void;
  getTank(): number;
  setFlow(newValue: number): void;
  getFlow(): number;
  setDestroyWhenNoParticles(enable: boolean): void;
  getDestroyWhenNoParticles(): boolean;
  setEmitterForceMin(newValue: number): void;
  getEmitterForceMin(): number;
  setEmitterForceMax(newValue: number): void;
  getEmitterForceMax(): number;
  setConeSprayAngle(newValue: number): void;
  getConeSprayAngle(): number;
  setZoneRadius(newValue: number): void;
  getZoneRadius(): number;
  setParticleGravityX(newValue: number): void;
  getParticleGravityX(): number;
  setParticleGravityY(newValue: number): void;
  getParticleGravityY(): number;
  setParticleGravityAngle(newValue: number): void;
  getParticleGravityAngle(): number;
  setParticleGravityLength(newValue: number): void;
  getParticleGravityLength(): number;
  setParticleLifeTimeMin(newValue: number): void;
  getParticleLifeTimeMin(): number;
  setParticleLifeTimeMax(newValue: number): void;
  getParticleLifeTimeMax(): number;
  setParticleColor1(newValue: string): void;
  getParticleColor1(): string;
  setParticleColor2(newValue: string): void;
  getParticleColor2(): string;
  setParticleAlpha1(newValue: number): void;
  getParticleAlpha1(): number;
  setParticleAlpha2(newValue: number): void;
  getParticleAlpha2(): number;
  setParticleSize1(newValue: number): void;
  getParticleSize1(): number;
  setParticleSize2(newValue: number): void;
  getParticleSize2(): number;
  setParticleAngle1(newValue: number): void;
  getParticleAngle1(): number;
  setParticleAngle2(newValue: number): void;
  getParticleAngle2(): number;
  setParticleAlphaRandomness1(newValue: number): void;
  getParticleAlphaRandomness1(): number;
  setParticleAlphaRandomness2(newValue: number): void;
  getParticleAlphaRandomness2(): number;
  setParticleSizeRandomness1(newValue: number): void;
  getParticleSizeRandomness1(): number;
  setParticleSizeRandomness2(newValue: number): void;
  getParticleSizeRandomness2(): number;
  setParticleAngleRandomness1(newValue: number): void;
  getParticleAngleRandomness1(): number;
  setParticleAngleRandomness2(newValue: number): void;
  getParticleAngleRandomness2(): number;
  setJumpForwardInTimeOnCreation(newValue: number): void;
  getJumpForwardInTimeOnCreation(): number;
}

export class LayoutCodeGenerator extends EmscriptenObject {
  constructor(project: Project);
  generateLayoutCompleteCode(layout: Layout, includes: SetString, diagnosticReport: DiagnosticReport, compilationForRuntime: boolean): string;
}

export class BehaviorCodeGenerator extends EmscriptenObject {
  constructor(project: Project);
  generateRuntimeBehaviorCompleteCode(eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, codeNamespace: string, behaviorMethodMangledNames: MapStringString, includes: SetString, compilationForRuntime: boolean): string;
  static getBehaviorPropertyGetterName(propertyName: string): string;
  static getBehaviorPropertySetterName(propertyName: string): string;
  static getBehaviorPropertyToggleFunctionName(propertyName: string): string;
  static getBehaviorSharedPropertyGetterName(propertyName: string): string;
  static getBehaviorSharedPropertySetterName(propertyName: string): string;
  static getBehaviorSharedPropertyToggleFunctionName(propertyName: string): string;
}

export class ObjectCodeGenerator extends EmscriptenObject {
  constructor(project: Project);
  generateRuntimeObjectCompleteCode(eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, codeNamespace: string, objectMethodMangledNames: MapStringString, includes: SetString, compilationForRuntime: boolean): string;
  static getObjectPropertyGetterName(propertyName: string): string;
  static getObjectPropertySetterName(propertyName: string): string;
  static getObjectPropertyToggleFunctionName(propertyName: string): string;
}

export class EventsFunctionsExtensionCodeGenerator extends EmscriptenObject {
  constructor(project: Project);
  generateFreeEventsFunctionCompleteCode(extension: EventsFunctionsExtension, eventsFunction: EventsFunction, codeNamespac: string, includes: SetString, compilationForRuntime: boolean): string;
}

export class PreviewExportOptions extends EmscriptenObject {
  constructor(project: Project, outputPath: string);
  useWebsocketDebuggerClientWithServerAddress(address: string, port: string): PreviewExportOptions;
  useWindowMessageDebuggerClient(): PreviewExportOptions;
  useMinimalDebuggerClient(): PreviewExportOptions;
  setInAppTutorialMessageInPreview(message: string, position: string): PreviewExportOptions;
  setLayoutName(layoutName: string): PreviewExportOptions;
  setFallbackAuthor(id: string, username: string): PreviewExportOptions;
  setAuthenticatedPlayer(playerId: string, playerUsername: string, playerToken: string): PreviewExportOptions;
  setExternalLayoutName(externalLayoutName: string): PreviewExportOptions;
  setIncludeFileHash(includeFile: string, hash: number): PreviewExportOptions;
  setProjectDataOnlyExport(enable: boolean): PreviewExportOptions;
  setNativeMobileApp(enable: boolean): PreviewExportOptions;
  setFullLoadingScreen(enable: boolean): PreviewExportOptions;
  setIsDevelopmentEnvironment(enable: boolean): PreviewExportOptions;
  setNonRuntimeScriptsCacheBurst(value: number): PreviewExportOptions;
  setElectronRemoteRequirePath(electronRemoteRequirePath: string): PreviewExportOptions;
  setGDevelopResourceToken(gdevelopResourceToken: string): PreviewExportOptions;
  setAllowAuthenticationUsingIframeForPreview(enable: boolean): PreviewExportOptions;
  setCrashReportUploadLevel(crashReportUploadLevel: string): PreviewExportOptions;
  setPreviewContext(previewContext: string): PreviewExportOptions;
  setGDevelopVersionWithHash(gdevelopVersionWithHash: string): PreviewExportOptions;
  setProjectTemplateSlug(projectTemplateSlug: string): PreviewExportOptions;
  setSourceGameId(sourceGameId: string): PreviewExportOptions;
  addScreenshotCapture(delayTimeInSeconds: number, signedUrl: string, publicUrl: string): PreviewExportOptions;
}

export class ExportOptions extends EmscriptenObject {
  constructor(project: Project, outputPath: string);
  setFallbackAuthor(id: string, username: string): ExportOptions;
  setTarget(target: string): ExportOptions;
}

export class Exporter extends EmscriptenObject {
  constructor(fs: AbstractFileSystem, gdjsRoot: string);
  setCodeOutputDirectory(path: string): void;
  exportProjectForPixiPreview(options: PreviewExportOptions): boolean;
  exportWholePixiProject(options: ExportOptions): boolean;
  getLastError(): string;
}

export class JsCodeEvent extends EmscriptenObject {
  constructor();
  getInlineCode(): string;
  setInlineCode(type: string): void;
  getParameterObjects(): string;
  setParameterObjects(type: string): void;
  isEventsSheetExpanded(): boolean;
  setEventsSheetExpanded(enable: boolean): void;
  clone(): JsCodeEvent;
  getType(): string;
  setType(type: string): void;
  isExecutable(): boolean;
  canHaveSubEvents(): boolean;
  hasSubEvents(): boolean;
  getSubEvents(): EventsList;
  isDisabled(): boolean;
  setDisabled(disable: boolean): void;
  isFolded(): boolean;
  setFolded(folded: boolean): void;
  serializeTo(element: SerializerElement): void;
  unserializeFrom(project: Project, element: SerializerElement): void;
}

export class MetadataDeclarationHelper extends EmscriptenObject {
  constructor();
  static declareExtension(extension: PlatformExtension, eventsFunctionsExtension: EventsFunctionsExtension): void;
  generateFreeFunctionMetadata(project: Project, extension: PlatformExtension, eventsFunctionsExtension: EventsFunctionsExtension, eventsFunction: EventsFunction): AbstractFunctionMetadata;
  static generateBehaviorMetadata(project: Project, extension: PlatformExtension, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedBehavior: EventsBasedBehavior, behaviorMethodMangledNames: MapStringString): BehaviorMetadata;
  static generateObjectMetadata(project: Project, extension: PlatformExtension, eventsFunctionsExtension: EventsFunctionsExtension, eventsBasedObject: EventsBasedObject, objectMethodMangledNames: MapStringString): ObjectMetadata;
  static getExtensionCodeNamespacePrefix(eventsFunctionsExtension: EventsFunctionsExtension): string;
  static getFreeFunctionCodeName(eventsFunctionsExtension: EventsFunctionsExtension, eventsFunction: EventsFunction): string;
  static getFreeFunctionCodeNamespace(eventsFunction: EventsFunction, codeNamespacePrefix: string): string;
  static getBehaviorFunctionCodeNamespace(eventsBasedBehavior: EventsBasedBehavior, codeNamespacePrefix: string): string;
  static getObjectFunctionCodeNamespace(eventsBasedObject: EventsBasedObject, codeNamespacePrefix: string): string;
  static isBehaviorLifecycleEventsFunction(functionName: string): boolean;
  static isObjectLifecycleEventsFunction(functionName: string): boolean;
  static isExtensionLifecycleEventsFunction(functionName: string): boolean;
  static shiftSentenceParamIndexes(sentence: string, offset: number): string;
}

export function toNewVectorString(): VectorString;

export function getTypeOfBehavior(layout: ObjectsContainer, name: string, searchInGroups: boolean): string;

export function getTypeOfObject(layout: ObjectsContainer, name: string, searchInGroups: boolean): string;

export function getBehaviorsOfObject(layout: ObjectsContainer, name: string, searchInGroups: boolean): VectorString;

export function isDefaultBehavior(layout: ObjectsContainer, objectOrGroupName: string, behaviorName: string, searchInGroups: boolean): boolean;

export function getTypeOfBehaviorInObjectOrGroup(layout: ObjectsContainer, objectOrGroupName: string, behaviorName: string, searchInGroups: boolean): string;

export function getBehaviorNamesInObjectOrGroup(layout: ObjectsContainer, objectOrGroupName: string, behaviorType: string, searchInGroups: boolean): VectorString;

export function removeFromVectorPolygon2d(index: number): void;

export function removeFromVectorVector2f(index: number): void;

export function moveVector2fInVector(oldIndex: number, newIndex: number): void;

export function asStandardEvent(object: Event): StandardEvent;

export function asRepeatEvent(object: Event): RepeatEvent;

export function asWhileEvent(object: Event): WhileEvent;

export function asForEachEvent(object: Event): ForEachEvent;

export function asForEachChildVariableEvent(object: Event): ForEachChildVariableEvent;

export function asCommentEvent(object: Event): CommentEvent;

export function asGroupEvent(object: Event): GroupEvent;

export function asLinkEvent(object: Event): LinkEvent;

export function asJsCodeEvent(object: Event): JsCodeEvent;

export function asPlatform(object: JsPlatform): Platform;

export function asSpriteConfiguration(object: ObjectConfiguration): SpriteObject;

export function asTiledSpriteConfiguration(object: ObjectConfiguration): TiledSpriteObject;

export function asPanelSpriteConfiguration(object: ObjectConfiguration): PanelSpriteObject;

export function asTextObjectConfiguration(object: ObjectConfiguration): TextObject;

export function asShapePainterConfiguration(object: ObjectConfiguration): ShapePainterObject;

export function asTextEntryObject(object: ObjectConfiguration): TextEntryObject;

export function asParticleEmitterConfiguration(object: ObjectConfiguration): ParticleEmitterObject;

export function asCustomObjectConfiguration(object: ObjectConfiguration): CustomObjectConfiguration;

export function asModel3DConfiguration(object: ObjectConfiguration): Model3DObjectConfiguration;

export function asSpineConfiguration(object: ObjectConfiguration): SpineObjectConfiguration;

export function asObjectJsImplementation(object: EmscriptenObject): ObjectJsImplementation;

export function asImageResource(object: Resource): ImageResource;


export const Object: typeof gdObject;

/**
 * Initialises the Platforms included in the build (currently, only the JsPlatform),
 * and loads all built-in extensions into the platform.
 * To be called once when the library is first loaded.
 */
export const initializePlatforms: typeof ProjectHelper.initializePlatforms;

/**
 * Returns the pointer in WASM memory to an object. It is a number that uniquely
 * represents that instance of the object.
 *
 * @see {@link wrapPointer} to convert a pointer back to an object.
 */
export function getPointer(object: EmscriptenObject): number;

type ClassConstructor<T> = {
  new (...args: any[]): T;
};

/**
 * Wraps a pointer with a wrapper class, allowing to use the object located at the
 * pointer's destination as an instance of that class.
 *
 * @see {@link getPointer} to get a pointer from an object.
 */
export function wrapPointer<T extends EmscriptenObject>(ptr: number, objectClass: ClassConstructor<T>): T;

/**
 * Casts an object to another class type.
 *
 * **Careful** - this is not a conversion function.
 * This only changes the class type and functions exposed, not the underlying memory.
 * Only cast to another class if you are certain that the underlying memory is of that type!
 */
export function castObject<T extends EmscriptenObject>(object: EmscriptenObject, objectClass: ClassConstructor<T>): T;

/**
 * Checks whether two objects are pointing to the same underlying memory.
 * A reference to the object itself is not trustworthy, since there may be multiple
 * wrapper objects (which allow to call C++ function on C++ memory) for a single
 * pointer ("real object").
 *
 * This function must be therefore used to check for referential equality instead of
 * JavaScript's standard equality operators when handling Emscripten objects.
 */
export function compare<T extends EmscriptenObject>(object1: T, object2: T): boolean;

/**
 * Call this to free the object's underlying memory. It may not be used afterwards.
 *
 * **Call with care** - if the object owns some other objects, those will also be destroyed,
 * or if this object is owned by another object that does not expect it to be externally deleted
 * (e.g. it is a child of a map), objects will be put in an invalid state that will most likely
 * crash the app.
 *
 * If the object is owned by your code, you should still call this method when adequate, as
 * otherwise the memory will never be freed, causing a memory leak, which is to be avoided.
 *
 * The alias {@link EmscriptenObject.delete} is recommended instead, for readability.
 */
export function destroy(object: EmscriptenObject): void;

export as namespace gd;

declare global {
  const gd: typeof gd;
}
