/*
 * GDevelop.js
 * Copyright 2015 Florian Rival (<EMAIL>). All rights reserved.
 * This project is released under the MIT License.
 */

/**
 * IDL file for GDevelop classes bindings.
 *
 * Notes:
 *
 * WRAPPED_* and STATIC_* functions are declared as such
 * to allow the Wrapper.cpp file to use a macro to do special work on it.
 * These functions are renamed in the final .js file (so that
 * `WRAPPED_set` become `set` for example).
 */

//Interfaces for some types from
//the C++ Standard Library:

interface VectorString {
    void VectorString();

    void push_back([Const] DOMString str);
    void resize(unsigned long size);
    unsigned long size();
    [Const, Ref] DOMString at(unsigned long index);
    void WRAPPED_set(unsigned long index, [Const] DOMString str);
    void clear();
};

interface VectorPlatformExtension {
    unsigned long size();
    [Const] PlatformExtension WRAPPED_at(unsigned long index);
};

interface VectorDependencyMetadata {
    unsigned long size();
    [Const, Ref] DependencyMetadata at(unsigned long index);
};

interface VectorSourceFileMetadata {
    unsigned long size();
    [Const, Ref] SourceFileMetadata at(unsigned long index);
};

interface VectorInt {
    unsigned long size();
    long at(unsigned long index);
};

interface VectorVariable {
    unsigned long size();
    [Const] Variable WRAPPED_at(unsigned long index);
};

interface VectorObjectFolderOrObject {
    unsigned long size();
    [Const] ObjectFolderOrObject at(unsigned long index);
};

interface VectorScreenshot {
    unsigned long size();
    [Const, Ref] Screenshot at(unsigned long index);
};

interface MapStringString {
    void MapStringString();

    [Const, Ref] DOMString MAP_get([Const] DOMString name);
    void MAP_set([Const] DOMString name, [Const] DOMString str);
    boolean MAP_has([Const] DOMString name);
    [Value] VectorString MAP_keys();
};

interface MapStringBoolean {
    void MapStringBoolean();

    boolean MAP_get([Const] DOMString name);
    void MAP_set([Const] DOMString name, boolean value);
    boolean MAP_has([Const] DOMString name);
    [Value] VectorString MAP_keys();
};

interface MapStringDouble {
    void MapStringDouble();

    double MAP_get([Const] DOMString name);
    void MAP_set([Const] DOMString name, double value);
    double MAP_has([Const] DOMString name);
    [Value] VectorString MAP_keys();
};

interface MapStringVariable {
    [Ref] Variable MAP_get([Const] DOMString name);
    void MAP_set([Const] DOMString name, [Const, Ref] Variable prop);
    boolean MAP_has([Const] DOMString name);
    [Value] VectorString MAP_keys();
};

interface MapStringExpressionMetadata {
    [Ref] ExpressionMetadata MAP_get([Const] DOMString name);
    void MAP_set([Const] DOMString name, [Const, Ref] ExpressionMetadata prop);
    boolean MAP_has([Const] DOMString name);
    [Value] VectorString MAP_keys();
};

interface MapStringInstructionMetadata {
    [Ref] InstructionMetadata MAP_get([Const] DOMString name);
    void MAP_set([Const] DOMString name, [Const, Ref] InstructionMetadata prop);
    boolean MAP_has([Const] DOMString name);
    [Value] VectorString MAP_keys();
};

interface MapStringEventMetadata {
    [Ref] EventMetadata MAP_get([Const] DOMString name);
    void MAP_set([Const] DOMString name, [Const, Ref] EventMetadata prop);
    boolean MAP_has([Const] DOMString name);
    [Value] VectorString MAP_keys();
};

interface MapExtensionProperties {
    [Ref] MapStringPropertyDescriptor MAP_get([Const] DOMString name);
    void MAP_set([Const] DOMString name, [Const, Ref] MapStringPropertyDescriptor prop);
    boolean MAP_has([Const] DOMString name);
    [Value] VectorString MAP_keys();
};

interface SetString {
    void SetString();
    [Value] VectorString FREE_toNewVectorString();
};

interface ProjectHelper {
    [Ref] Project STATIC_CreateNewGDJSProject();
    void STATIC_InitializePlatforms();
    [Const, Value] DOMString STATIC_SanityCheckBehaviorProperty(Behavior behavior, [Const] DOMString propertyName, [Const] DOMString newValue);
    [Const, Value] DOMString STATIC_SanityCheckBehaviorsSharedDataProperty(BehaviorsSharedData behavior, [Const] DOMString propertyName, [Const] DOMString newValue);
    [Const, Value] DOMString STATIC_SanityCheckObjectProperty(ObjectConfiguration configuration, [Const] DOMString propertyName, [Const] DOMString newValue);
    [Const, Value] DOMString STATIC_SanityCheckObjectInitialInstanceProperty(ObjectConfiguration configuration, [Const] DOMString propertyName, [Const] DOMString newValue);
};

interface EventsVariablesFinder {
    void EventsVariablesFinder();

    [Const, Value] SetString STATIC_FindAllGlobalVariables([Const, Ref] Platform platform, [Const, Ref] Project project);
    [Const, Value] SetString STATIC_FindAllLayoutVariables([Const, Ref] Platform platform, [Const, Ref] Project project, [Const, Ref] Layout layout);
    [Const, Value] SetString STATIC_FindAllObjectVariables([Const, Ref] Platform platform, [Const, Ref] Project project, [Const, Ref] Layout layout, [Const] DOMString objectName);

    //Inherited from ExpressionParser2NodeWorker:
};

interface EventsIdentifiersFinder {
    void EventsIdentifiersFinder();

    [Const, Value] SetString STATIC_FindAllIdentifierExpressions([Const, Ref] Platform platform, [Const, Ref] Project project, [Const, Ref] Layout layout, [Const] DOMString identifierType, [Const] DOMString contextObjectName);

    //Inherited from ExpressionParser2NodeWorker:
};

interface EventsFunctionSelfCallChecker {
  boolean STATIC_IsFreeFunctionOnlyCallingItself(
      [Const, Ref] Project project,
      [Const, Ref] EventsFunctionsExtension extension,
      [Const, Ref] EventsFunction eventsFunction);

  boolean STATIC_IsBehaviorFunctionOnlyCallingItself(
      [Const, Ref] Project project,
      [Const, Ref] EventsFunctionsExtension extension,
      [Const, Ref] EventsBasedBehavior eventsBasedBehavior,
      [Const, Ref] EventsFunction eventsFunction);

  boolean STATIC_IsObjectFunctionOnlyCallingItself(
      [Const, Ref] Project project,
      [Const, Ref] EventsFunctionsExtension extension,
      [Const, Ref] EventsBasedObject eventsBasedObject,
      [Const, Ref] EventsFunction eventsFunction);
};

interface InstructionOrExpressionGroupMetadata {
    void InstructionOrExpressionGroupMetadata();

    [Ref] InstructionOrExpressionGroupMetadata SetIcon([Const] DOMString icon);
    [Const, Ref] DOMString GetIcon();
};

interface VersionWrapper {
    long STATIC_Major();
    long STATIC_Minor();
    long STATIC_Build();
    long STATIC_Revision();

    [Const, Value] DOMString STATIC_FullString();
    [Const, Value] DOMString STATIC_Status();
};

interface Platform {
    [Const, Value] DOMString GetName();
    [Const, Value] DOMString GetFullName();
    [Const, Value] DOMString GetSubtitle();
    [Const, Value] DOMString GetDescription();
    [Const, Ref] InstructionOrExpressionGroupMetadata GetInstructionOrExpressionGroupMetadata([Const] DOMString name);
    boolean IsExtensionLoaded([Const] DOMString name);
    void RemoveExtension([Const] DOMString name);
    void ReloadBuiltinExtensions();

    [Const, Ref] VectorPlatformExtension GetAllPlatformExtensions();
};

interface JsPlatform {
    [Ref] JsPlatform STATIC_Get();
    void AddNewExtension([Const, Ref] PlatformExtension extension);

    // Inherited from Platform:
    [Const, Value] DOMString GetName();
    [Const, Value] DOMString GetFullName();
    [Const, Value] DOMString GetSubtitle();
    [Const, Value] DOMString GetDescription();
    [Const, Ref] InstructionOrExpressionGroupMetadata GetInstructionOrExpressionGroupMetadata([Const] DOMString name);
    boolean IsExtensionLoaded([Const] DOMString name);
    void RemoveExtension([Const] DOMString name);
    void ReloadBuiltinExtensions();

    [Const, Ref] VectorPlatformExtension GetAllPlatformExtensions();
};

interface PairStringVariable {
    void PairStringVariable();

    [Const, Ref] DOMString WRAPPED_GetName();
    [Ref] Variable WRAPPED_GetVariable();
};

enum Variable_Type {
    "Variable::Unknown",
    "Variable::MixedTypes",
    "Variable::String",
    "Variable::Number",
    "Variable::Boolean",
    "Variable::Structure",
    "Variable::Array"
};

interface VariableInstructionSwitcher {
    boolean STATIC_IsSwitchableVariableInstruction([Const] DOMString instructionType);
    boolean STATIC_IsSwitchableObjectVariableInstruction([Const] DOMString instructionType);
    [Const, Ref] DOMString STATIC_GetSwitchableVariableInstructionIdentifier([Const] DOMString instructionType);
    [Const] Variable_Type STATIC_GetSwitchableInstructionVariableType([Const] DOMString instructionType);
    void STATIC_SwitchVariableInstructionType(
    [Ref] Instruction instruction, [Const] Variable_Type variableType);
    [Const] Variable_Type STATIC_GetVariableTypeFromParameters(
        [Const, Ref] Platform platform,
        [Const, Ref] ProjectScopedContainers projectScopedContainers,
        [Const, Ref] Instruction instruction);
    void STATIC_SwitchBetweenUnifiedInstructionIfNeeded(
        [Const, Ref] Platform platform,
        [Const, Ref] ProjectScopedContainers projectScopedContainers,
        [Ref] Instruction instruction);
};

interface Variable {
    void Variable();

    boolean STATIC_IsPrimitive([Const] Variable_Type type);

    Variable_Type GetType();
    void CastTo([Const] DOMString type);

    void SetString([Const] DOMString str);
    [Const, Ref] DOMString GetString();
    void SetValue([Const] double val);
    double GetValue();
    void SetBool([Const] boolean val);
    boolean GetBool();
    boolean HasMixedValues();
    void SetFolded(boolean val);
    boolean IsFolded();

    unsigned long GetChildrenCount();
    boolean Contains([Const, Ref] Variable variableToSearch, boolean recursive);

    boolean HasChild([Const] DOMString str);
    [Ref] Variable GetChild([Const] DOMString str);
    void RemoveChild([Const] DOMString name);
    boolean RenameChild([Const] DOMString oldName, [Const] DOMString newName);
    [Value] VectorString GetAllChildrenNames();
    void RemoveRecursively([Const, Ref] Variable variableToRemove);

    [Ref] Variable GetAtIndex([Const] unsigned long index);
    [Ref] Variable PushNew();
    void RemoveAtIndex([Const] unsigned long index);
    [Const, Ref] VectorVariable GetAllChildrenArray();
    void MoveChildInArray([Const] unsigned long oldIndex, [Const] unsigned long newIndex);
    boolean InsertAtIndex([Const, Ref] Variable variable, [Const] unsigned long index);
    boolean InsertChild([Const] DOMString name, [Const, Ref] Variable variable);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
    [Ref] Variable ResetPersistentUuid();
    [Ref] Variable ClearPersistentUuid();
};

enum VariablesContainer_SourceType {
  "VariablesContainer::Unknown",
  "VariablesContainer::Global",
  "VariablesContainer::Scene",
  "VariablesContainer::Object",
  "VariablesContainer::Local",
  "VariablesContainer::ExtensionGlobal",
  "VariablesContainer::ExtensionScene",
  "VariablesContainer::Parameters",
  "VariablesContainer::Properties",
};

interface VariablesContainer {
    void VariablesContainer(VariablesContainer_SourceType sourceType);

    VariablesContainer_SourceType GetSourceType();

    boolean Has([Const] DOMString name);
    [Ref] Variable Get([Const] DOMString name);
    [Ref] Variable GetAt(unsigned long index);
    [Const, Ref] DOMString GetNameAt(unsigned long index);
    [Ref] Variable Insert([Const] DOMString name, [Const, Ref] Variable variable, unsigned long index);
    [Ref] Variable InsertNew([Const] DOMString name, unsigned long index);
    void Remove([Const] DOMString name);
    boolean Rename([Const] DOMString oldName, [Const] DOMString newName);
    void Swap(unsigned long firstIndex, unsigned long secondIndex);
    void Move(unsigned long oldIndex, unsigned long newIndex);
    unsigned long GetPosition([Const] DOMString name);
    unsigned long Count();
    void Clear();
    void RemoveRecursively([Const, Ref] Variable variableToRemove);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
    [Ref] VariablesContainer ResetPersistentUuid();
    [Ref] VariablesContainer ClearPersistentUuid();
};

interface VariablesContainersList {
  boolean Has([Const] DOMString name);
  [Const, Ref] Variable Get([Const] DOMString name);
  [Const, Ref] VariablesContainer GetVariablesContainerFromVariableOrPropertyOrParameterName([Const] DOMString variableName);
  [Const, Ref] VariablesContainer GetVariablesContainerFromVariableOrPropertyName([Const] DOMString variableName);
  [Const, Ref] VariablesContainer GetVariablesContainerFromVariableNameOnly([Const] DOMString variableName);

  [Const, Ref] VariablesContainer GetVariablesContainer(unsigned long index);
  unsigned long GetVariablesContainersCount();
};

interface ObjectGroup {
    void ObjectGroup();

    [Const, Ref] DOMString GetName();
    void SetName([Const] DOMString name);
    void AddObject([Const] DOMString objectName);
    void RemoveObject([Const] DOMString objectName);
    boolean Find([Const] DOMString objectName);

    [Const, Ref] VectorString GetAllObjectsNames();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface ObjectVariableHelper {
    [Value] VariablesContainer STATIC_MergeVariableContainers(
        [Const, Ref] ObjectsContainersList objectsContainersList,
        [Const, Ref] ObjectGroup objectGroup);
    void STATIC_FillAnyVariableBetweenObjects(
        [Ref] ObjectsContainer globalObjectsContainer,
        [Ref] ObjectsContainer objectsContainer,
        [Const, Ref] ObjectGroup objectGroup);
    void STATIC_ApplyChangesToVariants(
        [Ref] EventsBasedObject eventsBasedObject,
        [Const] DOMString objectName,
        [Const, Ref] VariablesChangeset changeset);
};

interface EventsBasedObjectVariantHelper {
    void STATIC_ComplyVariantsToEventsBasedObject(
        [Ref, Const] Project project,
        [Ref] EventsBasedObject eventsBasedObject);
};

interface ObjectGroupsContainer {
    void ObjectGroupsContainer();

    boolean Has([Const] DOMString name);
    [Ref] ObjectGroup Insert([Const, Ref] ObjectGroup objectGroup, unsigned long position);
    [Ref] ObjectGroup InsertNew([Const] DOMString name, unsigned long position);
    unsigned long Count();
    [Ref] ObjectGroup Get([Const] DOMString name);
    [Ref] ObjectGroup GetAt(unsigned long index);
    void Clear();
    void Remove([Const] DOMString name);
    unsigned long GetPosition([Const] DOMString name);
    boolean Rename([Const] DOMString oldName, [Const] DOMString newName);
    void Move(unsigned long oldIndex, unsigned long newIndex);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface PlatformSpecificAssets {
    void PlatformSpecificAssets();

    boolean Has([Const] DOMString platform, [Const] DOMString name);
    [Const, Ref] DOMString Get([Const] DOMString platform, [Const] DOMString name);
    void Remove([Const] DOMString platform, [Const] DOMString name);
    void Set([Const] DOMString platform, [Const] DOMString name, [Const] DOMString resourceName);

    void ExposeResources([Ref] ArbitraryResourceWorker worker);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface LoadingScreen {
    void LoadingScreen();

    boolean IsGDevelopLogoShownDuringLoadingScreen();
    [Ref] LoadingScreen ShowGDevelopLogoDuringLoadingScreen(boolean show);
    [Const, Ref] DOMString GetGDevelopLogoStyle();
    [Ref] LoadingScreen SetGDevelopLogoStyle([Const] DOMString value);
    [Const, Ref] DOMString GetBackgroundImageResourceName();
    [Ref] LoadingScreen SetBackgroundImageResourceName([Const] DOMString value);
    long GetBackgroundColor();
    [Ref] LoadingScreen SetBackgroundColor(long value);
    double GetBackgroundFadeInDuration();
    [Ref] LoadingScreen SetBackgroundFadeInDuration(double value);
    double GetMinDuration();
    [Ref] LoadingScreen SetMinDuration(double value);
    double GetLogoAndProgressFadeInDuration();
    [Ref] LoadingScreen SetLogoAndProgressFadeInDuration(double value);
    double GetLogoAndProgressLogoFadeInDelay();
    [Ref] LoadingScreen SetLogoAndProgressLogoFadeInDelay(double value);
    boolean GetShowProgressBar();
    [Ref] LoadingScreen SetShowProgressBar(boolean value);
    double GetProgressBarMaxWidth();
    [Ref] LoadingScreen SetProgressBarMaxWidth(double value);
    double GetProgressBarMinWidth();
    [Ref] LoadingScreen SetProgressBarMinWidth(double value);
    double GetProgressBarWidthPercent();
    [Ref] LoadingScreen SetProgressBarWidthPercent(double value);
    double GetProgressBarHeight();
    [Ref] LoadingScreen SetProgressBarHeight(double value);
    long GetProgressBarColor();
    [Ref] LoadingScreen SetProgressBarColor(long value);


    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface Watermark {
    void Watermark();

    boolean IsGDevelopWatermarkShown();
    [Ref] Watermark ShowGDevelopWatermark(boolean show);
    [Const, Ref] DOMString GetPlacement();
    [Ref] Watermark SetPlacement([Const] DOMString value);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface ObjectFolderOrObject {
    void ObjectFolderOrObject();
    boolean IsFolder();
    boolean IsRootFolder();
    [Ref] gdObject GetObject();
    [Const, Ref] DOMString GetFolderName();
    void SetFolderName([Const] DOMString name);
    boolean HasObjectNamed([Const] DOMString name);
    [Ref] ObjectFolderOrObject GetObjectNamed([Const] DOMString name);
    unsigned long GetChildrenCount();
    [Ref] ObjectFolderOrObject GetChildAt(unsigned long pos);
    [Ref] ObjectFolderOrObject GetObjectChild([Const] DOMString name);
    unsigned long GetChildPosition([Const, Ref] ObjectFolderOrObject child);
    [Ref] ObjectFolderOrObject GetParent();
    [Ref] ObjectFolderOrObject InsertNewFolder([Const] DOMString name, unsigned long newPosition);
    void MoveObjectFolderOrObjectToAnotherFolder([Ref] ObjectFolderOrObject objectFolderOrObject, [Ref] ObjectFolderOrObject newParentFolder, unsigned long newPosition);
    void MoveChild(unsigned long oldIndex, unsigned long newIndex);
    void RemoveFolderChild([Const, Ref] ObjectFolderOrObject childToRemove);
    boolean IsADescendantOf([Const, Ref] ObjectFolderOrObject otherObjectFolderOrObject);

    QuickCustomization_Visibility GetQuickCustomizationVisibility();
    void SetQuickCustomizationVisibility(QuickCustomization_Visibility visibility);
};

enum ObjectsContainer_SourceType {
  "ObjectsContainer::Unknown",
  "ObjectsContainer::Global",
  "ObjectsContainer::Scene",
  "ObjectsContainer::Object",
  "ObjectsContainer::Function",
};

interface ObjectsContainer {
    void ObjectsContainer(ObjectsContainer_SourceType sourceType);

    ObjectsContainer_SourceType GetSourceType();

    [Ref] gdObject InsertNewObject([Ref] Project project, [Const] DOMString type, [Const] DOMString name, unsigned long pos);
    [Ref] gdObject InsertNewObjectInFolder([Ref] Project project, [Const] DOMString type, [Const] DOMString name, [Ref] ObjectFolderOrObject folder, unsigned long pos);
    [Ref] gdObject InsertObject([Const, Ref] gdObject obj, unsigned long pos);
    boolean HasObjectNamed([Const] DOMString name);
    [Ref] gdObject GetObject([Const] DOMString name);
    [Ref] gdObject GetObjectAt(unsigned long pos);
    unsigned long GetObjectPosition([Const] DOMString name);
    void RemoveObject([Const] DOMString name);
    void MoveObject(unsigned long oldIndex, unsigned long newIndex);
    void MoveObjectFolderOrObjectToAnotherContainerInFolder([Ref] ObjectFolderOrObject objectFolderOrObject, [Ref] ObjectsContainer newObjectsContainer, [Ref] ObjectFolderOrObject parentObjectFolderOrObject, unsigned long newPosition);
    unsigned long GetObjectsCount();
    [Ref] ObjectFolderOrObject GetRootFolder();
    [Value] VectorObjectFolderOrObject GetAllObjectFolderOrObjects();
    [Ref] ObjectGroupsContainer GetObjectGroups();

    [Const, Value] DOMString FREE_GetTypeOfBehavior(
        [Const, Ref] ObjectsContainer layout,
        [Const] DOMString name,
        boolean searchInGroups);
    [Const, Value] DOMString FREE_GetTypeOfObject(
        [Const, Ref] ObjectsContainer layout,
        [Const] DOMString name,
        boolean searchInGroups);
    [Value] VectorString FREE_GetBehaviorsOfObject(
        [Const, Ref] ObjectsContainer layout,
        [Const] DOMString name,
        boolean searchInGroups);
    boolean FREE_IsDefaultBehavior(
        [Const, Ref] ObjectsContainer layout,
        [Const] DOMString objectOrGroupName,
        [Const] DOMString behaviorName,
        boolean searchInGroups);
    [Const, Value] DOMString FREE_GetTypeOfBehaviorInObjectOrGroup(
        [Const, Ref] ObjectsContainer layout,
        [Const] DOMString objectOrGroupName,
        [Const] DOMString behaviorName,
        boolean searchInGroups);
    [Value] VectorString FREE_GetBehaviorNamesInObjectOrGroup(
        [Const, Ref] ObjectsContainer layout,
        [Const] DOMString objectOrGroupName,
        [Const] DOMString behaviorType,
        boolean searchInGroups);
};

interface Project {
    void Project();

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
    [Ref] VectorString GetCategories();
    void SetDescription([Const] DOMString description);
    [Const, Ref] DOMString GetDescription();
    void SetVersion([Const] DOMString authorName);
    [Const, Ref] DOMString GetVersion();
    void SetAuthor([Const] DOMString authorName);
    [Const, Ref] DOMString GetAuthor();
    [Ref] VectorString GetAuthorIds();
    [Ref] VectorString GetAuthorUsernames();
    boolean IsPlayableWithKeyboard();
    void SetPlayableWithKeyboard(boolean playable);
    boolean IsPlayableWithGamepad();
    void SetPlayableWithGamepad(boolean playable);
    boolean IsPlayableWithMobile();
    void SetPlayableWithMobile(boolean playable);
    void SetPackageName([Const] DOMString packageName);
    [Const, Ref] DOMString GetPackageName();
    void SetTemplateSlug([Const] DOMString templateSlug);
    [Const, Ref] DOMString GetTemplateSlug();
    void SetOrientation([Const] DOMString orientation);
    [Const, Ref] DOMString GetOrientation();
    void SetProjectUuid([Const] DOMString projectUuid);
    [Const, Ref] DOMString GetProjectUuid();
    void ResetProjectUuid();
    void SetProjectFile([Const] DOMString file);
    [Const, Ref] DOMString GetProjectFile();
    void SetGameResolutionSize(long width, long height);
    long GetGameResolutionWidth();
    long GetGameResolutionHeight();
    boolean GetAdaptGameResolutionAtRuntime();
    void SetAdaptGameResolutionAtRuntime(boolean adaptGameResolutionAtRuntime);
    void SetScaleMode([Const] DOMString scaleMode);
    [Const, Ref] DOMString GetScaleMode();
    void SetPixelsRounding(boolean pixelsRounding);
    boolean GetPixelsRounding();
    void SetSizeOnStartupMode([Const] DOMString orientation);
    [Const, Ref] DOMString GetSizeOnStartupMode();
    void SetAntialiasingMode([Const] DOMString antialiasingMode);
    [Const, Ref] DOMString GetAntialiasingMode();
    void SetAntialisingEnabledOnMobile(boolean pixelsRounding);
    boolean IsAntialisingEnabledOnMobile();
    long GetMaximumFPS();
    void SetMaximumFPS(long fps);
    long GetMinimumFPS();
    void SetMinimumFPS(long fps);
    void SetFolderProject(boolean enable);
    boolean IsFolderProject();
    void SetUseDeprecatedZeroAsDefaultZOrder(boolean enable);
    boolean GetUseDeprecatedZeroAsDefaultZOrder();

    void SetLastCompilationDirectory([Const] DOMString path);
    [Const, Ref] DOMString GetLastCompilationDirectory();

    [Ref] ExtensionProperties GetExtensionProperties();
    void AddPlatform([Ref] Platform platform);
    [Ref] Platform GetCurrentPlatform();

    [Ref] PlatformSpecificAssets GetPlatformSpecificAssets();
    [Ref] LoadingScreen GetLoadingScreen();
    [Ref] Watermark GetWatermark();

    boolean HasLayoutNamed([Const] DOMString name);
    [Ref] Layout GetLayout([Const] DOMString name);
    [Ref] Layout GetLayoutAt(unsigned long index);
    void MoveLayout(unsigned long oldIndex, unsigned long newIndex);
    void SwapLayouts(unsigned long first, unsigned long second);
    unsigned long GetLayoutsCount();
    [Ref] Layout InsertNewLayout([Const] DOMString name, unsigned long position);
    void RemoveLayout([Const] DOMString name);
    void SetFirstLayout([Const] DOMString name);
    [Const, Ref] DOMString GetFirstLayout();
    unsigned long GetLayoutPosition([Const] DOMString name);

    boolean HasExternalEventsNamed([Const] DOMString name);
    [Ref] ExternalEvents GetExternalEvents([Const] DOMString name);
    [Ref] ExternalEvents GetExternalEventsAt(unsigned long index);
    void MoveExternalEvents(unsigned long oldIndex, unsigned long newIndex);
    void SwapExternalEvents(unsigned long first, unsigned long second);
    unsigned long GetExternalEventsCount();
    [Ref] ExternalEvents InsertNewExternalEvents([Const] DOMString name, unsigned long position);
    void RemoveExternalEvents([Const] DOMString name);
    unsigned long GetExternalEventsPosition([Const] DOMString name);

    boolean HasExternalLayoutNamed([Const] DOMString name);
    [Ref] ExternalLayout GetExternalLayout([Const] DOMString name);
    [Ref] ExternalLayout GetExternalLayoutAt(unsigned long index);
    void MoveExternalLayout(unsigned long oldIndex, unsigned long newIndex);
    void SwapExternalLayouts(unsigned long first, unsigned long second);
    unsigned long GetExternalLayoutsCount();
    [Ref] ExternalLayout InsertNewExternalLayout([Const] DOMString name, unsigned long position);
    void RemoveExternalLayout([Const] DOMString name);
    unsigned long GetExternalLayoutPosition([Const] DOMString name);

    boolean HasEventsFunctionsExtensionNamed([Const] DOMString name);
    [Ref] EventsFunctionsExtension GetEventsFunctionsExtension([Const] DOMString name);
    [Ref] EventsFunctionsExtension GetEventsFunctionsExtensionAt(unsigned long index);
    void MoveEventsFunctionsExtension(unsigned long oldIndex, unsigned long newIndex);
    void SwapEventsFunctionsExtensions(unsigned long first, unsigned long second);
    unsigned long GetEventsFunctionsExtensionsCount();
    [Ref] EventsFunctionsExtension InsertNewEventsFunctionsExtension([Const] DOMString name, unsigned long position);
    [Ref] EventsFunctionsExtension InsertEventsFunctionsExtension([Const, Ref] EventsFunctionsExtension eventsFunctionsExtension, unsigned long position);
    void RemoveEventsFunctionsExtension([Const] DOMString name);
    unsigned long GetEventsFunctionsExtensionPosition([Const] DOMString name);
    void UnserializeAndInsertExtensionsFrom([Const, Ref] SerializerElement eventsFunctionsExtensionsElement);

    boolean HasEventsBasedBehavior([Const] DOMString type);
    [Ref] EventsBasedBehavior GetEventsBasedBehavior([Const] DOMString type);
    boolean HasEventsBasedObject([Const] DOMString type);
    [Ref] EventsBasedObject GetEventsBasedObject([Const] DOMString type);

    [Ref] VariablesContainer GetVariables();
    [Ref] ObjectsContainer GetObjects();
    [Ref] ResourcesManager GetResourcesManager();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);

    [Ref] WholeProjectDiagnosticReport GetWholeProjectDiagnosticReport();

    boolean STATIC_IsNameSafe([Const] DOMString name);
    [Const, Value] DOMString STATIC_GetSafeName([Const] DOMString name);
};

enum ObjectsContainersList_VariableExistence {
    "ObjectsContainersList::DoesNotExist",
    "ObjectsContainersList::Exists",
    "ObjectsContainersList::GroupIsEmpty",
    "ObjectsContainersList::ExistsOnlyOnSomeObjectsOfTheGroup"
};

interface ObjectsContainersList {
  [Value] ObjectsContainersList STATIC_MakeNewObjectsContainersListForProjectAndLayout(
        [Const, Ref] Project project,
        [Const, Ref] Layout layout);
  [Value] ObjectsContainersList STATIC_MakeNewObjectsContainersListForContainers(
        [Const, Ref] ObjectsContainer globalObjectsContainer,
        [Const, Ref] ObjectsContainer objectsContainer);

  [Const, Value] DOMString GetTypeOfObject([Const] DOMString objectName);
  [Const, Value] DOMString GetTypeOfBehavior([Const] DOMString name, boolean searchInGroups);
  [Value] VectorString GetBehaviorsOfObject([Const] DOMString objectOrGroupName, boolean searchInGroups);
  [Value] VectorString GetBehaviorNamesInObjectOrGroup(
    [Const] DOMString objectOrGroupName,
    [Const] DOMString behaviorType,
    boolean searchInGroups);
  [Value] VectorString GetAnimationNamesOfObject([Const] DOMString name);
  [Const, Value] DOMString GetTypeOfBehaviorInObjectOrGroup([Const] DOMString objectOrGroupName, [Const] DOMString behaviorName, boolean searchInGroups);
  boolean HasObjectOrGroupNamed([Const] DOMString name);
  boolean HasObjectNamed([Const] DOMString name);
  ObjectsContainersList_VariableExistence HasObjectOrGroupWithVariableNamed([Const] DOMString objectName, [Const] DOMString variableName);
  ObjectsContainer_SourceType GetObjectsContainerSourceType(
    [Const] DOMString objectOrGroupName);

  [Const, Ref] ObjectsContainer GetObjectsContainer(unsigned long index);
  unsigned long GetObjectsContainersCount();
};

interface ProjectScopedContainers {
  [Value] ProjectScopedContainers STATIC_MakeNewProjectScopedContainersForProjectAndLayout(
        [Const, Ref] Project project,
        [Const, Ref] Layout layout);

  [Value] ProjectScopedContainers STATIC_MakeNewProjectScopedContainersForProject(
        [Const, Ref] Project project);

  [Value] ProjectScopedContainers STATIC_MakeNewProjectScopedContainersForEventsFunctionsExtension(
        [Const, Ref] Project project,
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension);

  [Value] ProjectScopedContainers STATIC_MakeNewProjectScopedContainersForFreeEventsFunction(
        [Const, Ref] Project project,
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
        [Const, Ref] EventsFunction eventsFunction,
        [Ref] ObjectsContainer parameterObjectsContainer,
        [Ref] VariablesContainer parameterVariablesContainer);

  [Value] ProjectScopedContainers STATIC_MakeNewProjectScopedContainersForBehaviorEventsFunction(
        [Const, Ref] Project project,
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
        [Const, Ref] EventsBasedBehavior eventsBasedBehavior,
        [Const, Ref] EventsFunction eventsFunction,
        [Ref] ObjectsContainer parameterObjectsContainer,
        [Ref] VariablesContainer parameterVariablesContainer,
        [Ref] VariablesContainer propertyVariablesContainer);

  [Value] ProjectScopedContainers STATIC_MakeNewProjectScopedContainersForObjectEventsFunction(
        [Const, Ref] Project project,
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
        [Const, Ref] EventsBasedObject eventsBasedObject,
        [Const, Ref] EventsFunction eventsFunction,
        [Ref] ObjectsContainer parameterObjectsContainer,
        [Ref] VariablesContainer parameterVariablesContainer,
        [Ref] VariablesContainer propertyVariablesContainer);

  [Value] ProjectScopedContainers STATIC_MakeNewProjectScopedContainersForEventsBasedObject(
        [Const, Ref] Project project,
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
        [Const, Ref] EventsBasedObject eventsBasedObject,
        [Ref] ObjectsContainer outputObjectsContainer);

  [Value] ProjectScopedContainers STATIC_MakeNewProjectScopedContainersWithLocalVariables(
        [Const, Ref] ProjectScopedContainers projectScopedContainers,
        [Const, Ref] BaseEvent event);

  [Ref] ProjectScopedContainers AddPropertiesContainer(
        [Const, Ref] PropertiesContainer propertiesContainer);

  [Ref] ProjectScopedContainers AddParameters(
        [Const, Ref] ParameterMetadataContainer parameters);

  [Const, Ref] ObjectsContainersList GetObjectsContainersList();
  [Const, Ref] VariablesContainersList GetVariablesContainersList();
};

interface ExtensionProperties {
    [Const, Ref] DOMString GetValue([Const] DOMString extension, [Const] DOMString property);
    void SetValue([Const] DOMString extension, [Const] DOMString property, [Const] DOMString newValue);
    boolean HasProperty([Const] DOMString extension, [Const] DOMString property);
    [Value] MapStringPropertyDescriptor GetAllExtensionProperties([Const] DOMString extension, [Ref] Project project);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface Behavior {
    void Behavior();

    Behavior Clone();

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
    [Const, Ref] DOMString GetTypeName();

    [Value] MapStringPropertyDescriptor GetProperties();
    boolean UpdateProperty([Const] DOMString name, [Const] DOMString value);
    void InitializeContent();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);

    boolean IsFolded();
    void SetFolded(boolean folded);

    boolean IsDefaultBehavior();

    [Ref] QuickCustomizationVisibilitiesContainer GetPropertiesQuickCustomizationVisibilities();
};

[JSImplementation=Behavior]
interface BehaviorJsImplementation {
    void BehaviorJsImplementation();

    [Value] MapStringPropertyDescriptor GetProperties([Const, Ref] SerializerElement behaviorContent);
    boolean UpdateProperty([Ref] SerializerElement behaviorContent, [Const] DOMString name, [Const] DOMString value);
    void InitializeContent([Ref] SerializerElement behaviorContent);
};

interface BehaviorsSharedData {
    void BehaviorsSharedData();

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
    [Const, Ref] DOMString GetTypeName();

    [Value] MapStringPropertyDescriptor GetProperties();
    boolean UpdateProperty([Const] DOMString name, [Const] DOMString value);
    void InitializeContent();

    [Ref] QuickCustomizationVisibilitiesContainer GetPropertiesQuickCustomizationVisibilities();
};

[JSImplementation=BehaviorsSharedData]
interface BehaviorSharedDataJsImplementation {
    void BehaviorSharedDataJsImplementation();

    [Value] MapStringPropertyDescriptor GetProperties([Const, Ref] SerializerElement behaviorSharedDataContent);
    boolean UpdateProperty([Ref] SerializerElement behaviorSharedDataContent, [Const] DOMString name, [Const] DOMString value);
    void InitializeContent([Ref] SerializerElement behaviorSharedDataContent);
};

interface ObjectConfiguration {
    void ObjectConfiguration();
    [Value] UniquePtrObjectConfiguration Clone();

    [Const, Ref] DOMString GetType();
    // SetType must only be used by tests.
    void SetType([Const] DOMString typeName);

    [Value] MapStringPropertyDescriptor GetProperties();
    boolean UpdateProperty([Const] DOMString name, [Const] DOMString value);

    [Value] MapStringPropertyDescriptor GetInitialInstanceProperties(
        [Const, Ref] InitialInstance instance);
    boolean UpdateInitialInstanceProperty(
        [Ref] InitialInstance instance,
        [Const] DOMString name,
        [Const] DOMString value);

    void ExposeResources([Ref] ArbitraryResourceWorker worker);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);

    unsigned long GetAnimationsCount();
    [Const, Ref] DOMString GetAnimationName(unsigned long index);
};

interface UniquePtrObjectConfiguration {
    ObjectConfiguration get();
    ObjectConfiguration release();
};

interface gdObject {
    // /!\ We need to call it gdObject to avoid messing up with javascript Object
    //     global in glue.js!
    void gdObject([Const] DOMString name, [Const] DOMString type, ObjectConfiguration configuration);
    [Value] UniquePtrObject Clone();

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
    void SetAssetStoreId([Const] DOMString assetStoreId);
    [Const, Ref] DOMString GetAssetStoreId();
    void SetType([Const] DOMString type);
    [Const, Ref] DOMString GetType();

    [Ref] ObjectConfiguration GetConfiguration();

    [Ref] VariablesContainer GetVariables();
    [Ref] EffectsContainer GetEffects();
    [Value] VectorString GetAllBehaviorNames();
    boolean HasBehaviorNamed([Const] DOMString name);
    Behavior AddNewBehavior([Ref] Project project, [Const] DOMString type, [Const] DOMString name);
    [Ref] Behavior GetBehavior([Const] DOMString name);
    void RemoveBehavior([Const] DOMString name);
    boolean RenameBehavior([Const] DOMString oldName, [Const] DOMString name);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);
    [Ref] gdObject ResetPersistentUuid();
    [Ref] gdObject ClearPersistentUuid();
};

interface UniquePtrObject {
    gdObject get();
    gdObject release();
};

[JSImplementation=ObjectConfiguration]
interface ObjectJsImplementation {
    void ObjectJsImplementation();
    [Value] UniquePtrObjectConfiguration Clone();

    [Value] MapStringPropertyDescriptor GetProperties();
    boolean UpdateProperty([Const] DOMString name, [Const] DOMString value);

    [Value] MapStringPropertyDescriptor GetInitialInstanceProperties(
        [Const, Ref] InitialInstance instance);
    boolean UpdateInitialInstanceProperty(
        [Ref] InitialInstance instance,
        [Const] DOMString name,
        [Const] DOMString value);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);

    // Not exposed as protected:
    // void DoSerializeTo([Ref] SerializerElement element);
    // void DoUnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);
};

enum CustomObjectConfiguration_EdgeAnchor {
    "CustomObjectConfiguration::NoAnchor",
    "CustomObjectConfiguration::MinEdge",
    "CustomObjectConfiguration::MaxEdge",
    "CustomObjectConfiguration::Proportional",
    "CustomObjectConfiguration::Center",
};

interface CustomObjectConfiguration {
    [Value] UniquePtrObjectConfiguration Clone();

    [Const, Ref] DOMString GetVariantName();
    void SetVariantName([Const] DOMString name);
    boolean IsForcedToOverrideEventsBasedObjectChildrenConfiguration();
    boolean IsMarkedAsOverridingEventsBasedObjectChildrenConfiguration();
    void SetMarkedAsOverridingEventsBasedObjectChildrenConfiguration(
        boolean isOverridingEventsBasedObjectChildrenConfiguration);

    void ClearChildrenConfiguration();
    [Ref] ObjectConfiguration GetChildObjectConfiguration([Const] DOMString objectName);

    [Value] MapStringPropertyDescriptor GetProperties();
    boolean UpdateProperty([Const] DOMString name, [Const] DOMString value);

    [Value] MapStringPropertyDescriptor GetInitialInstanceProperties(
        [Const, Ref] InitialInstance instance);
    boolean UpdateInitialInstanceProperty(
        [Ref] InitialInstance instance,
        [Const] DOMString name, [Const]
        DOMString value);

    [Ref] SpriteAnimationList GetAnimations();

    boolean IsChildObjectFolded([Const] DOMString childName);
    void SetChildObjectFolded([Const] DOMString childName, boolean folded);

    CustomObjectConfiguration_EdgeAnchor STATIC_GetEdgeAnchorFromString([Const] DOMString value);
};
CustomObjectConfiguration implements ObjectConfiguration;

interface Layout {
    void Layout();

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
    void SetBackgroundColor(unsigned long r, unsigned long g, unsigned long b);
    unsigned long GetBackgroundColorRed();
    unsigned long GetBackgroundColorGreen();
    unsigned long GetBackgroundColorBlue();
    void SetWindowDefaultTitle([Const] DOMString name);
    [Const, Ref] DOMString GetWindowDefaultTitle();
    [Ref] InitialInstancesContainer GetInitialInstances();
    [Ref] VariablesContainer GetVariables();
    [Ref] ObjectsContainer GetObjects();
    [Ref] EventsList GetEvents();
    [Ref] LayersContainer GetLayers();

    void UpdateBehaviorsSharedData([Ref] Project project);
    [Value] VectorString GetAllBehaviorSharedDataNames();
    boolean HasBehaviorSharedData([Const] DOMString behaviorName);
    [Ref] BehaviorsSharedData GetBehaviorSharedData([Const] DOMString behaviorName);

    // Deprecated (use GetLayers instead):
    void InsertNewLayer([Const] DOMString name, unsigned long position);
    void InsertLayer([Const, Ref] Layer layer, unsigned long position);
    [Ref] Layer GetLayer([Const] DOMString name);
    [Ref] Layer GetLayerAt(unsigned long pos);
    boolean HasLayerNamed([Const] DOMString name);
    void RemoveLayer([Const] DOMString name);
    unsigned long GetLayersCount();
    void SwapLayers(unsigned long firstLayerIndex, unsigned long secondLayerIndex);
    void MoveLayer(unsigned long oldIndex, unsigned long newIndex);

    [Ref] EditorSettings GetAssociatedEditorSettings();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);

    void SetStopSoundsOnStartup(boolean enable);
    boolean StopSoundsOnStartup();
};

interface ExternalEvents {
    void ExternalEvents();

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();

    [Const, Ref] DOMString GetAssociatedLayout();
    void SetAssociatedLayout([Const] DOMString name);

    [Ref] EventsList GetEvents();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);
};

interface ExternalLayout {
    void ExternalLayout();

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();

    void SetAssociatedLayout([Const] DOMString name);
    [Const, Ref] DOMString GetAssociatedLayout();

    [Ref] InitialInstancesContainer GetInitialInstances();
    [Ref] EditorSettings GetAssociatedEditorSettings();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface Effect {
  void Effect();

  void SetName([Const] DOMString name_);
  [Const, Ref] DOMString GetName();

  void SetEffectType([Const] DOMString effectType_);
  [Const, Ref] DOMString GetEffectType();

  void SetFolded(boolean val);
  boolean IsFolded();

  void SetDoubleParameter([Const] DOMString name, double value);
  double GetDoubleParameter([Const] DOMString name);
  void SetStringParameter([Const] DOMString name, [Const] DOMString value);
  [Const, Ref] DOMString GetStringParameter([Const] DOMString name);
  void SetBooleanParameter([Const] DOMString name, boolean value);
  boolean GetBooleanParameter([Const] DOMString name);
  [Const, Ref] MapStringDouble GetAllDoubleParameters();
  [Const, Ref] MapStringString GetAllStringParameters();
  [Const, Ref] MapStringBoolean GetAllBooleanParameters();
  void ClearParameters();

  void SerializeTo([Ref] SerializerElement element);
  void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface EffectsContainer {
  void EffectsContainer();

  boolean HasEffectNamed([Const] DOMString name);
  [Ref] Effect GetEffect([Const] DOMString name);
  [Ref] Effect GetEffectAt(unsigned long index);
  unsigned long GetEffectPosition([Const] DOMString name);
  unsigned long GetEffectsCount();
  [Ref] Effect InsertNewEffect([Const] DOMString name, unsigned long position);
  void InsertEffect([Const, Ref] Effect theEffect, unsigned long position);
  void RemoveEffect([Const] DOMString name);
  void SwapEffects(unsigned long firstEffectIndex, unsigned long secondEffectIndex);
  void MoveEffect(unsigned long oldIndex, unsigned long newIndex);
  void Clear();

  void SerializeTo([Ref] SerializerElement element);
  void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface Layer {
    void Layer();
    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
    void SetRenderingType([Const] DOMString renderingType);
    [Const, Ref] DOMString GetRenderingType();
    void SetCameraType([Const] DOMString cameraType);
    [Const, Ref] DOMString GetCameraType();
    void SetDefaultCameraBehavior([Const] DOMString defaultCameraBehavior);
    [Const, Ref] DOMString GetDefaultCameraBehavior();
    void SetVisibility(boolean visible);
    boolean GetVisibility();
    void SetLocked(boolean isLocked);
    boolean IsLocked();
    void SetLightingLayer(boolean lightingLayer);
    boolean IsLightingLayer();
    void SetFollowBaseLayerCamera(boolean followBaseLayerCamera);
    boolean IsFollowingBaseLayerCamera();
    double GetCamera3DNearPlaneDistance();
    void SetCamera3DNearPlaneDistance(double distance);
    double GetCamera3DFarPlaneDistance();
    void SetCamera3DFarPlaneDistance(double distance);
    double GetCamera3DFieldOfView();
    void SetCamera3DFieldOfView(double angle);

    void SetAmbientLightColor(unsigned long r, unsigned long g, unsigned long b);
    unsigned long GetAmbientLightColorRed();
    unsigned long GetAmbientLightColorGreen();
    unsigned long GetAmbientLightColorBlue();

    [Ref] EffectsContainer GetEffects();

    // Note that for now, cameras are not used in the editor (and not supported in the game engine).
    // Just exposing the count to ensure we can test it.
    unsigned long GetCameraCount();
    void SetCameraCount(unsigned long cameraCount);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface LayersContainer {
    void InsertNewLayer([Const] DOMString name, unsigned long position);
    void InsertLayer([Const, Ref] Layer layer, unsigned long position);
    [Ref] Layer GetLayer([Const] DOMString name);
    [Ref] Layer GetLayerAt(unsigned long pos);
    boolean HasLayerNamed([Const] DOMString name);
    void RemoveLayer([Const] DOMString name);
    unsigned long GetLayersCount();
    void SwapLayers(unsigned long firstLayerIndex, unsigned long secondLayerIndex);
    void MoveLayer(unsigned long oldIndex, unsigned long newIndex);
    void SerializeLayersTo([Ref] SerializerElement element);
    void UnserializeLayersFrom([Const, Ref] SerializerElement element);
};

interface PropertyDescriptor {
    void PropertyDescriptor([Const] DOMString propValue);

    [Ref] PropertyDescriptor SetValue([Const] DOMString value);
    [Const, Ref] DOMString GetValue();
    [Ref] PropertyDescriptor SetType([Const] DOMString type);
    [Const, Ref] DOMString GetType();
    [Ref] PropertyDescriptor SetLabel([Const] DOMString label);
    [Const, Ref] DOMString GetLabel();
    [Ref] PropertyDescriptor SetDescription([Const] DOMString label);
    [Const, Ref] DOMString GetDescription();
    [Ref] PropertyDescriptor SetGroup([Const] DOMString label);
    [Const, Ref] DOMString GetGroup();
    [Ref] PropertyDescriptor AddExtraInfo([Const] DOMString type);
    [Ref] PropertyDescriptor SetExtraInfo([Const, Ref] VectorString info);
    [Ref] VectorString GetExtraInfo();
    [Ref] PropertyDescriptor SetHidden(boolean enable);
    boolean IsHidden();
    [Ref] PropertyDescriptor SetDeprecated(boolean enable);
    boolean IsDeprecated();
    [Ref] PropertyDescriptor SetAdvanced(boolean enable);
    boolean IsAdvanced();
    [Const, Ref] MeasurementUnit GetMeasurementUnit();
    [Ref] PropertyDescriptor SetMeasurementUnit([Const, Ref] MeasurementUnit measurementUnit);

    boolean HasImpactOnOtherProperties();
    [Ref] PropertyDescriptor SetHasImpactOnOtherProperties(boolean enable);

    QuickCustomization_Visibility GetQuickCustomizationVisibility();
    [Ref] PropertyDescriptor SetQuickCustomizationVisibility(QuickCustomization_Visibility visibility);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
    void SerializeValuesTo([Ref] SerializerElement element);
    void UnserializeValuesFrom([Const, Ref] SerializerElement element);
};

interface MeasurementUnit {
  void MeasurementUnit(DOMString name, DOMString label, DOMString description);

  [Const, Ref] DOMString GetName();
  [Const, Ref] DOMString GetLabel();
  [Const, Ref] DOMString GetDescription();
  [Const, Ref] DOMString GetElementsWithWords();

  long GetElementsCount();
  long GetElementPower(long elementIndex);
  [Const, Ref] MeasurementBaseUnit GetElementBaseUnit(long elementIndex);

  boolean IsUndefined();

  void STATIC_ApplyTranslation();

  [Const, Ref] MeasurementUnit STATIC_GetUndefined();
  [Const, Ref] MeasurementUnit STATIC_GetDimensionless();
  [Const, Ref] MeasurementUnit STATIC_GetDegreeAngle();
  [Const, Ref] MeasurementUnit STATIC_GetSecond();
  [Const, Ref] MeasurementUnit STATIC_GetPixel();
  [Const, Ref] MeasurementUnit STATIC_GetPixelSpeed();
  [Const, Ref] MeasurementUnit STATIC_GetPixelAcceleration();
  [Const, Ref] MeasurementUnit STATIC_GetAngularSpeed();
  [Const, Ref] MeasurementUnit STATIC_GetNewton();

  long STATIC_GetDefaultMeasurementUnitsCount();
  [Const, Ref] MeasurementUnit STATIC_GetDefaultMeasurementUnitAtIndex(long index);
  [Const, Ref] MeasurementUnit STATIC_GetDefaultMeasurementUnitByName([Const] DOMString name);
  boolean STATIC_HasDefaultMeasurementUnitNamed([Const] DOMString name);
};

interface MeasurementBaseUnit {
  void MeasurementBaseUnit(DOMString name, DOMString symbol, DOMString quantity);

  [Const, Ref] DOMString GetName();
  [Const, Ref] DOMString GetSymbol();
  [Const, Ref] DOMString GetQuantity();
};

interface NamedPropertyDescriptor {
    void NamedPropertyDescriptor();

    [Ref] PropertyDescriptor SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
};
NamedPropertyDescriptor implements PropertyDescriptor;

interface MapStringPropertyDescriptor {
    void MapStringPropertyDescriptor();

    [Ref] PropertyDescriptor MAP_getOrCreate([Const] DOMString name);
    [Ref] PropertyDescriptor MAP_get([Const] DOMString name);
    void MAP_set([Const] DOMString name, [Const, Ref] PropertyDescriptor prop);
    boolean MAP_has([Const] DOMString name);
    [Value] VectorString MAP_keys();
};

interface MapStringSerializerValue {
    void MapStringSerializerValue();

    [Ref] SerializerValue MAP_getOrCreate([Const] DOMString name);
    [Ref] SerializerValue MAP_get([Const] DOMString name);
    void MAP_set([Const] DOMString name, [Const, Ref] SerializerValue prop);
    boolean MAP_has([Const] DOMString name);
    [Value] VectorString MAP_keys();
};

interface VectorPairStringSharedPtrSerializerElement {
    unsigned long size();
    [Const, Ref] DOMString WRAPPED_GetString(unsigned long id);
    [Value] SharedPtrSerializerElement WRAPPED_GetSharedPtrSerializerElement(unsigned long id);
};

interface Resource {
    void Resource();

    Resource Clone();
    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
    void SetKind([Const] DOMString kind);
    [Const, Ref] DOMString GetKind();
    boolean IsUserAdded();
    void SetUserAdded(boolean yes);
    boolean UseFile();
    void SetFile([Const] DOMString file);
    [Const, Ref] DOMString GetFile();
    void SetMetadata([Const] DOMString metadata);
    [Const, Ref] DOMString GetMetadata();
    void SetOrigin([Const] DOMString originName, [Const] DOMString originIdentifier);
    [Const, Ref] DOMString GetOriginName();
    [Const, Ref] DOMString GetOriginIdentifier();

    [Value] MapStringPropertyDescriptor GetProperties();
    boolean UpdateProperty([Const] DOMString name, [Const] DOMString value);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface ResourcesManager {
    void ResourcesManager();

    [Value] VectorString GetAllResourceNames();
    [Value] VectorString FindFilesNotInResources([Const, Ref] VectorString filesToCheck);
    boolean HasResource([Const] DOMString name);
    [Const, Ref] Resource GetResource([Const] DOMString name);
    [Const, Ref] DOMString GetResourceNameWithOrigin([Const] DOMString originName, [Const] DOMString originIdentifier);
    [Const, Ref] DOMString GetResourceNameWithFile([Const] DOMString file);
    boolean AddResource([Const, Ref] Resource res);
    void RemoveResource([Const] DOMString name);
    void RenameResource([Const] DOMString oldName, [Const] DOMString name);
    unsigned long GetResourcePosition([Const] DOMString name);
    boolean MoveResourceUpInList([Const] DOMString oldName);
    boolean MoveResourceDownInList([Const] DOMString oldName);
    void MoveResource(unsigned long oldIndex, unsigned long newIndex);
};

interface ImageResource {
    void ImageResource();

    boolean IsSmooth();
    void SetSmooth(boolean enable);
};
ImageResource implements Resource;

interface AudioResource {
    void AudioResource();
};
AudioResource implements Resource;

interface FontResource {
    void FontResource();
};
FontResource implements Resource;

interface BitmapFontResource {
    void BitmapFontResource();
};
BitmapFontResource implements Resource;

interface VideoResource {
    void VideoResource();
};
VideoResource implements Resource;

interface JsonResource {
    void JsonResource();
};
JsonResource implements Resource;

interface SpineResource {
    void SpineResource();
};
SpineResource implements JsonResource;

interface TilemapResource {
    void TilemapResource();
};
TilemapResource implements Resource;

interface TilesetResource {
    void TilesetResource();
};
TilesetResource implements Resource;

interface Model3DResource {
    void Model3DResource();
};
Model3DResource implements Resource;

interface AtlasResource {
    void AtlasResource();
};
AtlasResource implements Resource;

interface JavaScriptResource {
    void JavaScriptResource();
};
JavaScriptResource implements Resource;

interface InitialInstance {
    void InitialInstance();

    void SetObjectName([Const] DOMString name);
    [Const, Ref] DOMString GetObjectName();

    double GetX();
    void SetX(double x);
    double GetY();
    void SetY(double y);
    double GetZ();
    void SetZ(double z);
    double GetAngle();
    void SetAngle(double angle);
    double GetRotationX();
    void SetRotationX(double rotationX);
    double GetRotationY();
    void SetRotationY(double rotationY);
    boolean IsLocked();
    void SetLocked(boolean lock);
    boolean IsSealed();
    void SetSealed(boolean seal);
    boolean ShouldKeepRatio();
    void SetShouldKeepRatio(boolean keepRatio);
    long GetZOrder();
    void SetZOrder(long zOrder);
    long GetOpacity();
    void SetOpacity(long opacity);
    [Const, Ref] DOMString GetLayer();
    void SetLayer([Const] DOMString layer);

    boolean IsFlippedX();
    void SetFlippedX(boolean flippedX);
    boolean IsFlippedY();
    void SetFlippedY(boolean flippedY);
    boolean IsFlippedZ();
    void SetFlippedZ(boolean flippedZ);

    void SetHasCustomSize(boolean enable);
    boolean HasCustomSize();
    void SetHasCustomDepth(boolean enable);
    boolean HasCustomDepth();

    void SetCustomWidth(double width);
    double GetCustomWidth();
    void SetCustomHeight(double height);
    double GetCustomHeight();
    void SetCustomDepth(double depth);
    double GetCustomDepth();

    [Ref] InitialInstance ResetPersistentUuid();

    void UpdateCustomProperty(
        [Const] DOMString name,
        [Const] DOMString value,
        [Ref] ObjectsContainer globalObjectsContainer,
        [Ref] ObjectsContainer objectsContainer);
    [Value] MapStringPropertyDescriptor GetCustomProperties(
        [Ref] ObjectsContainer globalObjectsContainer,
        [Ref] ObjectsContainer objectsContainer);
    double GetRawDoubleProperty([Const] DOMString name);
    [Const, Ref] DOMString GetRawStringProperty([Const] DOMString name);
    void SetRawDoubleProperty([Const] DOMString name, double value);
    void SetRawStringProperty([Const] DOMString name, [Const] DOMString value);

    [Ref] VariablesContainer GetVariables();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface InitialInstancesContainer {
    void InitialInstancesContainer();
    InitialInstancesContainer Clone();

    unsigned long GetInstancesCount();

    void IterateOverInstances([Ref] InitialInstanceFunctor func);
    void IterateOverInstancesWithZOrdering([Ref] InitialInstanceFunctor func, [Const] DOMString layer);
    void MoveInstancesToLayer([Const] DOMString fromLayer, [Const] DOMString toLayer);
    void RemoveAllInstancesOnLayer([Const] DOMString layer);
    void RemoveInitialInstancesOfObject([Const] DOMString obj);
    boolean HasInstancesOfObject([Const] DOMString objectName);
    boolean IsInstancesCountOfObjectGreaterThan([Const] DOMString objectName, unsigned long minInstanceCount);
    boolean SomeInstancesAreOnLayer([Const] DOMString layer);
    void RenameInstancesOfObject([Const] DOMString oldName, [Const] DOMString newName);
    void RemoveInstance([Const, Ref] InitialInstance inst);
    unsigned long GetLayerInstancesCount([Const] DOMString layerName);

    [Ref] InitialInstance InsertNewInitialInstance();
    [Ref] InitialInstance InsertInitialInstance([Const, Ref] InitialInstance inst);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface HighestZOrderFinder {
    void HighestZOrderFinder();

    void RestrictSearchToLayer([Const] DOMString layer);
    long GetHighestZOrder();
    long GetLowestZOrder();
    void Reset();
    unsigned long GetInstancesCount();
};

interface InitialInstanceFunctor {};
interface InitialInstanceJSFunctorWrapper {};
[JSImplementation=InitialInstanceJSFunctorWrapper]
interface InitialInstanceJSFunctor {
    void InitialInstanceJSFunctor();

    void invoke(InitialInstance instance);
};

interface SerializerValue {
    boolean GetBool();
    [Const, Value] DOMString GetString();
    long GetInt();
    double GetDouble();

    [Const, Value] DOMString GetRawString();

    boolean IsBoolean();
    boolean IsString();
    boolean IsInt();
    boolean IsDouble();
};

interface SerializerElement {
    void SerializerElement();

    // Setting and reading the value:
    void SetBoolValue(boolean value);
    void SetStringValue([Const] DOMString value);
    void SetIntValue(long value);
    void SetDoubleValue(double value);

    [Const, Ref] SerializerValue GetValue();
    boolean GetBoolValue();
    [Const, Value] DOMString GetStringValue();
    long GetIntValue();
    double GetDoubleValue();

    boolean IsValueUndefined();

    // Setting and reading attributes (will be serialized as children in JSON,
    // but the setter/getter are faster to type and offer better semantic compared
    // to AddChild/GetChild):
    [Ref] SerializerElement SetBoolAttribute([Const] DOMString name, boolean value);
    [Ref] SerializerElement SetStringAttribute([Const] DOMString name, [Const] DOMString value);
    [Ref] SerializerElement SetIntAttribute([Const] DOMString name, long value);
    [Ref] SerializerElement SetDoubleAttribute([Const] DOMString name, double value);

    boolean GetBoolAttribute([Const] DOMString name);
    [Const, Value] DOMString GetStringAttribute([Const] DOMString name);
    long GetIntAttribute([Const] DOMString name);
    double GetDoubleAttribute([Const] DOMString name);

    // Use the element as an array:
    void ConsiderAsArray();
    boolean ConsideredAsArray();

    // Use the element as an object ("associative array", "dictionary") or array:
    [Ref] SerializerElement AddChild([Const] DOMString str);
    [Ref] SerializerElement GetChild([Const] DOMString str);
    void WRAPPED_SetChild([Const] DOMString str, [Const, Ref] SerializerElement element);
    boolean HasChild([Const] DOMString str);

    [Const, Ref] VectorPairStringSharedPtrSerializerElement GetAllChildren();
    [Const, Ref] MapStringSerializerValue GetAllAttributes();
};

interface SharedPtrSerializerElement {
    SerializerElement get();
    void reset();
};

interface Serializer {
    [Const, Value] DOMString STATIC_ToJSON([Const, Ref] SerializerElement element);
    [Value] SerializerElement STATIC_FromJSON([Const] DOMString json);
};

interface ObjectAssetSerializer {
    void STATIC_SerializeTo([Ref] Project project, [Const, Ref] gdObject obj,
        [Const] DOMString objectFullName, [Ref] SerializerElement element,
        [Ref] VectorString usedResourceNames);
};

interface InstructionsList {
    void InstructionsList();

    [Ref] Instruction Insert([Const, Ref] Instruction instr, unsigned long pos);
    void InsertInstructions([Const, Ref] InstructionsList list, unsigned long begin, unsigned long end, unsigned long pos);
    unsigned long size();
    void WRAPPED_set(unsigned long index, [Const, Ref] Instruction instr);
    boolean Contains([Const, Ref] Instruction instr/*, boolean recursive*/); //TODO
    [Ref] Instruction Get(unsigned long index);
    void Remove([Const, Ref] Instruction instr);
    void RemoveAt(unsigned long index);
    void Clear();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);
};

interface Instruction {
    void Instruction();

    Instruction CLONE_Instruction();
    void SetType([Const] DOMString type);
    [Const, Ref] DOMString GetType();
    void SetInverted(boolean inverted);
    boolean IsInverted();
    void SetAwaited(boolean awaited);
    boolean IsAwaited();
    void SetParameter(unsigned long id, [Const] DOMString value);
    [Const, Ref] Expression GetParameter(unsigned long id);
    void SetParametersCount(unsigned long count);
    unsigned long GetParametersCount();
    [Ref] InstructionsList GetSubInstructions();
};

interface Expression {
  [Const, Ref] DOMString GetPlainString();
  ExpressionNode GetRootNode();
};

interface VectorPairStringTextFormatting {
    unsigned long size();
    [Const, Ref] DOMString WRAPPED_GetString(unsigned long id);
    [Ref] TextFormatting WRAPPED_GetTextFormatting(unsigned long id);
};

interface TextFormatting {
    unsigned long GetUserData();
};

interface InstructionSentenceFormatter {
    InstructionSentenceFormatter STATIC_Get();
    [Value] VectorPairStringTextFormatting GetAsFormattedText([Const, Ref] Instruction instr, [Const, Ref] InstructionMetadata metadata);
};

interface ParameterOptions {
    [Ref] ParameterOptions SetDescription([Const] DOMString description);
    [Ref] ParameterOptions SetTypeExtraInfo([Const] DOMString typeExtraInfo);

    [Value] ParameterOptions STATIC_MakeNewOptions();
};

interface AbstractFunctionMetadata {
    [Ref] AbstractFunctionMetadata AddParameter([Const] DOMString type,
                                        [Const] DOMString description,
                                        [Const] optional DOMString optionalObjectType,
                                        optional boolean parameterIsOptional);
    [Ref] AbstractFunctionMetadata AddCodeOnlyParameter(
        [Const] DOMString type, [Const] DOMString supplementaryInformation);
    [Ref] AbstractFunctionMetadata SetDefaultValue([Const] DOMString defaultValue);
    [Ref] AbstractFunctionMetadata SetParameterLongDescription([Const] DOMString longDescription);
    [Ref] AbstractFunctionMetadata SetParameterExtraInfo([Const] DOMString extraInfo);

    [Ref] AbstractFunctionMetadata SetHidden();
    [Ref] AbstractFunctionMetadata SetPrivate();

    [Ref] AbstractFunctionMetadata SetFunctionName([Const] DOMString functionName);
    [Ref] AbstractFunctionMetadata SetIncludeFile([Const] DOMString includeFile);
    [Ref] AbstractFunctionMetadata AddIncludeFile([Const] DOMString includeFile);
    [Const, Ref] VectorString GetIncludeFiles();
};

interface InstructionMetadata {
    void InstructionMetadata(); // For tests only.

    [Const, Ref] DOMString GetFullName();
    [Const, Ref] DOMString GetDescription();
    [Const, Ref] DOMString GetSentence();
    [Const, Ref] DOMString GetGroup();
    [Const, Ref] DOMString GetIconFilename();
    [Const, Ref] DOMString GetSmallIconFilename();
    [Const, Ref] DOMString GetHelpPath();
    boolean CanHaveSubInstructions();
    [Ref] ParameterMetadata GetParameter(unsigned long index);
    unsigned long GetParametersCount();
    [Const, Ref] ParameterMetadataContainer GetParameters();
    long GetUsageComplexity();
    boolean IsHidden();
    boolean IsPrivate();
    boolean IsAsync();
    boolean IsOptionallyAsync();
    boolean IsRelevantForLayoutEvents();
    boolean IsRelevantForFunctionEvents();
    boolean IsRelevantForAsynchronousFunctionEvents();
    boolean IsRelevantForCustomObjectEvents();

    [Ref] InstructionMetadata SetCanHaveSubInstructions();
    [Ref] InstructionMetadata SetHelpPath([Const] DOMString helpPath);
    [Ref] InstructionMetadata SetHidden();
    [Ref] InstructionMetadata SetPrivate();
    [Ref] InstructionMetadata SetRelevantForLayoutEventsOnly();
    [Ref] InstructionMetadata SetRelevantForFunctionEventsOnly();
    [Ref] InstructionMetadata SetRelevantForAsynchronousFunctionEventsOnly();
    [Ref] InstructionMetadata SetRelevantForCustomObjectEventsOnly();
    [Ref] InstructionMetadata AddParameter([Const] DOMString type,
                                        [Const] DOMString description,
                                        [Const] optional DOMString optionalObjectType,
                                        optional boolean parameterIsOptional);
    [Ref] InstructionMetadata AddCodeOnlyParameter(
        [Const] DOMString type, [Const] DOMString supplementaryInformation);
    [Ref] InstructionMetadata SetDefaultValue([Const] DOMString defaultValue);
    [Ref] InstructionMetadata SetParameterLongDescription([Const] DOMString longDescription);
    [Ref] InstructionMetadata SetParameterExtraInfo([Const] DOMString extraInfo);

    [Ref] InstructionMetadata UseStandardOperatorParameters([Const] DOMString type, [Const, Ref] ParameterOptions options);
    [Ref] InstructionMetadata UseStandardRelationalOperatorParameters([Const] DOMString type, [Const, Ref] ParameterOptions options);

    [Ref] InstructionMetadata MarkAsSimple();
    [Ref] InstructionMetadata MarkAsAdvanced();
    [Ref] InstructionMetadata MarkAsComplex();

    [Ref] InstructionMetadata GetCodeExtraInformation();

    [Ref] InstructionMetadata SetFunctionName([Const] DOMString functionName_);
    [Ref] InstructionMetadata SetAsyncFunctionName([Const] DOMString functionName_);
    [Const, Ref] DOMString GetFunctionName();
    [Const, Ref] DOMString GetAsyncFunctionName();
    [Ref] InstructionMetadata SetManipulatedType([Const] DOMString type_);
    [Ref] InstructionMetadata SetGetter([Const] DOMString getter);
    [Ref] InstructionMetadata SetMutators([Const, Ref] MapStringString mutators);
    [Ref] InstructionMetadata SetIncludeFile([Const] DOMString includeFile);
    [Ref] InstructionMetadata AddIncludeFile([Const] DOMString includeFile);
    [Const, Ref] VectorString GetIncludeFiles();
};
InstructionMetadata implements AbstractFunctionMetadata;

interface ExpressionMetadata {
    void ExpressionMetadata(
        [Const] DOMString returnType,
        [Const] DOMString extensionNamespace,
        [Const] DOMString name,
        [Const] DOMString fullname,
        [Const] DOMString description,
        [Const] DOMString group,
        [Const] DOMString smallicon);

    [Const, Ref] DOMString GetReturnType();
    [Const, Ref] DOMString GetFullName();
    [Const, Ref] DOMString GetDescription();
    [Const, Ref] DOMString GetGroup();
    [Const, Ref] DOMString GetSmallIconFilename();
    [Const, Ref] DOMString GetHelpPath();
    boolean IsShown();
    boolean IsPrivate();
    boolean IsRelevantForLayoutEvents();
    boolean IsRelevantForFunctionEvents();
    boolean IsRelevantForAsynchronousFunctionEvents();
    boolean IsRelevantForCustomObjectEvents();
    [Ref] ParameterMetadata GetParameter(unsigned long id);
    unsigned long GetParametersCount();
    [Const, Ref] ParameterMetadataContainer GetParameters();

    [Ref] ExpressionMetadata SetHidden();
    [Ref] ExpressionMetadata SetPrivate();
    [Ref] ExpressionMetadata SetRelevantForLayoutEventsOnly();
    [Ref] ExpressionMetadata SetRelevantForFunctionEventsOnly();
    [Ref] ExpressionMetadata SetRelevantForAsynchronousFunctionEventsOnly();
    [Ref] ExpressionMetadata SetRelevantForCustomObjectEventsOnly();
    [Ref] ExpressionMetadata AddParameter(
        [Const] DOMString type,
        [Const] DOMString description,
        [Const] optional DOMString optionalObjectType,
        optional boolean parameterIsOptional);
    [Ref] ExpressionMetadata AddCodeOnlyParameter(
        [Const] DOMString type, [Const] DOMString supplementaryInformation);
    [Ref] ExpressionMetadata SetDefaultValue([Const] DOMString defaultValue);
    [Ref] ExpressionMetadata SetParameterLongDescription([Const] DOMString longDescription);
    [Ref] ExpressionMetadata SetParameterExtraInfo([Const] DOMString extraInfo);

    [Ref] ExpressionMetadata GetCodeExtraInformation();

    [Ref] ExpressionMetadata SetFunctionName([Const] DOMString functionName);
    [Const, Ref] DOMString GetFunctionName();
    [Ref] ExpressionMetadata SetStatic();
    [Ref] ExpressionMetadata SetIncludeFile([Const] DOMString includeFile);
    [Ref] ExpressionMetadata AddIncludeFile([Const] DOMString includeFile);
    [Const, Ref] VectorString GetIncludeFiles();
};
ExpressionMetadata implements AbstractFunctionMetadata;

interface MultipleInstructionMetadata {
    [Ref] MultipleInstructionMetadata AddParameter([Const] DOMString type,
                                        [Const] DOMString description,
                                        [Const] optional DOMString optionalObjectType,
                                        optional boolean parameterIsOptional);
    [Ref] MultipleInstructionMetadata AddCodeOnlyParameter(
        [Const] DOMString type, [Const] DOMString supplementaryInformation);
    [Ref] MultipleInstructionMetadata SetDefaultValue([Const] DOMString defaultValue);
    [Ref] MultipleInstructionMetadata SetParameterLongDescription([Const] DOMString longDescription);
    [Ref] MultipleInstructionMetadata SetParameterExtraInfo([Const] DOMString extraInfo);

    [Ref] MultipleInstructionMetadata UseStandardParameters([Const] DOMString type, [Const, Ref] ParameterOptions options);

    [Ref] MultipleInstructionMetadata SetHidden();

    [Ref] MultipleInstructionMetadata SetFunctionName([Const] DOMString functionName);
    [Ref] MultipleInstructionMetadata SetGetter([Const] DOMString getter);

    [Ref] MultipleInstructionMetadata SetIncludeFile([Const] DOMString includeFile);
    [Ref] MultipleInstructionMetadata AddIncludeFile([Const] DOMString includeFile);
    [Const, Ref] VectorString GetIncludeFiles();

    [Ref] MultipleInstructionMetadata MarkAsSimple();
    [Ref] MultipleInstructionMetadata MarkAsAdvanced();
    [Ref] MultipleInstructionMetadata MarkAsComplex();
    [Ref] MultipleInstructionMetadata SetPrivate();
};
MultipleInstructionMetadata implements AbstractFunctionMetadata;

interface DependencyMetadata {
    void DependencyMetadata();

    [Const, Ref] DOMString GetName();
    [Ref] DependencyMetadata SetName([Const] DOMString name_);
    [Const, Ref] DOMString GetExportName();
    [Ref] DependencyMetadata SetExportName([Const] DOMString exportName_);
    [Const, Ref] DOMString GetVersion();
    [Ref] DependencyMetadata SetVersion([Const] DOMString version_);
    [Const, Ref] DOMString GetDependencyType();
    [Ref] DependencyMetadata SetDependencyType([Const] DOMString dependencyType_);
    [Ref] DependencyMetadata SetExtraSetting([Const] DOMString settingName, [Const, Ref] PropertyDescriptor settingValue);
    [Const, Ref] MapStringPropertyDescriptor GetAllExtraSettings();

    [Ref] DependencyMetadata OnlyIfSomeExtraSettingsNonEmpty();
    [Ref] DependencyMetadata OnlyIfOtherDependencyIsExported([Const] DOMString otherDependency);

    void CopyFrom([Const, Ref] DependencyMetadata dependencyMetadata);
};

interface SourceFileMetadata {
    void SourceFileMetadata();

    [Const, Ref] DOMString GetResourceName();
    [Ref] SourceFileMetadata SetResourceName([Const] DOMString resourceName_);
    [Const, Ref] DOMString GetIncludePosition();
    [Ref] SourceFileMetadata SetIncludePosition([Const] DOMString includePosition_);
};

interface ParameterMetadata {
    void ParameterMetadata();

    [Const, Ref] DOMString GetType();
    [Ref] ParameterMetadata SetType([Const] DOMString type_);
    [Const, Ref] DOMString GetName();
    [Ref] ParameterMetadata SetName([Const] DOMString name_);
    [Const, Ref] DOMString GetExtraInfo();
    [Ref] ParameterMetadata SetExtraInfo([Const] DOMString extraInfo_);
    boolean IsOptional();
    [Ref] ParameterMetadata SetOptional(boolean optional_);
    [Const, Ref] DOMString GetDescription();
    [Ref] ParameterMetadata SetDescription([Const] DOMString description_);
    [Const, Ref] DOMString GetLongDescription();
    [Ref] ParameterMetadata SetLongDescription([Const] DOMString longDescription_);
    boolean IsCodeOnly();
    [Ref] ParameterMetadata SetCodeOnly(boolean codeOnly_);
    [Const, Ref] DOMString GetDefaultValue();
    [Ref] ParameterMetadata SetDefaultValue([Const] DOMString defaultValue_);

    [Ref] ParameterMetadata SetValueTypeMetadata([Const, Ref] ValueTypeMetadata type);
    [Const, Ref] ValueTypeMetadata GetValueTypeMetadata();

    boolean STATIC_IsObject([Const] DOMString param);
    boolean STATIC_IsBehavior([Const] DOMString param);
    boolean STATIC_IsExpression([Const] DOMString type_, [Const] DOMString parameterType);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface ValueTypeMetadata {
    void ValueTypeMetadata();

    [Const, Ref] DOMString GetName();
    [Ref] ValueTypeMetadata SetName([Const] DOMString name_);
    [Const, Ref] DOMString GetExtraInfo();
    [Ref] ValueTypeMetadata SetExtraInfo([Const] DOMString extraInfo_);
    boolean IsOptional();
    [Ref] ValueTypeMetadata SetOptional(boolean optional_);
    [Const, Ref] DOMString GetDefaultValue();
    [Ref] ValueTypeMetadata SetDefaultValue([Const] DOMString defaultValue_);

    boolean IsObject();
    boolean IsBehavior();
    boolean IsNumber();
    boolean IsString();
    boolean IsVariable();

    boolean STATIC_IsTypeObject([Const] DOMString parameterType);
    boolean STATIC_IsTypeBehavior([Const] DOMString parameterType);
    boolean STATIC_IsTypeExpression([Const] DOMString type, [Const] DOMString parameterType);
    [Const, Ref] DOMString STATIC_GetPrimitiveValueType([Const] DOMString parameterType);
    [Const, Ref] DOMString STATIC_ConvertPropertyTypeToValueType([Const] DOMString propertyType);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

interface ParameterMetadataContainer {
    [Ref] ParameterMetadata InsertNewParameter([Const] DOMString name, unsigned long pos);
    [Ref] ParameterMetadata InsertParameter([Const, Ref] ParameterMetadata parameterMetadata, unsigned long pos);
    boolean HasParameterNamed([Const] DOMString name);
    [Ref] ParameterMetadata GetParameter([Const] DOMString name);
    [Ref] ParameterMetadata GetParameterAt(unsigned long pos);
    void RemoveParameter([Const] DOMString name);
    void MoveParameter(unsigned long oldIndex, unsigned long newIndex);
    unsigned long GetParametersCount();
    unsigned long GetParameterPosition([Const, Ref] ParameterMetadata parameterMetadata);
    void ClearParameters();
    [Ref] ParameterMetadata AddNewParameter([Const] DOMString name);
};

interface ParameterMetadataTools {
    void STATIC_ParametersToObjectsContainer(
        [Ref] Project project,
        [Const, Ref] ParameterMetadataContainer parameters,
        [Ref] ObjectsContainer outputObjectsContainer);
    unsigned long STATIC_GetObjectParameterIndexFor(
        [Const, Ref] ParameterMetadataContainer parameters,
        unsigned long parameterIndex);
};

interface ObjectMetadata {
    [Const, Ref] DOMString GetName();
    [Const, Ref] DOMString GetFullName();
    [Const, Ref] DOMString GetDescription();
    [Const, Ref] DOMString GetIconFilename();
    [Const, Ref] DOMString GetHelpPath();
    [Const, Ref] DOMString GetCategoryFullName();

    [Ref] ObjectMetadata SetCategoryFullName([Const] DOMString categoryFullName);

    [Const, Ref] InstructionMetadata AddScopedCondition([Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString sentence,
                                        [Const] DOMString group,
                                        [Const] DOMString icon,
                                        [Const] DOMString smallicon);

    [Const, Ref] InstructionMetadata AddScopedAction([Const] DOMString name,
                                     [Const] DOMString fullname,
                                     [Const] DOMString description,
                                     [Const] DOMString sentence,
                                     [Const] DOMString group,
                                     [Const] DOMString icon,
                                     [Const] DOMString smallicon);

    // Prefer using AddScopedCondition, or even better: AddExpressionAndCondition.
    [Const, Ref] InstructionMetadata AddCondition([Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString sentence,
                                        [Const] DOMString group,
                                        [Const] DOMString icon,
                                        [Const] DOMString smallicon);

    // Prefer using AddScopedAction, or even better: AddExpressionAndConditionAndAction.
    [Const, Ref] InstructionMetadata AddAction([Const] DOMString name,
                                     [Const] DOMString fullname,
                                     [Const] DOMString description,
                                     [Const] DOMString sentence,
                                     [Const] DOMString group,
                                     [Const] DOMString icon,
                                     [Const] DOMString smallicon);

    [Const, Ref] ExpressionMetadata AddExpression([Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString group,
                                        [Const] DOMString smallicon);

    [Const, Ref] ExpressionMetadata AddStrExpression([Const] DOMString name,
                                           [Const] DOMString fullname,
                                           [Const] DOMString description,
                                           [Const] DOMString group,
                                           [Const] DOMString smallicon);

    [Value] MultipleInstructionMetadata AddExpressionAndCondition(
                                        [Const] DOMString type,
                                        [Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString sentenceName,
                                        [Const] DOMString group,
                                        [Const] DOMString icon);

    [Value] MultipleInstructionMetadata AddExpressionAndConditionAndAction(
                                        [Const] DOMString type,
                                        [Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString sentenceName,
                                        [Const] DOMString group,
                                        [Const] DOMString icon);

    [Ref] MapStringInstructionMetadata GetAllActions();
    [Ref] MapStringInstructionMetadata GetAllConditions();
    [Ref] MapStringExpressionMetadata GetAllExpressions();
    [Ref] MapStringExpressionMetadata GetAllStrExpressions();

    [Ref] ObjectMetadata SetIncludeFile([Const] DOMString includeFile);
    [Ref] ObjectMetadata AddIncludeFile([Const] DOMString includeFile);

    [Const, Ref] SetString GetDefaultBehaviors();
    boolean HasDefaultBehavior([Const] DOMString behaviorType);
    [Ref] ObjectMetadata AddDefaultBehavior([Const] DOMString behaviorType);

    boolean IsPrivate();
    [Ref] ObjectMetadata SetPrivate();

    [Ref] ObjectMetadata SetHidden();
    boolean IsHidden();

    [Ref] ObjectMetadata MarkAsRenderedIn3D();
    boolean IsRenderedIn3D();

    [Ref] ObjectMetadata SetOpenFullEditorLabel([Const] DOMString label);
    [Const, Ref] DOMString GetOpenFullEditorLabel();
};

enum QuickCustomization_Visibility {
    "QuickCustomization::Default",
    "QuickCustomization::Visible",
    "QuickCustomization::Hidden",
};

interface QuickCustomization {
    // Nothing, it's just a container for the visibility enum.
};

interface QuickCustomizationVisibilitiesContainer {
    void Set([Const] DOMString name, QuickCustomization_Visibility visibility);
    QuickCustomization_Visibility Get([Const] DOMString name);
};

interface Screenshot {
    long GetDelayTimeInSeconds();
    void SetDelayTimeInSeconds(long delayTimeInSeconds);
    [Const, Ref] DOMString GetSignedUrl();
    void SetSignedUrl([Const] DOMString signedUrl);
    [Const, Ref] DOMString GetPublicUrl();
    void SetPublicUrl([Const] DOMString publicUrl);
};

interface CaptureOptions {
    void AddScreenshot([Const, Ref] Screenshot screenshot);
    void ClearScreenshots();
    [Const, Ref] VectorScreenshot GetScreenshots();
};

interface BehaviorMetadata {
    [Const, Ref] DOMString GetName();
    [Const, Ref] DOMString GetFullName();
    [Const, Ref] DOMString GetDefaultName();
    [Const, Ref] DOMString GetDescription();
    [Const, Ref] DOMString GetGroup();
    [Const, Ref] DOMString GetIconFilename();
    [Const, Ref] DOMString GetHelpPath();

    [Const, Ref] InstructionMetadata AddScopedCondition([Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString sentence,
                                        [Const] DOMString group,
                                        [Const] DOMString icon,
                                        [Const] DOMString smallicon);

    [Const, Ref] InstructionMetadata AddScopedAction([Const] DOMString name,
                                     [Const] DOMString fullname,
                                     [Const] DOMString description,
                                     [Const] DOMString sentence,
                                     [Const] DOMString group,
                                     [Const] DOMString icon,
                                     [Const] DOMString smallicon);

    // Prefer using AddScopedCondition, or even better: AddExpressionAndCondition.
    [Const, Ref] InstructionMetadata AddCondition([Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString sentence,
                                        [Const] DOMString group,
                                        [Const] DOMString icon,
                                        [Const] DOMString smallicon);

    // Prefer using AddScopedAction, or even better: AddExpressionAndConditionAndAction.
    [Const, Ref] InstructionMetadata AddAction([Const] DOMString name,
                                     [Const] DOMString fullname,
                                     [Const] DOMString description,
                                     [Const] DOMString sentence,
                                     [Const] DOMString group,
                                     [Const] DOMString icon,
                                     [Const] DOMString smallicon);

    [Const, Ref] ExpressionMetadata AddExpression([Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString group,
                                        [Const] DOMString smallicon);

    [Const, Ref] ExpressionMetadata AddStrExpression([Const] DOMString name,
                                           [Const] DOMString fullname,
                                           [Const] DOMString description,
                                           [Const] DOMString group,
                                           [Const] DOMString smallicon);

    [Value] MultipleInstructionMetadata AddExpressionAndCondition(
                                        [Const] DOMString type,
                                        [Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString sentenceName,
                                        [Const] DOMString group,
                                        [Const] DOMString icon);

    [Value] MultipleInstructionMetadata AddExpressionAndConditionAndAction(
                                        [Const] DOMString type,
                                        [Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString sentenceName,
                                        [Const] DOMString group,
                                        [Const] DOMString icon);

    [Ref] InstructionMetadata AddDuplicatedAction(
        [Const] DOMString newActionName,
        [Const] DOMString copiedActionName);
    [Ref] InstructionMetadata AddDuplicatedCondition(
        [Const] DOMString newConditionName,
        [Const] DOMString copiedConditionName);
    [Ref] ExpressionMetadata AddDuplicatedExpression(
        [Const] DOMString newExpressionName,
        [Const] DOMString copiedExpressionName);
    [Ref] ExpressionMetadata AddDuplicatedStrExpression(
        [Const] DOMString newExpressionName,
        [Const] DOMString copiedExpressionName);

    [Ref] MapStringInstructionMetadata GetAllActions();
    [Ref] MapStringInstructionMetadata GetAllConditions();
    [Ref] MapStringExpressionMetadata GetAllExpressions();
    [Ref] MapStringExpressionMetadata GetAllStrExpressions();

    [Ref] BehaviorMetadata SetIncludeFile([Const] DOMString includeFile);
    [Ref] BehaviorMetadata AddIncludeFile([Const] DOMString includeFile);
    [Ref] BehaviorMetadata AddRequiredFile([Const] DOMString resourceFile);

    [Ref] BehaviorMetadata SetObjectType([Const] DOMString objectType);
    [Const, Ref] DOMString GetObjectType();
    [Const, Ref] VectorString GetRequiredBehaviorTypes();

    boolean IsPrivate();
    [Ref] BehaviorMetadata SetPrivate();

    boolean IsHidden();
    [Ref] BehaviorMetadata SetHidden();

    QuickCustomization_Visibility GetQuickCustomizationVisibility();
    [Ref] BehaviorMetadata SetQuickCustomizationVisibility(QuickCustomization_Visibility visibility);

    [Ref] BehaviorMetadata SetOpenFullEditorLabel([Const] DOMString label);
    [Const, Ref] DOMString GetOpenFullEditorLabel();

    [Ref] Behavior Get();
    BehaviorsSharedData GetSharedDataInstance();

    [Value] MapStringPropertyDescriptor GetProperties();
    [Value] MapStringPropertyDescriptor GetSharedProperties();
};

interface EffectMetadata {
    [Ref] EffectMetadata SetFullName([Const] DOMString fullName);
    [Ref] EffectMetadata SetDescription([Const] DOMString description);
    [Ref] EffectMetadata SetHelpPath([Const] DOMString helpPath);
    [Ref] EffectMetadata SetIncludeFile([Const] DOMString includeFile);
    [Ref] EffectMetadata AddIncludeFile([Const] DOMString includeFile);
    [Ref] EffectMetadata MarkAsNotWorkingForObjects();
    [Ref] EffectMetadata MarkAsOnlyWorkingFor2D();
    [Ref] EffectMetadata MarkAsOnlyWorkingFor3D();
    [Ref] EffectMetadata MarkAsUnique();

    [Const, Ref] DOMString GetType();
    [Const, Ref] DOMString GetFullName();
    [Const, Ref] DOMString GetDescription();
    [Const, Ref] DOMString GetHelpPath();
    boolean IsMarkedAsNotWorkingForObjects();
    boolean IsMarkedAsOnlyWorkingFor2D();
    boolean IsMarkedAsOnlyWorkingFor3D();
    boolean IsMarkedAsUnique();

    [Ref] MapStringPropertyDescriptor GetProperties();
};

interface EventMetadata {
    [Const, Ref] DOMString GetFullName();
    [Const, Ref] DOMString GetDescription();
    [Const, Ref] DOMString GetGroup();
};

interface PlatformExtension {
    void PlatformExtension();
    [Ref] PlatformExtension SetExtensionInformation([Const] DOMString name,
                                [Const] DOMString fullname,
                                [Const] DOMString description,
                                [Const] DOMString author,
                                [Const] DOMString license);
    [Ref] PlatformExtension SetExtensionHelpPath([Const] DOMString helpPath);
    [Ref] PlatformExtension SetIconUrl([Const] DOMString iconUrl);
    [Ref] PlatformExtension SetCategory([Const] DOMString category);
    [Ref] InstructionOrExpressionGroupMetadata AddInstructionOrExpressionGroupMetadata([Const] DOMString name);
    void MarkAsDeprecated();

    [Const, Ref] VectorString GetTags();
    [Ref] PlatformExtension SetTags([Const] DOMString csvTags);

    [Value] MultipleInstructionMetadata AddExpressionAndCondition(
                                        [Const] DOMString type,
                                        [Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString sentenceName,
                                        [Const] DOMString group,
                                        [Const] DOMString icon);

    [Value] MultipleInstructionMetadata AddExpressionAndConditionAndAction(
                                        [Const] DOMString type,
                                        [Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString sentenceName,
                                        [Const] DOMString group,
                                        [Const] DOMString icon);

    [Const, Ref] InstructionMetadata AddCondition([Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString sentence,
                                        [Const] DOMString group,
                                        [Const] DOMString icon,
                                        [Const] DOMString smallicon);

    [Const, Ref] InstructionMetadata AddAction([Const] DOMString name,
                                     [Const] DOMString fullname,
                                     [Const] DOMString description,
                                     [Const] DOMString sentence,
                                     [Const] DOMString group,
                                     [Const] DOMString icon,
                                     [Const] DOMString smallicon);

    [Const, Ref] ExpressionMetadata AddExpression([Const] DOMString name,
                                        [Const] DOMString fullname,
                                        [Const] DOMString description,
                                        [Const] DOMString group,
                                        [Const] DOMString smallicon);

    [Const, Ref] ExpressionMetadata AddStrExpression([Const] DOMString name,
                                           [Const] DOMString fullname,
                                           [Const] DOMString description,
                                           [Const] DOMString group,
                                           [Const] DOMString smallicon);

    [Const, Ref] DependencyMetadata AddDependency();

    [Ref] BehaviorMetadata WRAPPED_AddBehavior([Const] DOMString name,
        [Const] DOMString fullname,
        [Const] DOMString defaultName,
        [Const] DOMString description,
        [Const] DOMString group,
        [Const] DOMString icon24x24,
        [Const] DOMString className,
        Behavior instance,
        BehaviorsSharedData sharedDatasInstance);

    [Ref] ObjectMetadata WRAPPED_AddObject([Const] DOMString name,
        [Const] DOMString fullname,
        [Const] DOMString description,
        [Const] DOMString icon24x24,
        ObjectConfiguration instance);

    [Ref] EffectMetadata AddEffect([Const] DOMString name);

    [Ref] PropertyDescriptor RegisterProperty([Const] DOMString name);

    [Const, Ref] DOMString GetFullName();
    [Const, Ref] DOMString GetName();
    [Const, Ref] DOMString GetCategory();
    [Const, Ref] DOMString GetDescription();
    [Const, Ref] DOMString GetAuthor();
    [Const, Ref] DOMString GetLicense();
    [Const, Ref] DOMString GetHelpPath();
    [Const, Ref] DOMString GetIconUrl();
    [Const, Ref] DOMString GetNameSpace();

    [Ref] InstructionMetadata AddDuplicatedAction(
        [Const] DOMString newActionName,
        [Const] DOMString copiedActionName);
    [Ref] InstructionMetadata AddDuplicatedCondition(
        [Const] DOMString newConditionName,
        [Const] DOMString copiedConditionName);
    [Ref] ExpressionMetadata AddDuplicatedExpression(
        [Const] DOMString newExpressionName,
        [Const] DOMString copiedExpressionName);
    [Ref] ExpressionMetadata AddDuplicatedStrExpression(
        [Const] DOMString newExpressionName,
        [Const] DOMString copiedExpressionName);

    [Value] VectorString GetExtensionObjectsTypes();
    [Value] VectorString GetBehaviorsTypes();
    [Value] VectorString GetExtensionEffectTypes();
    [Ref] ObjectMetadata GetObjectMetadata([Const] DOMString type);
    [Ref] BehaviorMetadata GetBehaviorMetadata([Const] DOMString type);
    [Ref] EffectMetadata GetEffectMetadata([Const] DOMString type);
    [Ref] MapStringEventMetadata GetAllEvents();
    [Ref] MapStringInstructionMetadata GetAllActions();
    [Ref] MapStringInstructionMetadata GetAllConditions();
    [Ref] MapStringExpressionMetadata GetAllExpressions();
    [Ref] MapStringExpressionMetadata GetAllStrExpressions();
    [Ref] MapStringInstructionMetadata GetAllActionsForObject([Const] DOMString objectType);
    [Ref] MapStringInstructionMetadata GetAllConditionsForObject([Const] DOMString objectType);
    [Ref] MapStringExpressionMetadata GetAllExpressionsForObject([Const] DOMString objectType);
    [Ref] MapStringExpressionMetadata GetAllStrExpressionsForObject([Const] DOMString objectType);
    [Ref] MapStringInstructionMetadata GetAllActionsForBehavior([Const] DOMString autoType);
    [Ref] MapStringInstructionMetadata GetAllConditionsForBehavior([Const] DOMString autoType);
    [Ref] MapStringExpressionMetadata GetAllExpressionsForBehavior([Const] DOMString autoType);
    [Ref] MapStringExpressionMetadata GetAllStrExpressionsForBehavior([Const] DOMString autoType);

    [Ref] MapStringPropertyDescriptor GetAllProperties();
    [Ref] VectorDependencyMetadata GetAllDependencies();
    [Ref] VectorSourceFileMetadata GetAllSourceFiles();

    [Const, Value] DOMString STATIC_GetNamespaceSeparator();
    [Const, Value] DOMString STATIC_GetBehaviorFullType(
        [Const] DOMString extensionName, [Const] DOMString behaviorName);
    [Const, Value] DOMString STATIC_GetObjectFullType(
        [Const] DOMString extensionName, [Const] DOMString objectName);
    [Const, Value] DOMString STATIC_GetExtensionFromFullObjectType([Const] DOMString type);
    [Const, Value] DOMString STATIC_GetObjectNameFromFullObjectType([Const] DOMString type);
};

interface EventsList {
    void EventsList();

    [Ref] BaseEvent InsertEvent([Const, Ref] BaseEvent event, unsigned long pos);
    [Ref] BaseEvent InsertNewEvent([Ref] Project project, [Const] DOMString type, unsigned long pos);
    void InsertEvents([Const, Ref] EventsList list, unsigned long begin, unsigned long end, unsigned long pos);
    [Ref] BaseEvent GetEventAt(unsigned long pos);
    void RemoveEventAt(unsigned long pos);
    void RemoveEvent([Const, Ref] BaseEvent event);
    unsigned long GetEventsCount();
    boolean Contains([Const, Ref] BaseEvent event, boolean recursive);
    boolean MoveEventToAnotherEventsList([Const, Ref] BaseEvent eventToMove, [Ref] EventsList newEventsList, unsigned long newPosition);
    boolean IsEmpty();
    void Clear();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);
};


interface BaseEvent {
    void BaseEvent();

    BaseEvent Clone();
    [Const, Ref] DOMString GetType();
    void SetType([Const] DOMString type);
    boolean IsExecutable();

    boolean CanHaveSubEvents();
    boolean HasSubEvents();
    [Ref] EventsList GetSubEvents();

    boolean CanHaveVariables();
    boolean HasVariables();
    [Ref] VariablesContainer GetVariables();

    boolean IsDisabled();
    void SetDisabled(boolean disable);
    boolean IsFolded();
    void SetFolded(boolean folded);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);
};

interface StandardEvent {
    void StandardEvent();

    [Ref] InstructionsList GetConditions();
    [Ref] InstructionsList GetActions();
};
StandardEvent implements BaseEvent;

interface RepeatEvent {
    void RepeatEvent();

    [Ref] InstructionsList GetConditions();
    [Ref] InstructionsList GetActions();
    void SetRepeatExpressionPlainString([Const] DOMString expr);
    [Const, Ref] Expression GetRepeatExpression();
};
RepeatEvent implements BaseEvent;

interface WhileEvent {
    void WhileEvent();

    [Ref] InstructionsList GetConditions();
    [Ref] InstructionsList GetWhileConditions();
    [Ref] InstructionsList GetActions();
};
WhileEvent implements BaseEvent;

interface ForEachEvent {
    void ForEachEvent();

    void SetObjectToPick([Const] DOMString objects);
    [Const, Ref] DOMString GetObjectToPick();
    [Ref] InstructionsList GetConditions();
    [Ref] InstructionsList GetActions();
};
ForEachEvent implements BaseEvent;

interface ForEachChildVariableEvent {
    void ForEachChildVariableEvent();

    [Ref] InstructionsList GetConditions();
    [Ref] InstructionsList GetActions();

    [Const, Ref] DOMString GetIterableVariableName();
    [Const, Ref] DOMString GetKeyIteratorVariableName();
    [Const, Ref] DOMString GetValueIteratorVariableName();
    void SetIterableVariableName([Const] DOMString newName);
    void SetKeyIteratorVariableName([Const] DOMString newName);
    void SetValueIteratorVariableName([Const] DOMString newName);
};
ForEachChildVariableEvent implements BaseEvent;

interface CommentEvent {
    void CommentEvent();

    [Const, Ref] DOMString GetComment();
    void SetComment([Const] DOMString type);

    void SetBackgroundColor(unsigned long r, unsigned long g, unsigned long b);
    unsigned long GetBackgroundColorRed();
    unsigned long GetBackgroundColorGreen();
    unsigned long GetBackgroundColorBlue();

    void SetTextColor(unsigned long r, unsigned long g, unsigned long b);
    unsigned long GetTextColorRed();
    unsigned long GetTextColorGreen();
    unsigned long GetTextColorBlue();
};
CommentEvent implements BaseEvent;

interface GroupEvent {
    void GroupEvent();

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
    void SetBackgroundColor(unsigned long r, unsigned long g, unsigned long b);
    unsigned long GetBackgroundColorR();
    unsigned long GetBackgroundColorG();
    unsigned long GetBackgroundColorB();
    void SetSource([Const] DOMString source);
    [Const, Ref] DOMString GetSource();
    [Ref] VectorString GetCreationParameters();
    unsigned long GetCreationTimestamp();
    void SetCreationTimestamp(unsigned long ts);
};
GroupEvent implements BaseEvent;

interface LinkEvent {
    void LinkEvent();

    void SetTarget([Const] DOMString name);
    [Const, Ref] DOMString GetTarget();

    long GetIncludeConfig();
    void SetIncludeAllEvents();
    void SetIncludeEventsGroup([Const] DOMString source);
    [Const, Ref] DOMString GetEventsGroupName();
    void SetIncludeStartAndEnd(unsigned long start, unsigned long end);
    unsigned long GetIncludeStart();
    unsigned long GetIncludeEnd();
};
LinkEvent implements BaseEvent;

interface EventsRemover {
    void EventsRemover();

    void AddEventToRemove([Ref] BaseEvent eventToRemove);
    void AddInstructionToRemove([Ref] Instruction instructionToRemove);
    void Launch([Ref] EventsList events);
};

interface EventsListUnfolder {
    void STATIC_UnfoldWhenContaining([Ref] EventsList list, [Const, Ref] BaseEvent eventToContain);
    void STATIC_FoldAll([Ref] EventsList list);
    void STATIC_UnfoldToLevel([Ref] EventsList list, [Const] unsigned long maxLevel, [Const] optional unsigned long currentLevel = 0);
};

interface EventsSearchResult {
  boolean IsEventsListValid();
  [Const, Ref] EventsList GetEventsList();
  unsigned long GetPositionInList();
  boolean IsEventValid();
  [Const, Ref] BaseEvent GetEvent();
};

interface VectorEventsSearchResult {
    void VectorEventsSearchResult();

    VectorEventsSearchResult CLONE_VectorEventsSearchResult();
    void push_back([Const, Ref] EventsSearchResult result);
    void resize(unsigned long size);
    unsigned long size();
    [Const, Ref] EventsSearchResult at(unsigned long index);
    void WRAPPED_set(unsigned long index, [Const, Ref] EventsSearchResult result);
    void clear();
};

interface EventsRefactorer {
    void STATIC_RenameObjectInEvents(
        [Const, Ref] Platform platform,
        [Ref] ProjectScopedContainers projectScopedContainers,
        [Ref] EventsList events,
        [Const, Ref] ObjectsContainer targetedObjectsContainer,
        [Const] DOMString oldName,
        [Const] DOMString newName);
    [Value] VectorEventsSearchResult STATIC_ReplaceStringInEvents([Ref] ObjectsContainer project, [Ref] ObjectsContainer layout, [Ref] EventsList events, [Const] DOMString toReplace, [Const] DOMString newString, boolean matchCase, boolean inConditions, boolean inActions,  boolean inEventStrings);
    [Value] VectorEventsSearchResult STATIC_SearchInEvents([Const, Ref] Platform platform, [Ref] EventsList events, [Const] DOMString search, boolean matchCase, boolean inConditions, boolean inActions, boolean inEventStrings, boolean inEventSentences);
};

interface UnfilledRequiredBehaviorPropertyProblem {
  [Const, Ref] Project GetSourceProject();
  [Ref] gdObject GetSourceObject();
  [Ref] Behavior GetSourceBehaviorContent();
  [Const, Ref] DOMString GetSourcePropertyName();
  [Const, Ref] DOMString GetExpectedBehaviorTypeName();
};

interface VectorUnfilledRequiredBehaviorPropertyProblem {
    unsigned long size();
    [Const, Ref] UnfilledRequiredBehaviorPropertyProblem at(unsigned long index);
};

interface ProjectBrowserHelper {
    void STATIC_ExposeProjectEvents([Ref] Project project, [Ref] ArbitraryEventsWorker worker);
    void STATIC_ExposeProjectObjects([Ref] Project project, [Ref] ArbitraryObjectsWorker worker);
};

interface ResourceExposer {
    void STATIC_ExposeWholeProjectResources([Ref] Project project, [Ref] ArbitraryResourceWorker worker);
};

interface VariablesChangeset {
    boolean HasRemovedVariables();
    [Ref] VariablesChangeset ClearRemovedVariables();
};

interface WholeProjectRefactorer {
    [Value] VariablesChangeset STATIC_ComputeChangesetForVariablesContainer(
      [Const, Ref] SerializerElement oldSerializedVariablesContainer,
      [Const, Ref] VariablesContainer newVariablesContainer);
    void STATIC_ApplyRefactoringForVariablesContainer(
      [Ref] Project project,
      [Ref] VariablesContainer newVariablesContainer,
      [Const, Ref] VariablesChangeset changeset,
      [Const, Ref] SerializerElement originalSerializedVariables);
      void STATIC_ApplyRefactoringForObjectVariablesContainer(
      [Ref] Project project,
      [Ref] VariablesContainer objectVariablesContainer,
      [Ref] InitialInstancesContainer initialInstancesContainer,
      [Const] DOMString objectName,
      [Const, Ref] VariablesChangeset changeset,
      [Const, Ref] SerializerElement originalSerializedVariables);
    void STATIC_ApplyRefactoringForGroupVariablesContainer(
      [Ref] Project project,
      [Ref] ObjectsContainer globalObjectsContainer,
      [Ref] ObjectsContainer objectsContainer,
      [Ref] InitialInstancesContainer initialInstancesContainer,
      [Const, Ref] VariablesContainer groupVariablesContainer,
      [Const, Ref] ObjectGroup objectGroup,
      [Const, Ref] VariablesChangeset changeset,
      [Const, Ref] SerializerElement originalSerializedVariables);
    void STATIC_RenameEventsFunctionsExtension(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_UpdateExtensionNameInEventsBasedBehavior(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Ref] EventsBasedBehavior eventsBasedBehavior,
      [Const] DOMString sourceExtensionName);
    void STATIC_UpdateExtensionNameInEventsBasedObject(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Ref] EventsBasedObject eventsBasedObject,
      [Const] DOMString sourceExtensionName);
    void STATIC_RenameEventsFunction(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameBehaviorEventsFunction(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const, Ref] EventsBasedBehavior eventsBasedBehavior,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameObjectEventsFunction(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const, Ref] EventsBasedObject eventsBasedObject,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameParameter(
      [Ref] Project project,
      [Ref] ProjectScopedContainers projectScopedContainers,
      [Ref] EventsFunction eventsFunction,
      [Const, Ref] ObjectsContainer parameterObjectsContainer,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_ChangeParameterType(
      [Ref] Project project,
      [Ref] ProjectScopedContainers projectScopedContainers,
      [Ref] EventsFunction eventsFunction,
      [Const, Ref] ObjectsContainer parameterObjectsContainer,
      [Const] DOMString parameterName);
    void STATIC_MoveEventsFunctionParameter(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const] DOMString functionName,
      unsigned long oldIndex,
      unsigned long newIndex);
    void STATIC_MoveBehaviorEventsFunctionParameter(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const, Ref] EventsBasedBehavior eventsBasedBehavior,
      [Const] DOMString functionName,
      unsigned long oldIndex,
      unsigned long newIndex);
    void STATIC_MoveObjectEventsFunctionParameter(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const, Ref] EventsBasedObject eventsBasedObject,
      [Const] DOMString functionName,
      unsigned long oldIndex,
      unsigned long newIndex);
    void STATIC_RenameEventsBasedBehaviorProperty(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const, Ref] EventsBasedBehavior eventsBasedBehavior,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameEventsBasedBehaviorSharedProperty(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const, Ref] EventsBasedBehavior eventsBasedBehavior,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_ChangeEventsBasedBehaviorPropertyType(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const, Ref] EventsBasedBehavior eventsBasedBehavior,
      [Const] DOMString propertyName);
    void STATIC_RenameEventsBasedObjectProperty(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const, Ref] EventsBasedObject eventsBasedObject,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_ChangeEventsBasedObjectPropertyType(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const, Ref] EventsBasedObject eventsBasedObject,
      [Const] DOMString propertyName);
    void STATIC_RenameEventsBasedBehavior(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_UpdateBehaviorNameInEventsBasedBehavior(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Ref] EventsBasedBehavior eventsBasedBehavior,
      [Const] DOMString sourceBehaviorName);
    void STATIC_RenameEventsBasedObject(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_UpdateObjectNameInEventsBasedObject(
      [Ref] Project project,
      [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Ref] EventsBasedObject eventsBasedObject,
      [Const] DOMString sourceObjectName);
    void STATIC_RenameLayout(
      [Ref] Project project,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameExternalLayout(
      [Ref] Project project,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameExternalEvents(
      [Ref] Project project,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameLayerInScene(
      [Ref] Project project,
      [Ref] Layout scene,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameLayerInEventsBasedObject(
      [Ref] Project project,
      [Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Ref] EventsBasedObject eventsBasedObject,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameLayerEffectInScene(
      [Ref] Project project,
      [Ref] Layout scene,
      [Ref] Layer layer,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameLayerEffectInEventsBasedObject(
      [Ref] Project project,
      [Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Ref] EventsBasedObject eventsBasedObject,
      [Ref] Layer layer,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameObjectAnimationInScene(
      [Ref] Project project,
      [Ref] Layout scene,
      [Ref] gdObject gdObject,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameObjectAnimationInEventsBasedObject(
      [Ref] Project project,
      [Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Ref] EventsBasedObject eventsBasedObject,
      [Ref] gdObject gdObject,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameObjectPointInScene(
      [Ref] Project project,
      [Ref] Layout scene,
      [Ref] gdObject gdObject,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameObjectPointInEventsBasedObject(
      [Ref] Project project,
      [Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Ref] EventsBasedObject eventsBasedObject,
      [Ref] gdObject gdObject,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameObjectEffectInScene(
      [Ref] Project project,
      [Ref] Layout scene,
      [Ref] gdObject gdObject,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_RenameObjectEffectInEventsBasedObject(
      [Ref] Project project,
      [Ref] EventsFunctionsExtension eventsFunctionsExtension,
      [Ref] EventsBasedObject eventsBasedObject,
      [Ref] gdObject gdObject,
      [Const] DOMString oldName,
      [Const] DOMString newName);
    void STATIC_ObjectOrGroupRenamedInScene(
        [Ref] Project project,
        [Ref] Layout scene,
        [Const] DOMString oldName,
        [Const] DOMString newName,
        boolean isObjectGroup);
    void STATIC_ObjectRemovedInScene(
        [Ref] Project project,
        [Ref] Layout scene,
        [Const] DOMString objectName);
    void STATIC_BehaviorsAddedToObjectInScene(
        [Ref] Project project,
        [Ref] Layout scene,
        [Const] DOMString objectName);
    void STATIC_ObjectOrGroupRenamedInEventsFunction(
        [Ref] Project project,
        [Ref] ProjectScopedContainers projectScopedContainers,
        [Ref] EventsFunction eventsFunction,
        [Const, Ref] ObjectsContainer parameterObjectsContainer,
        [Const] DOMString oldName,
        [Const] DOMString newName,
        boolean isObjectGroup);
    void STATIC_ObjectRemovedInEventsFunction(
        [Ref] Project project,
        [Ref] EventsFunction eventsFunction,
        [Const] DOMString objectName);
    void STATIC_ObjectOrGroupRenamedInEventsBasedObject(
        [Ref] Project project,
        [Ref] ProjectScopedContainers projectScopedContainers,
        [Ref] EventsBasedObject eventsBasedObject,
        [Const] DOMString oldName,
        [Const] DOMString newName,
        boolean isObjectGroup);
    void STATIC_ObjectRemovedInEventsBasedObject(
        [Ref] Project project,
        [Ref] EventsBasedObject eventsBasedObject,
        [Const] DOMString objectName);
    void STATIC_GlobalObjectOrGroupRenamed([Ref] Project project, [Const] DOMString oldName, [Const] DOMString newName, boolean isObjectGroup);
    void STATIC_GlobalObjectRemoved(
        [Ref] Project project,
        [Const] DOMString objectName);
    void STATIC_BehaviorsAddedToGlobalObject(
        [Ref] Project project,
        [Const] DOMString objectName);
    [Value] SetString STATIC_GetAllObjectTypesUsingEventsBasedBehavior([Const, Ref] Project project, [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension, [Const, Ref] EventsBasedBehavior eventsBasedBehavior);
    void STATIC_EnsureBehaviorEventsFunctionsProperParameters([Const, Ref] EventsFunctionsExtension eventsFunctionsExtension, [Const, Ref] EventsBasedBehavior eventsBasedBehavior);
    void STATIC_EnsureObjectEventsFunctionsProperParameters([Const, Ref] EventsFunctionsExtension eventsFunctionsExtension, [Const, Ref] EventsBasedObject eventsBasedObject);
    void STATIC_AddBehaviorAndRequiredBehaviors([Ref] Project project, [Ref] gdObject obj, [Const] DOMString behaviorType, [Const] DOMString behaviorName);
    void STATIC_AddRequiredBehaviorsFor([Ref] Project project, [Ref] gdObject obj, [Const] DOMString behaviorName);
    [Value] VectorString STATIC_FindDependentBehaviorNames([Const, Ref] Project project, [Const, Ref] gdObject obj, [Const] DOMString behaviorName);
    [Value] VectorUnfilledRequiredBehaviorPropertyProblem STATIC_FindInvalidRequiredBehaviorProperties([Const, Ref] Project project);
    [Value] VectorString STATIC_GetBehaviorsWithType([Const, Ref] gdObject obj, [Const] DOMString type);
    boolean STATIC_FixInvalidRequiredBehaviorProperties([Ref] Project project);
    void STATIC_RemoveLayerInScene(
        [Ref] Project project,
        [Ref] Layout scene,
        [Const] DOMString layerName);
    void STATIC_MergeLayersInScene(
        [Ref] Project project,
        [Ref] Layout scene,
        [Const] DOMString originLayerName,
        [Const] DOMString targetLayerName);
    void STATIC_RemoveLayerInEventsBasedObject(
        [Ref] EventsBasedObject eventsBasedObject,
        [Const] DOMString layerName);
    void STATIC_MergeLayersInEventsBasedObject(
        [Ref] EventsBasedObject eventsBasedObject,
        [Const] DOMString originLayerName,
        [Const] DOMString targetLayerName);
    unsigned long STATIC_GetLayoutAndExternalLayoutLayerInstancesCount([Ref] Project project, [Ref] Layout layout, [Const] DOMString layerName);
    void STATIC_RenameLeaderboards([Ref] Project project, [Const, Ref] MapStringString leaderboardIdMap);
    [Value] SetString STATIC_FindAllLeaderboardIds([Ref] Project project);
};

interface ObjectTools {
    boolean STATIC_IsBehaviorCompatibleWithObject(
        [Const, Ref] Platform platform,
        [Const] DOMString objectType,
        [Const] DOMString behaviorType);
};

interface EventsBasedObjectDependencyFinder {
  boolean STATIC_IsDependentFromEventsBasedObject(
    [Const, Ref] Project project,
    [Const, Ref] EventsBasedObject eventsBasedObject,
    [Const, Ref] EventsBasedObject dependency);
};

interface PropertyFunctionGenerator {
    void STATIC_GenerateBehaviorGetterAndSetter([Ref] Project project, [Ref] EventsFunctionsExtension extension, [Ref] EventsBasedBehavior eventsBasedBehavior, [Const, Ref] NamedPropertyDescriptor property, boolean isSharedProperties);
    void STATIC_GenerateObjectGetterAndSetter([Ref] Project project, [Ref] EventsFunctionsExtension extension, [Ref] EventsBasedObject eventsBasedObject, [Const, Ref] NamedPropertyDescriptor property);
    boolean STATIC_CanGenerateGetterAndSetter([Const, Ref] AbstractEventsBasedEntity eventsBasedBehavior, [Const, Ref] NamedPropertyDescriptor property);

    void STATIC_GenerateConditionSkeleton([Ref] Project project, [Ref] EventsFunction eventFunction);
};

interface UsedExtensionsResult {
  [Const, Ref] SetString GetUsedExtensions();
};

interface UsedExtensionsFinder {
  [Value] UsedExtensionsResult STATIC_ScanProject([Ref] Project project);
};

interface ExampleExtensionUsagesFinder {
  [Value] SetString STATIC_GetUsedExtensions([Ref] Project project);
};

interface InstructionsCountEvaluator {
  long STATIC_ScanProject([Ref] Project project);
};

interface ExtensionAndBehaviorMetadata {
  [Const, Ref] PlatformExtension GetExtension();
  [Const, Ref] BehaviorMetadata GetMetadata();
};

interface ExtensionAndObjectMetadata {
  [Const, Ref] PlatformExtension GetExtension();
  [Const, Ref] ObjectMetadata GetMetadata();
};

interface ExtensionAndEffectMetadata {
  [Const, Ref] PlatformExtension GetExtension();
  [Const, Ref] EffectMetadata GetMetadata();
};

interface ExtensionAndInstructionMetadata {
  [Const, Ref] PlatformExtension GetExtension();
  [Const, Ref] InstructionMetadata GetMetadata();
};

interface ExtensionAndExpressionMetadata {
  [Const, Ref] PlatformExtension GetExtension();
  [Const, Ref] ExpressionMetadata GetMetadata();
};

interface MetadataProvider {

    [Value] ExtensionAndBehaviorMetadata STATIC_GetExtensionAndBehaviorMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Value] ExtensionAndObjectMetadata STATIC_GetExtensionAndObjectMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Value] ExtensionAndEffectMetadata STATIC_GetExtensionAndEffectMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Value] ExtensionAndInstructionMetadata STATIC_GetExtensionAndActionMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Value] ExtensionAndInstructionMetadata STATIC_GetExtensionAndConditionMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Value] ExtensionAndExpressionMetadata STATIC_GetExtensionAndExpressionMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Value] ExtensionAndExpressionMetadata STATIC_GetExtensionAndObjectExpressionMetadata([Const, Ref] Platform p, [Const] DOMString objectType, [Const] DOMString type);
    [Value] ExtensionAndExpressionMetadata STATIC_GetExtensionAndBehaviorExpressionMetadata([Const, Ref] Platform p, [Const] DOMString autoType, [Const] DOMString type);
    [Value] ExtensionAndExpressionMetadata STATIC_GetExtensionAndStrExpressionMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Value] ExtensionAndExpressionMetadata STATIC_GetExtensionAndObjectStrExpressionMetadata([Const, Ref] Platform p, [Const] DOMString objectType, [Const] DOMString type);
    [Value] ExtensionAndExpressionMetadata STATIC_GetExtensionAndBehaviorStrExpressionMetadata([Const, Ref] Platform p, [Const] DOMString autoType, [Const] DOMString type);
    [Const, Ref] BehaviorMetadata STATIC_GetBehaviorMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Const, Ref] ObjectMetadata STATIC_GetObjectMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Const, Ref] EffectMetadata STATIC_GetEffectMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Const, Ref] InstructionMetadata STATIC_GetActionMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Const, Ref] InstructionMetadata STATIC_GetConditionMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Const, Ref] ExpressionMetadata STATIC_GetExpressionMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Const, Ref] ExpressionMetadata STATIC_GetObjectExpressionMetadata([Const, Ref] Platform p, [Const] DOMString objectType, [Const] DOMString type);
    [Const, Ref] ExpressionMetadata STATIC_GetBehaviorExpressionMetadata([Const, Ref] Platform p, [Const] DOMString autoType, [Const] DOMString type);
    [Const, Ref] ExpressionMetadata STATIC_GetStrExpressionMetadata([Const, Ref] Platform p, [Const] DOMString type);
    [Const, Ref] ExpressionMetadata STATIC_GetObjectStrExpressionMetadata([Const, Ref] Platform p, [Const] DOMString objectType, [Const] DOMString type);
    [Const, Ref] ExpressionMetadata STATIC_GetBehaviorStrExpressionMetadata([Const, Ref] Platform p, [Const] DOMString autoType, [Const] DOMString type);

    boolean STATIC_IsBadExpressionMetadata([Const, Ref] ExpressionMetadata metadata);
    boolean STATIC_IsBadInstructionMetadata([Const, Ref] InstructionMetadata metadata);
    boolean STATIC_IsBadBehaviorMetadata([Const, Ref] BehaviorMetadata metadata);
    boolean STATIC_IsBadObjectMetadata([Const, Ref] ObjectMetadata metadata);
};

enum ProjectDiagnostic_ErrorType {
    "ProjectDiagnostic::UndeclaredVariable",
    "ProjectDiagnostic::MissingBehavior",
    "ProjectDiagnostic::UnknownObject",
    "ProjectDiagnostic::MismatchedObjectType",
};

interface ProjectDiagnostic {
    ProjectDiagnostic_ErrorType GetType();
    [Const, Ref] DOMString GetMessage();
    [Const, Ref] DOMString GetActualValue();
    [Const, Ref] DOMString GetExpectedValue();
    [Const, Ref] DOMString GetObjectName();
};

 interface DiagnosticReport {
    void DiagnosticReport();
    [Const, Ref] ProjectDiagnostic Get(unsigned long index);
    unsigned long Count();
    [Const, Ref] DOMString GetSceneName();
};

interface WholeProjectDiagnosticReport {
    [Const, Ref] DiagnosticReport Get(unsigned long index);
    unsigned long Count();
    boolean HasAnyIssue();
};

interface ExpressionParserError {
    [Const, Ref] DOMString GetMessage();
    unsigned long GetStartPosition();
    unsigned long GetEndPosition();
};

interface VectorExpressionParserError {
    unsigned long size();
    [Const] ExpressionParserError at(unsigned long index);
};

interface ExpressionParser2NodeWorker {
};

interface ExpressionValidator {
    void ExpressionValidator(
        [Const, Ref] Platform platform,
        [Const, Ref] ProjectScopedContainers projectScopedContainers,
        [Const] DOMString rootType,
        [Const] DOMString extraInfo);

    [Const, Ref] VectorExpressionParserError GetAllErrors();
    [Const, Ref] VectorExpressionParserError GetFatalErrors();

    //Inherited from ExpressionParser2NodeWorker:
};

enum ExpressionCompletionDescription_CompletionKind {
  "ExpressionCompletionDescription::Object",
  "ExpressionCompletionDescription::BehaviorWithPrefix",
  "ExpressionCompletionDescription::ExpressionWithPrefix",
  "ExpressionCompletionDescription::Variable",
  "ExpressionCompletionDescription::TextWithPrefix",
  "ExpressionCompletionDescription::Property",
  "ExpressionCompletionDescription::Parameter"
};

interface ExpressionCompletionDescription {
  ExpressionCompletionDescription_CompletionKind GetCompletionKind();
  [Const, Ref] DOMString GetType();
  Variable_Type GetVariableType();
  VariablesContainer_SourceType GetVariableScope();
  [Const, Ref] DOMString GetPrefix();
  [Const, Ref] DOMString GetCompletion();
  [Const, Ref] DOMString GetObjectName();
  [Const, Ref] DOMString GetBehaviorName();
  boolean IsExact();
  boolean IsLastParameter();
  unsigned long GetReplacementStartPosition();
  unsigned long GetReplacementEndPosition();
  [Const, Ref] ParameterMetadata GetParameterMetadata();
  boolean HasObjectConfiguration();
  [Const, Ref] ObjectConfiguration GetObjectConfiguration();

  [Const, Value] DOMString ToString();
};

interface VectorExpressionCompletionDescription {
    unsigned long size();
    [Value] ExpressionCompletionDescription at(unsigned long index);
};

interface ExpressionCompletionFinder {
    [Value] VectorExpressionCompletionDescription STATIC_GetCompletionDescriptionsFor([Const, Ref] Platform platform, [Const, Ref] ProjectScopedContainers projectScopedContainers, [Const] DOMString rootType, [Ref] ExpressionNode node, unsigned long location);

    [Const, Ref] VectorExpressionCompletionDescription GetCompletionDescriptions();

    //Inherited from ExpressionParser2NodeWorker:
};

interface ExpressionNodeLocationFinder {
    ExpressionNode STATIC_GetNodeAtPosition([Ref] ExpressionNode node, unsigned long searchedPosition);
};

interface ExpressionTypeFinder {
    [Const, Ref] DOMString STATIC_GetType([Const, Ref] Platform platform, [Const, Ref] ProjectScopedContainers projectScopedContainers, [Const] DOMString rootType, [Ref] ExpressionNode node);
};

interface ExpressionNode {
    void Visit([Ref] ExpressionParser2NodeWorker worker);
};

interface UniquePtrExpressionNode {
    ExpressionNode get();
};

interface ExpressionParser2 {
    void ExpressionParser2();

    [Value] UniquePtrExpressionNode ParseExpression([Const] DOMString expression);
};

enum EventsFunction_FunctionType {
  "EventsFunction::Action",
  "EventsFunction::Condition",
  "EventsFunction::Expression",
  "EventsFunction::ExpressionAndCondition",
  "EventsFunction::ActionWithOperator"
};

interface EventsFunction {
    void EventsFunction();
    EventsFunction Clone();

    [Ref] EventsFunction SetDescription([Const] DOMString description);
    [Const, Ref] DOMString GetDescription();
    [Ref] EventsFunction SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
    [Ref] EventsFunction SetFullName([Const] DOMString fullName);
    [Const, Ref] DOMString GetFullName();
    [Ref] EventsFunction SetSentence([Const] DOMString sentence);
    [Const, Ref] DOMString GetSentence();
    [Ref] EventsFunction SetGroup([Const] DOMString group);
    [Const, Ref] DOMString GetGroup();
    [Ref] EventsFunction SetGetterName([Const] DOMString group);
    [Const, Ref] DOMString GetGetterName();
    [Ref] EventsFunction SetExpressionType([Const, Ref] ValueTypeMetadata type);
    [Const, Ref] ValueTypeMetadata GetExpressionType();
    [Ref] EventsFunction SetPrivate(boolean isPrivate);
    boolean IsPrivate();
    [Ref] EventsFunction SetAsync(boolean isAsync);
    boolean IsAsync();
    boolean IsAction();
    boolean IsExpression();
    boolean IsCondition();
    [Ref] EventsFunction SetFunctionType(EventsFunction_FunctionType type);
    EventsFunction_FunctionType GetFunctionType();

    [Ref] EventsList GetEvents();
    [Ref] ParameterMetadataContainer GetParameters();
    [Const, Ref] ParameterMetadataContainer GetParametersForEvents([Const, Ref] EventsFunctionsContainer functionsContainer);
    [Ref] ObjectGroupsContainer GetObjectGroups();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);
};

enum EventsFunctionsContainer_FunctionOwner {
    "EventsFunctionsContainer::Extension",
    "EventsFunctionsContainer::Behavior",
    "EventsFunctionsContainer::Object"
};

interface EventsFunctionsContainer {
    [Ref] EventsFunction InsertNewEventsFunction([Const] DOMString name, unsigned long pos);
    [Ref] EventsFunction InsertEventsFunction([Const, Ref] EventsFunction eventsFunction, unsigned long pos);
    boolean HasEventsFunctionNamed([Const] DOMString name);
    [Ref] EventsFunction GetEventsFunction([Const] DOMString name);
    [Ref] EventsFunction GetEventsFunctionAt(unsigned long pos);
    void RemoveEventsFunction([Const] DOMString name);
    void MoveEventsFunction(unsigned long oldIndex, unsigned long newIndex);
    unsigned long GetEventsFunctionsCount();
    unsigned long GetEventsFunctionPosition([Const, Ref] EventsFunction eventsFunction);
};

interface AbstractEventsBasedEntity {
    [Ref] EventsFunctionsContainer GetEventsFunctions();
    [Ref] PropertiesContainer GetPropertyDescriptors();

    [Const, Ref] DOMString GetName();
    [Const, Ref] DOMString GetFullName();
    [Const, Ref] DOMString GetDescription();
    boolean IsPrivate();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);
};

interface EventsBasedBehavior {
    void EventsBasedBehavior();

    [Ref] EventsBasedBehavior SetName([Const] DOMString name);
    [Ref] EventsBasedBehavior SetFullName([Const] DOMString fullName);
    [Ref] EventsBasedBehavior SetDescription([Const] DOMString description);
    [Ref] EventsBasedBehavior SetPrivate(boolean isPrivate);

    [Ref] EventsBasedBehavior SetObjectType([Const] DOMString fullName);
    [Const, Ref] DOMString GetObjectType();
    [Ref] EventsBasedBehavior SetQuickCustomizationVisibility(QuickCustomization_Visibility visibility);
    QuickCustomization_Visibility GetQuickCustomizationVisibility();

    [Ref] PropertiesContainer GetSharedPropertyDescriptors();

    [Const, Value] DOMString STATIC_GetPropertyActionName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetPropertyConditionName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetPropertyExpressionName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetPropertyToggleActionName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetSharedPropertyActionName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetSharedPropertyConditionName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetSharedPropertyExpressionName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetSharedPropertyToggleActionName([Const] DOMString propertyName);
};
EventsBasedBehavior implements AbstractEventsBasedEntity;

interface EventsBasedBehaviorsList {
    [Ref] EventsBasedBehavior InsertNew([Const] DOMString name, unsigned long pos);
    [Ref] EventsBasedBehavior Insert([Const, Ref] EventsBasedBehavior item, unsigned long pos);
    boolean Has([Const] DOMString name);
    [Ref] EventsBasedBehavior Get([Const] DOMString name);
    [Ref] EventsBasedBehavior GetAt(unsigned long pos);
    void Remove([Const] DOMString name);
    void Move(unsigned long oldIndex, unsigned long newIndex);
    unsigned long GetCount();
    unsigned long GetPosition([Const, Ref] EventsBasedBehavior item);

    unsigned long size();
    [Ref] EventsBasedBehavior at(unsigned long index);
};

interface EventsBasedObject {
    void EventsBasedObject();

    [Ref] EventsBasedObject SetName([Const] DOMString name);
    [Ref] EventsBasedObject SetFullName([Const] DOMString fullName);
    [Ref] EventsBasedObject SetDescription([Const] DOMString description);
    [Ref] EventsBasedObject SetPrivate(boolean isPrivate);

    [Ref] EventsBasedObject SetDefaultName([Const] DOMString defaultName);
    [Const, Ref] DOMString GetDefaultName();
    [Ref] EventsBasedObject MarkAsRenderedIn3D(boolean isRenderedIn3D);
    boolean IsRenderedIn3D();
    [Ref] EventsBasedObject MarkAsAnimatable(boolean isAnimatable);
    boolean IsAnimatable();
    [Ref] EventsBasedObject MarkAsTextContainer(boolean isTextContainer);
    boolean IsTextContainer();
    [Ref] EventsBasedObject MarkAsInnerAreaFollowingParentSize(boolean value);
    boolean IsInnerAreaFollowingParentSize();
    [Ref] EventsBasedObject MakAsUsingLegacyInstancesRenderer(boolean value);
    boolean IsUsingLegacyInstancesRenderer();

    [Ref] EventsBasedObjectVariant GetDefaultVariant();
    [Ref] EventsBasedObjectVariantsContainer GetVariants();

    [Ref] InitialInstancesContainer GetInitialInstances();
    [Ref] LayersContainer GetLayers();
    [Ref] ObjectsContainer GetObjects();
    double GetAreaMinX();
    double GetAreaMinY();
    double GetAreaMinZ();
    double GetAreaMaxX();
    double GetAreaMaxY();
    double GetAreaMaxZ();
    void SetAreaMinX(double value);
    void SetAreaMinY(double value);
    void SetAreaMinZ(double value);
    void SetAreaMaxX(double value);
    void SetAreaMaxY(double value);
    void SetAreaMaxZ(double value);

    [Const, Value] DOMString STATIC_GetPropertyActionName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetPropertyConditionName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetPropertyExpressionName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetPropertyToggleActionName([Const] DOMString propertyName);
};
EventsBasedObject implements AbstractEventsBasedEntity;

interface EventsBasedObjectVariant {
    void EventsBasedObjectVariant();

    [Const, Ref] DOMString GetName();
    [Ref] EventsBasedObjectVariant SetName([Const] DOMString name);

    [Ref] InitialInstancesContainer GetInitialInstances();
    [Ref] LayersContainer GetLayers();
    [Ref] ObjectsContainer GetObjects();
    double GetAreaMinX();
    double GetAreaMinY();
    double GetAreaMinZ();
    double GetAreaMaxX();
    double GetAreaMaxY();
    double GetAreaMaxZ();
    void SetAreaMinX(double value);
    void SetAreaMinY(double value);
    void SetAreaMinZ(double value);
    void SetAreaMaxX(double value);
    void SetAreaMaxY(double value);
    void SetAreaMaxZ(double value);
    void SetAssetStoreAssetId([Const] DOMString assetStoreAssetId);
    [Const, Ref] DOMString GetAssetStoreAssetId();
    void SetAssetStoreOriginalName([Const] DOMString assetStoreOriginalName);
    [Const, Ref] DOMString GetAssetStoreOriginalName();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);
};

interface EventsBasedObjectVariantsContainer {
    [Ref] EventsBasedObjectVariant InsertNewVariant([Const] DOMString name, unsigned long pos);
    [Ref] EventsBasedObjectVariant InsertVariant([Const, Ref] EventsBasedObjectVariant variant, unsigned long pos);
    boolean HasVariantNamed([Const] DOMString name);
    [Ref] EventsBasedObjectVariant GetVariant([Const] DOMString name);
    [Ref] EventsBasedObjectVariant GetVariantAt(unsigned long pos);
    void RemoveVariant([Const] DOMString name);
    void MoveVariant(unsigned long oldIndex, unsigned long newIndex);
    unsigned long GetVariantsCount();
    unsigned long GetVariantPosition([Const, Ref] EventsBasedObjectVariant variant);
};

interface EventsBasedObjectsList {
    [Ref] EventsBasedObject InsertNew([Const] DOMString name, unsigned long pos);
    [Ref] EventsBasedObject Insert([Const, Ref] EventsBasedObject item, unsigned long pos);
    boolean Has([Const] DOMString name);
    [Ref] EventsBasedObject Get([Const] DOMString name);
    [Ref] EventsBasedObject GetAt(unsigned long pos);
    void Remove([Const] DOMString name);
    void Move(unsigned long oldIndex, unsigned long newIndex);
    long GetCount();
    long GetPosition([Const, Ref] EventsBasedObject item);

    unsigned long size();
    [Ref] EventsBasedObject at(unsigned long index);
};

interface PropertiesContainer {
    void PropertiesContainer(EventsFunctionsContainer_FunctionOwner owner);

    [Ref] NamedPropertyDescriptor InsertNew([Const] DOMString name, unsigned long pos);
    [Ref] NamedPropertyDescriptor Insert([Const, Ref] NamedPropertyDescriptor item, unsigned long pos);
    boolean Has([Const] DOMString name);
    [Ref] NamedPropertyDescriptor Get([Const] DOMString name);
    [Ref] NamedPropertyDescriptor GetAt(unsigned long pos);
    void Remove([Const] DOMString name);
    void Move(unsigned long oldIndex, unsigned long newIndex);
    unsigned long GetCount();
    unsigned long GetPosition([Const, Ref] NamedPropertyDescriptor item);

    unsigned long size();
    [Ref] NamedPropertyDescriptor at(unsigned long index);
};

interface EventsFunctionsExtension {
    void EventsFunctionsExtension();

    [Ref] EventsFunctionsExtension SetNamespace([Const] DOMString namespace_);
    [Const, Ref] DOMString GetNamespace();
    [Ref] EventsFunctionsExtension SetVersion([Const] DOMString version);
    [Const, Ref] DOMString GetVersion();
    [Ref] EventsFunctionsExtension SetShortDescription([Const] DOMString shortDescription);
    [Const, Ref] DOMString GetShortDescription();
    [Ref] EventsFunctionsExtension SetDescription([Const] DOMString description);
    [Const, Ref] DOMString GetDescription();
    [Ref] EventsFunctionsExtension SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
    [Ref] EventsFunctionsExtension SetFullName([Const] DOMString fullName);
    [Const, Ref] DOMString GetFullName();
    [Ref] EventsFunctionsExtension SetCategory([Const] DOMString category);
    [Const, Ref] DOMString GetCategory();
    [Ref] VectorString GetTags();
    [Ref] VectorString GetAuthorIds();
    [Ref] EventsFunctionsExtension SetAuthor([Const] DOMString author);
    [Const, Ref] DOMString GetAuthor();
    [Ref] EventsFunctionsExtension SetPreviewIconUrl([Const] DOMString previewIconUrl);
    [Const, Ref] DOMString GetPreviewIconUrl();
    [Ref] EventsFunctionsExtension SetIconUrl([Const] DOMString iconUrl);
    [Const, Ref] DOMString GetIconUrl();
    [Ref] EventsFunctionsExtension SetHelpPath([Const] DOMString helpPath);
    [Const, Ref] DOMString GetHelpPath();
    void SetOrigin([Const] DOMString originName, [Const] DOMString originIdentifier);
    [Const, Ref] DOMString GetOriginName();
    [Const, Ref] DOMString GetOriginIdentifier();


    [Ref] DependencyMetadata AddDependency();
    void RemoveDependencyAt(unsigned long index);
    [Ref] VectorDependencyMetadata GetAllDependencies();

    [Ref] SourceFileMetadata AddSourceFile();
    void RemoveSourceFileAt(unsigned long index);
    [Ref] VectorSourceFileMetadata GetAllSourceFiles();

    [Ref] EventsFunctionsContainer GetEventsFunctions();
    [Ref] VariablesContainer GetGlobalVariables();
    [Ref] VariablesContainer GetSceneVariables();

    [Ref] EventsBasedBehaviorsList GetEventsBasedBehaviors();
    [Ref] EventsBasedObjectsList GetEventsBasedObjects();

    void SerializeTo([Ref] SerializerElement element);
    void SerializeToExternal([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);

    boolean STATIC_IsExtensionLifecycleEventsFunction([Const] DOMString eventsFunctionName);
};

interface AbstractFileSystem {
};

[JSImplementation=AbstractFileSystem]
interface AbstractFileSystemJS {
    void AbstractFileSystemJS();

    void MkDir([Const] DOMString dir);
    void DirExists([Const] DOMString dir);
    void ClearDir([Const] DOMString dir);
    [Const, Ref] DOMString GetTempDir();
    [Const, Ref] DOMString FileNameFrom([Const] DOMString dir);
    [Const, Ref] DOMString DirNameFrom([Const] DOMString dir);
    //void MakeAbsolute([Const] DOMString dir);
    //void MakeRelative([Const] DOMString dir);
    boolean IsAbsolute([Const] DOMString fn);
    void CopyFile([Const] DOMString src, [Const] DOMString dest);
    void WriteToFile([Const] DOMString fn, [Const] DOMString content);
    [Const, Ref] DOMString ReadFile([Const] DOMString fn);
    [Value] VectorString ReadDir([Const] DOMString dir);
    boolean FileExists([Const] DOMString fn);
};

interface ProjectResourcesAdder {
    [Value] VectorString STATIC_GetAllUseless([Ref] Project project, [Const] DOMString resourceType);
    void STATIC_RemoveAllUseless([Ref] Project project, [Const] DOMString resourceType);
};

interface ArbitraryEventsWorker {
    void Launch([Ref] EventsList events);
};

interface ArbitraryObjectsWorker {
    void Launch([Ref] ObjectsContainer container);
};

interface EventsParametersLister {
    void EventsParametersLister([Ref] Project project);
    [Const, Ref] MapStringString GetParametersAndTypes();

    //Inherited from ArbitraryEventsWorker
    void Launch([Ref] EventsList events);
};

interface EventsPositionFinder {
    void EventsPositionFinder();
    [Value] VectorInt GetPositions();
    void AddEventToSearch(BaseEvent event);

    //Inherited from ArbitraryEventsWorker
    void Launch([Ref] EventsList events);
};

interface EventsTypesLister {
    void EventsTypesLister([Const, Ref] Project project);
    [Const, Ref] VectorString GetAllEventsTypes();
    [Const, Ref] VectorString GetAllConditionsTypes();
    [Const, Ref] VectorString GetAllActionsTypes();

    //Inherited from ArbitraryEventsWorker
    void Launch([Ref] EventsList events);
};

interface InstructionsTypeRenamer {
    void InstructionsTypeRenamer([Const, Ref] Project project, [Const] DOMString oldType, [Const] DOMString newType);

    //Inherited from ArbitraryEventsWorker
    void Launch([Ref] EventsList events);
};

interface EventsContext {
    [Const, Ref] SetString GetReferencedObjectOrGroupNames();
    [Const, Ref] SetString GetObjectNames();
    [Const, Ref] SetString GetBehaviorNamesOfObjectOrGroup([Const] DOMString objectOrGroupName);
};

interface EventsContextAnalyzer {
    void EventsContextAnalyzer([Const, Ref] Platform platform);
    [Const, Ref] EventsContext GetEventsContext();

    //Inherited from ArbitraryEventsWorkerWithContext
    void Launch([Ref] EventsList events, [Const, Ref] ProjectScopedContainers projectScopedContainers);
};

interface ArbitraryResourceWorker {
};
[JSImplementation=ArbitraryResourceWorker]
interface ArbitraryResourceWorkerJS {
    void ArbitraryResourceWorkerJS([Ref] ResourcesManager resourcesManager);

    void ExposeImage([Ref] DOMString image);
    void ExposeShader([Ref] DOMString shader);
    void ExposeFile([Ref] DOMString file);
};

interface ResourcesMergingHelper {
    void ResourcesMergingHelper([Ref] ResourcesManager resourcesManager, [Ref] AbstractFileSystem fs);

    void SetBaseDirectory([Const] DOMString basePath);
    [Ref] MapStringString GetAllResourcesOldAndNewFilename();
};
ResourcesMergingHelper implements ArbitraryResourceWorker;

interface ResourcesRenamer {
    void ResourcesRenamer([Ref] ResourcesManager resourcesManager, [Const, Ref] MapStringString oldToNewNames);
};
ResourcesRenamer implements ArbitraryResourceWorker;

interface ProjectResourcesCopier {
    boolean STATIC_CopyAllResourcesTo([Ref] Project project,
                                 [Ref] AbstractFileSystem fs,
                                 [Const] DOMString destinationDirectory,
                                 boolean updateOriginalProject,
                                 boolean preserveAbsoluteFilenames,
                                 boolean preserveDirectoryStructure);
};

interface ObjectsUsingResourceCollector {
    void ObjectsUsingResourceCollector([Ref] ResourcesManager resourcesManager, [Const] DOMString resourceName);
    [Const, Ref] VectorString GetObjectNames();

    //Inherited from ArbitraryObjectsWorker
    void Launch([Ref] ObjectsContainer container);
};

interface ResourcesInUseHelper {
    void ResourcesInUseHelper([Ref] ResourcesManager resourcesManager);

    [Const, Ref] VectorString GetAllResources();
    [Ref] SetString GetAllImages();
    [Ref] SetString GetAllAudios();
    [Ref] SetString GetAllFonts();
    [Ref] SetString GetAllBitmapFonts();
    [Ref] SetString GetAll([Const] DOMString resourceType);
};
ResourcesInUseHelper implements ArbitraryResourceWorker;

interface EditorSettings {
    void EditorSettings();

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Const, Ref] SerializerElement element);
};

//SpriteObject:

interface Point {
    void Point([Const] DOMString name);

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();
    void SetXY(double x, double y);
    double GetX();
    void SetX(double x);
    double GetY();
    void SetY(double y);
};

interface VectorPoint {
    void VectorPoint();

    void push_back([Const, Ref] Point pt);
    unsigned long size();
    [Ref] Point at(unsigned long index);
    void WRAPPED_set(unsigned long index, [Const, Ref] Point pt);
    void clear();
};

interface Polygon2d {
    void Polygon2d();

    [Ref] VectorVector2f GetVertices();
    void Move(double x, double y);
    void Rotate(double angle);
    boolean IsConvex();
    [Value] Vector2f ComputeCenter();

    [Value] Polygon2d STATIC_CreateRectangle(double width, double height);
};

interface VectorPolygon2d {
    void VectorPolygon2d();

    void push_back([Const, Ref] Polygon2d polygon);
    unsigned long size();
    [Ref] Polygon2d at(unsigned long index);
    void WRAPPED_set(unsigned long index, [Const, Ref] Polygon2d polygon);
    void FREE_removeFromVectorPolygon2d(unsigned long index);
    void clear();
};

interface Sprite {
    void Sprite();

    void SetImageName([Const] DOMString name);
    [Const, Ref] DOMString GetImageName();

    [Ref] Point GetOrigin();
    [Ref] Point GetCenter();
    boolean IsDefaultCenterPoint();
    void SetDefaultCenterPoint(boolean defaultPoint);

    [Ref] VectorPoint GetAllNonDefaultPoints();
    void AddPoint([Const, Ref] Point point);
    void DelPoint([Const] DOMString name);
    [Ref] Point GetPoint([Const] DOMString name);
    boolean HasPoint([Const] DOMString name);

    boolean IsFullImageCollisionMask();
    void SetFullImageCollisionMask(boolean enabled);

    [Ref] VectorPolygon2d GetCustomCollisionMask();
    void SetCustomCollisionMask([Const, Ref] VectorPolygon2d collisionMask);
};

interface Direction {
    void Direction();

    void AddSprite([Const, Ref] Sprite sprite);
    [Ref] Sprite GetSprite(unsigned long index);
    unsigned long GetSpritesCount();
    boolean HasNoSprites();
    void RemoveSprite(unsigned long index);
    void RemoveAllSprites();
    boolean IsLooping();
    void SetLoop(boolean enable);
    double GetTimeBetweenFrames();
    [Const, Ref] VectorString GetSpriteNames();
    void SetTimeBetweenFrames(double time);
    void SwapSprites(unsigned long first, unsigned long second);
    void MoveSprite(unsigned long oldIndex, unsigned long newIndex);

    void SetMetadata([Const] DOMString metadata);
    [Const, Ref] DOMString GetMetadata();
};

interface Animation {
    void Animation();

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();

    void SetDirectionsCount(unsigned long count);
    unsigned long GetDirectionsCount();
    [Ref] Direction GetDirection(unsigned long index);
    void SetDirection([Const, Ref] Direction direction, unsigned long index);
    boolean HasNoDirections();

    boolean UseMultipleDirections();
    void SetUseMultipleDirections(boolean enable);
};

interface SpriteAnimationList {
    void SpriteAnimationList();

    void AddAnimation([Const, Ref] Animation animation);
    [Ref] Animation GetAnimation(unsigned long index);
    unsigned long GetAnimationsCount();
    void RemoveAnimation(unsigned long index);
    void RemoveAllAnimations();
    boolean HasNoAnimations();
    void SwapAnimations(unsigned long first, unsigned long second);
    void MoveAnimation(unsigned long oldIndex, unsigned long newIndex);

    boolean AdaptCollisionMaskAutomatically();
    void SetAdaptCollisionMaskAutomatically(boolean adaptCollisionMaskAutomatically);
};

interface SpriteObject {
    void SpriteObject();

    [Ref] SpriteAnimationList GetAnimations();

    void SetUpdateIfNotVisible(boolean updateIfNotVisible);
    boolean GetUpdateIfNotVisible();
    void SetPreScale(double value);
    double GetPreScale();
};
SpriteObject implements ObjectConfiguration;

interface Model3DAnimation {
    void Model3DAnimation();

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();

    void SetSource([Const] DOMString name);
    [Const, Ref] DOMString GetSource();

    void SetShouldLoop(boolean shouldLoop);
    boolean ShouldLoop();
};

interface Model3DObjectConfiguration {
    void Model3DObjectConfiguration();

    void AddAnimation([Const, Ref] Model3DAnimation animation);
    [Ref] Model3DAnimation GetAnimation(unsigned long index);
    boolean HasAnimationNamed([Const] DOMString name);
    unsigned long GetAnimationsCount();
    void RemoveAnimation(unsigned long index);
    void RemoveAllAnimations();
    boolean HasNoAnimations();
    void SwapAnimations(unsigned long first, unsigned long second);
    void MoveAnimation(unsigned long oldIndex, unsigned long newIndex);

    double GetWidth();
    double GetHeight();
    double GetDepth();
    double GetRotationX();
    double GetRotationY();
    double GetRotationZ();

    [Const, Ref] DOMString GetModelResourceName();
    [Const, Ref] DOMString GetMaterialType();
    [Const, Ref] DOMString GetOriginLocation();
    [Const, Ref] DOMString GetCenterLocation();

    boolean shouldKeepAspectRatio();
};
Model3DObjectConfiguration implements ObjectConfiguration;


interface SpineAnimation {
    void SpineAnimation();

    void SetName([Const] DOMString name);
    [Const, Ref] DOMString GetName();

    void SetSource([Const] DOMString name);
    [Const, Ref] DOMString GetSource();

    void SetShouldLoop(boolean shouldLoop);
    boolean ShouldLoop();
};

interface SpineObjectConfiguration {
    void SpineObjectConfiguration();

    void AddAnimation([Const, Ref] SpineAnimation animation);
    [Ref] SpineAnimation GetAnimation(unsigned long index);
    boolean HasAnimationNamed([Const] DOMString name);
    unsigned long GetAnimationsCount();
    void RemoveAnimation(unsigned long index);
    void RemoveAllAnimations();
    boolean HasNoAnimations();
    void SwapAnimations(unsigned long first, unsigned long second);
    void MoveAnimation(unsigned long oldIndex, unsigned long newIndex);

    double GetScale();
    [Const, Ref] DOMString GetSpineResourceName();
};
SpineObjectConfiguration implements ObjectConfiguration;

interface Vector2f {
    void Vector2f();

    attribute float x;
    attribute float y;
};

interface VectorVector2f {
    void VectorVector2f();

    void push_back([Const, Ref] Vector2f pt);
    unsigned long size();
    [Ref] Vector2f at(unsigned long index);
    void WRAPPED_set(unsigned long index, [Const, Ref] Vector2f pt);
    void FREE_removeFromVectorVector2f(unsigned long index);
    void FREE_moveVector2fInVector(unsigned long oldIndex, unsigned long newIndex);
    void clear();
};

//Extensions bindings:

interface TextObject {
    void TextObject();

    void SetText([Const] DOMString string);
    [Const, Ref] DOMString GetText();
    void SetCharacterSize(double size);
    double GetCharacterSize();
    void SetFontName([Const] DOMString string);
    [Const, Ref] DOMString GetFontName();
    boolean IsBold();
    void SetBold(boolean enable);
    boolean IsItalic();
    void SetItalic(boolean enable);
    boolean IsUnderlined();
    void SetUnderlined(boolean enable);
    void SetColor([Const] DOMString color);
    [Const, Ref] DOMString GetColor();
    void SetTextAlignment([Const] DOMString textAlignment);
    [Const, Ref] DOMString GetTextAlignment();
    void SetVerticalTextAlignment([Const] DOMString value);
    [Const, Ref] DOMString GetVerticalTextAlignment();

    void SetOutlineEnabled(boolean enable);
    boolean IsOutlineEnabled();
    void SetOutlineThickness(double value);
    double GetOutlineThickness();
    void SetOutlineColor([Const] DOMString color);
    [Const, Ref] DOMString GetOutlineColor();

    void SetShadowEnabled(boolean enable);
    boolean IsShadowEnabled();
    void SetShadowColor([Const] DOMString color);
    [Const, Ref] DOMString GetShadowColor();
    void SetShadowOpacity(double value);
    double GetShadowOpacity();
    void SetShadowAngle(double value);
    double GetShadowAngle();
    void SetShadowDistance(double value);
    double GetShadowDistance();
    void SetShadowBlurRadius(double value);
    double GetShadowBlurRadius();
};
TextObject implements ObjectConfiguration;

interface TiledSpriteObject {
    void TiledSpriteObject();

    void SetTexture([Const] DOMString texture);
    [Const, Ref] DOMString GetTexture();
    void SetWidth(double width);
    double GetWidth();
    void SetHeight(double height);
    double GetHeight();
};
TiledSpriteObject implements ObjectConfiguration;

interface PanelSpriteObject {
    void PanelSpriteObject();

    double GetLeftMargin();
    void SetLeftMargin(double newMargin);

    double GetTopMargin();
    void SetTopMargin(double newMargin);

    double GetRightMargin();
    void SetRightMargin(double newMargin);

    double GetBottomMargin();
    void SetBottomMargin(double newMargin);

    boolean IsTiled();
    void SetTiled(boolean enable);

    void SetTexture([Const] DOMString texture);
    [Const, Ref] DOMString GetTexture();

    void SetWidth(double width);
    double GetWidth();
    void SetHeight(double height);
    double GetHeight();
};
PanelSpriteObject implements ObjectConfiguration;

interface ShapePainterObject {
    void ShapePainterObject();

    void SetCoordinatesAbsolute();
    void SetCoordinatesRelative();
    boolean AreCoordinatesAbsolute();

    void SetClearBetweenFrames(boolean value);
    boolean IsClearedBetweenFrames();

    void SetOutlineSize(double size);
    double GetOutlineSize();

    void SetOutlineColor([Const] DOMString color);
    [Const, Ref] DOMString GetOutlineColor();

    void SetOutlineOpacity(double val);
    double GetOutlineOpacity();

    void SetFillColor([Const] DOMString color);
    [Const, Ref] DOMString GetFillColor();

    void SetFillOpacity(double val);
    double GetFillOpacity();

    [Const, Value] DOMString GetAntialiasing();
    void SetAntialiasing([Const] DOMString value);

};
ShapePainterObject implements ObjectConfiguration;

interface TextEntryObject {
    void TextEntryObject();
};
TextEntryObject implements ObjectConfiguration;

enum ParticleEmitterObject_RendererType {
  "ParticleEmitterObject::Point",
  "ParticleEmitterObject::Line",
  "ParticleEmitterObject::Quad"
};

interface ParticleEmitterObject {
    void ParticleEmitterObject();

    void SetRendererType(ParticleEmitterObject_RendererType type);
    ParticleEmitterObject_RendererType GetRendererType();

    void SetParticleTexture([Const] DOMString resourceName);
    [Const, Ref] DOMString GetParticleTexture();
    void SetRendererParam1(double newValue);
    double GetRendererParam1();
    void SetRendererParam2(double newValue);
    double GetRendererParam2();

    boolean IsRenderingAdditive();
    void SetRenderingAdditive();
    void SetRenderingAlpha();

    void SetMaxParticleNb(double newValue);
    double GetMaxParticleNb();
    void SetTank(double newValue);
    double GetTank();
    void SetFlow(double newValue);
    double GetFlow();
    void SetDestroyWhenNoParticles(boolean enable);
    boolean GetDestroyWhenNoParticles();

    void SetEmitterForceMin(double newValue);
    double GetEmitterForceMin();
    void SetEmitterForceMax(double newValue);
    double GetEmitterForceMax();

    void SetConeSprayAngle(double newValue);
    double GetConeSprayAngle();

    void SetZoneRadius(double newValue);
    double GetZoneRadius();

    void SetParticleGravityX(double newValue);
    double GetParticleGravityX();
    void SetParticleGravityY(double newValue);
    double GetParticleGravityY();
    void SetParticleGravityAngle(double newValue);
    double GetParticleGravityAngle();
    void SetParticleGravityLength(double newValue);
    double GetParticleGravityLength();

    void SetParticleLifeTimeMin(double newValue);
    double GetParticleLifeTimeMin();
    void SetParticleLifeTimeMax(double newValue);
    double GetParticleLifeTimeMax();

    void SetParticleColor1([Const] DOMString newValue);
    [Const, Ref] DOMString GetParticleColor1();
    void SetParticleColor2([Const] DOMString newValue);
    [Const, Ref] DOMString GetParticleColor2();
    void SetParticleAlpha1(double newValue);
    double GetParticleAlpha1();
    void SetParticleAlpha2(double newValue);
    double GetParticleAlpha2();
    void SetParticleSize1(double newValue);
    double GetParticleSize1();
    void SetParticleSize2(double newValue);
    double GetParticleSize2();
    void SetParticleAngle1(double newValue);
    double GetParticleAngle1();
    void SetParticleAngle2(double newValue);
    double GetParticleAngle2();
    void SetParticleAlphaRandomness1(double newValue);
    double GetParticleAlphaRandomness1();
    void SetParticleAlphaRandomness2(double newValue);
    double GetParticleAlphaRandomness2();
    void SetParticleSizeRandomness1(double newValue);
    double GetParticleSizeRandomness1();
    void SetParticleSizeRandomness2(double newValue);
    double GetParticleSizeRandomness2();
    void SetParticleAngleRandomness1(double newValue);
    double GetParticleAngleRandomness1();
    void SetParticleAngleRandomness2(double newValue);
    double GetParticleAngleRandomness2();

    void SetJumpForwardInTimeOnCreation(double newValue);
    double GetJumpForwardInTimeOnCreation();

};
ParticleEmitterObject implements ObjectConfiguration;

//GDJS bindings:
[Prefix="gdjs::"]
interface LayoutCodeGenerator {
    void LayoutCodeGenerator([Ref] Project project);
    [Const, Value] DOMString GenerateLayoutCompleteCode(
        [Const, Ref] Layout layout,
        [Ref] SetString includes,
        [Ref] DiagnosticReport diagnosticReport,
        boolean compilationForRuntime);
};

[Prefix="gdjs::"]
interface BehaviorCodeGenerator {
    void BehaviorCodeGenerator([Ref] Project project);
    [Const, Value] DOMString GenerateRuntimeBehaviorCompleteCode(
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
        [Const, Ref] EventsBasedBehavior eventsBasedBehavior,
        [Const] DOMString codeNamespace,
        [Const, Ref] MapStringString behaviorMethodMangledNames,
        [Ref] SetString includes,
        boolean compilationForRuntime);
    [Const, Value] DOMString STATIC_GetBehaviorPropertyGetterName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetBehaviorPropertySetterName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetBehaviorPropertyToggleFunctionName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetBehaviorSharedPropertyGetterName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetBehaviorSharedPropertySetterName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetBehaviorSharedPropertyToggleFunctionName([Const] DOMString propertyName);
};

[Prefix="gdjs::"]
interface ObjectCodeGenerator {
    void ObjectCodeGenerator([Ref] Project project);
    [Const, Value] DOMString GenerateRuntimeObjectCompleteCode(
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
        [Const, Ref] EventsBasedObject eventsBasedObject,
        [Const] DOMString codeNamespace,
        [Const, Ref] MapStringString objectMethodMangledNames,
        [Ref] SetString includes,
        boolean compilationForRuntime);
    [Const, Value] DOMString STATIC_GetObjectPropertyGetterName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetObjectPropertySetterName([Const] DOMString propertyName);
    [Const, Value] DOMString STATIC_GetObjectPropertyToggleFunctionName([Const] DOMString propertyName);
};

[Prefix="gdjs::"]
interface EventsFunctionsExtensionCodeGenerator {
    void EventsFunctionsExtensionCodeGenerator([Ref] Project project);
    [Const, Value] DOMString GenerateFreeEventsFunctionCompleteCode([Const, Ref] EventsFunctionsExtension extension, [Const, Ref] EventsFunction eventsFunction, [Const] DOMString codeNamespac, [Ref] SetString includes, boolean compilationForRuntime);
};

[Prefix="gdjs::"]
interface PreviewExportOptions {
    void PreviewExportOptions([Ref] Project project, [Const] DOMString outputPath);
    [Ref] PreviewExportOptions UseWebsocketDebuggerClientWithServerAddress([Const] DOMString address, [Const] DOMString port);
    [Ref] PreviewExportOptions UseWindowMessageDebuggerClient();
    [Ref] PreviewExportOptions UseMinimalDebuggerClient();
    [Ref] PreviewExportOptions SetInAppTutorialMessageInPreview([Const] DOMString message, [Const] DOMString position);
    [Ref] PreviewExportOptions SetLayoutName([Const] DOMString layoutName);
    [Ref] PreviewExportOptions SetFallbackAuthor([Const] DOMString id, [Const] DOMString username);
    [Ref] PreviewExportOptions SetAuthenticatedPlayer([Const] DOMString playerId, [Const] DOMString playerUsername, [Const] DOMString playerToken);
    [Ref] PreviewExportOptions SetExternalLayoutName([Const] DOMString externalLayoutName);
    [Ref] PreviewExportOptions SetIncludeFileHash([Const] DOMString includeFile, long hash);
    [Ref] PreviewExportOptions SetProjectDataOnlyExport(boolean enable);
    [Ref] PreviewExportOptions SetNativeMobileApp(boolean enable);
    [Ref] PreviewExportOptions SetFullLoadingScreen(boolean enable);
    [Ref] PreviewExportOptions SetIsDevelopmentEnvironment(boolean enable);
    [Ref] PreviewExportOptions SetNonRuntimeScriptsCacheBurst(unsigned long value);
    [Ref] PreviewExportOptions SetElectronRemoteRequirePath([Const] DOMString electronRemoteRequirePath);
    [Ref] PreviewExportOptions SetGDevelopResourceToken([Const] DOMString gdevelopResourceToken);
    [Ref] PreviewExportOptions SetAllowAuthenticationUsingIframeForPreview(boolean enable);
    [Ref] PreviewExportOptions SetCrashReportUploadLevel([Const] DOMString crashReportUploadLevel);
    [Ref] PreviewExportOptions SetPreviewContext([Const] DOMString previewContext);
    [Ref] PreviewExportOptions SetGDevelopVersionWithHash([Const] DOMString gdevelopVersionWithHash);
    [Ref] PreviewExportOptions SetProjectTemplateSlug([Const] DOMString projectTemplateSlug);
    [Ref] PreviewExportOptions SetSourceGameId([Const] DOMString sourceGameId);
    [Ref] PreviewExportOptions AddScreenshotCapture(long delayTimeInSeconds, [Const] DOMString signedUrl, [Const] DOMString publicUrl);
};

[Prefix="gdjs::"]
interface ExportOptions {
    void ExportOptions([Ref] Project project, [Const] DOMString outputPath);
    [Ref] ExportOptions SetFallbackAuthor([Const] DOMString id, [Const] DOMString username);
    [Ref] ExportOptions SetTarget([Const] DOMString target);
};

[Prefix="gdjs::"]
interface Exporter {
    void Exporter([Ref] AbstractFileSystem fs, [Const] DOMString gdjsRoot);
    void SetCodeOutputDirectory([Const] DOMString path);

    boolean ExportProjectForPixiPreview([Const, Ref] PreviewExportOptions options);
    boolean ExportWholePixiProject([Const, Ref] ExportOptions options);

    [Const, Ref] DOMString GetLastError();
};

[Prefix="gdjs::"]
interface JsCodeEvent {
    void JsCodeEvent();

    [Const, Ref] DOMString GetInlineCode();
    void SetInlineCode([Const] DOMString type);
    [Const, Ref] DOMString GetParameterObjects();
    void SetParameterObjects([Const] DOMString type);
    boolean IsEventsSheetExpanded();
    void SetEventsSheetExpanded(boolean enable);

    //Inherited from BaseEvent:
    JsCodeEvent Clone();
    [Const, Ref] DOMString GetType();
    void SetType([Const] DOMString type);
    boolean IsExecutable();
    boolean CanHaveSubEvents();
    boolean HasSubEvents();
    [Ref] EventsList GetSubEvents();
    boolean IsDisabled();
    void SetDisabled(boolean disable);
    boolean IsFolded();
    void SetFolded(boolean folded);

    void SerializeTo([Ref] SerializerElement element);
    void UnserializeFrom([Ref] Project project, [Const, Ref] SerializerElement element);
};

[Prefix="gdjs::"]
interface MetadataDeclarationHelper {
    void MetadataDeclarationHelper();

    void STATIC_DeclareExtension(
        [Ref] PlatformExtension extension,
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension);

    [Ref] AbstractFunctionMetadata GenerateFreeFunctionMetadata(
        [Const, Ref] Project project,
        [Ref] PlatformExtension extension,
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
        [Const, Ref] EventsFunction eventsFunction);

    [Ref] BehaviorMetadata STATIC_GenerateBehaviorMetadata(
        [Const, Ref] Project project,
        [Ref] PlatformExtension extension,
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
        [Const, Ref] EventsBasedBehavior eventsBasedBehavior,
        [Ref] MapStringString behaviorMethodMangledNames);

    [Ref] ObjectMetadata STATIC_GenerateObjectMetadata(
        [Ref] Project project,
        [Ref] PlatformExtension extension,
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
        [Const, Ref] EventsBasedObject eventsBasedObject,
        [Ref] MapStringString objectMethodMangledNames);

    [Const, Value] DOMString STATIC_GetExtensionCodeNamespacePrefix(
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension);

    [Const, Value] DOMString STATIC_GetFreeFunctionCodeName(
        [Const, Ref] EventsFunctionsExtension eventsFunctionsExtension,
        [Const, Ref] EventsFunction eventsFunction);

    [Const, Value] DOMString STATIC_GetFreeFunctionCodeNamespace(
        [Const, Ref] EventsFunction eventsFunction,
        [Const] DOMString codeNamespacePrefix);

    [Const, Value] DOMString STATIC_GetBehaviorFunctionCodeNamespace(
        [Const, Ref] EventsBasedBehavior eventsBasedBehavior,
        [Const] DOMString codeNamespacePrefix);

    [Const, Value] DOMString STATIC_GetObjectFunctionCodeNamespace(
        [Const, Ref] EventsBasedObject eventsBasedObject,
        [Const] DOMString codeNamespacePrefix);

    boolean STATIC_IsBehaviorLifecycleEventsFunction([Const] DOMString functionName);
    boolean STATIC_IsObjectLifecycleEventsFunction([Const] DOMString functionName);
    boolean STATIC_IsExtensionLifecycleEventsFunction([Const] DOMString functionName);
    [Const, Value] DOMString STATIC_ShiftSentenceParamIndexes([Const] DOMString sentence, long offset);
};
