// Automatically generated by GDevelop.js/scripts/generate-types.js
declare class gdCustomObjectConfiguration extends gdObjectConfiguration {
  static NoAnchor: 0;
  static MinEdge: 1;
  static MaxEdge: 2;
  static Proportional: 3;
  static Center: 4;
  clone(): gdUniquePtrObjectConfiguration;
  getVariantName(): string;
  setVariantName(name: string): void;
  isForcedToOverrideEventsBasedObjectChildrenConfiguration(): boolean;
  isMarkedAsOverridingEventsBasedObjectChildrenConfiguration(): boolean;
  setMarkedAsOverridingEventsBasedObjectChildrenConfiguration(isOverridingEventsBasedObjectChildrenConfiguration: boolean): void;
  clearChildrenConfiguration(): void;
  getChildObjectConfiguration(objectName: string): gdObjectConfiguration;
  getProperties(): gdMapStringPropertyDescriptor;
  updateProperty(name: string, value: string): boolean;
  getInitialInstanceProperties(instance: gdInitialInstance): gdMapStringPropertyDescriptor;
  updateInitialInstanceProperty(instance: gdInitialInstance, name: string, value: string): boolean;
  getAnimations(): gdSpriteAnimationList;
  isChildObjectFolded(childName: string): boolean;
  setChildObjectFolded(childName: string, folded: boolean): void;
  static getEdgeAnchorFromString(value: string): CustomObjectConfiguration_EdgeAnchor;
  delete(): void;
  ptr: number;
};