// Automatically generated by GDevelop.js/scripts/generate-types.js
declare class gdMeasurementUnit {
  constructor(name: string, label: string, description: string): void;
  getName(): string;
  getLabel(): string;
  getDescription(): string;
  getElementsWithWords(): string;
  getElementsCount(): number;
  getElementPower(elementIndex: number): number;
  getElementBaseUnit(elementIndex: number): gdMeasurementBaseUnit;
  isUndefined(): boolean;
  static applyTranslation(): void;
  static getUndefined(): gdMeasurementUnit;
  static getDimensionless(): gdMeasurementUnit;
  static getDegreeAngle(): gdMeasurementUnit;
  static getSecond(): gdMeasurementUnit;
  static getPixel(): gdMeasurementUnit;
  static getPixelSpeed(): gdMeasurementUnit;
  static getPixelAcceleration(): gdMeasurementUnit;
  static getAngularSpeed(): gdMeasurementUnit;
  static getNewton(): gdMeasurementUnit;
  static getDefaultMeasurementUnitsCount(): number;
  static getDefaultMeasurementUnitAtIndex(index: number): gdMeasurementUnit;
  static getDefaultMeasurementUnitByName(name: string): gdMeasurementUnit;
  static hasDefaultMeasurementUnitNamed(name: string): boolean;
  delete(): void;
  ptr: number;
};