// Automatically generated by GDevelop.js/scripts/generate-types.js
declare class gdPlatformExtension {
  constructor(): void;
  setExtensionInformation(name: string, fullname: string, description: string, author: string, license: string): gdPlatformExtension;
  setExtensionHelpPath(helpPath: string): gdPlatformExtension;
  setIconUrl(iconUrl: string): gdPlatformExtension;
  setCategory(category: string): gdPlatformExtension;
  addInstructionOrExpressionGroupMetadata(name: string): gdInstructionOrExpressionGroupMetadata;
  markAsDeprecated(): void;
  getTags(): gdVectorString;
  setTags(csvTags: string): gdPlatformExtension;
  addExpressionAndCondition(type: string, name: string, fullname: string, description: string, sentenceName: string, group: string, icon: string): gdMultipleInstructionMetadata;
  addExpressionAndConditionAndAction(type: string, name: string, fullname: string, description: string, sentenceName: string, group: string, icon: string): gdMultipleInstructionMetadata;
  addCondition(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): gdInstructionMetadata;
  addAction(name: string, fullname: string, description: string, sentence: string, group: string, icon: string, smallicon: string): gdInstructionMetadata;
  addExpression(name: string, fullname: string, description: string, group: string, smallicon: string): gdExpressionMetadata;
  addStrExpression(name: string, fullname: string, description: string, group: string, smallicon: string): gdExpressionMetadata;
  addDependency(): gdDependencyMetadata;
  addBehavior(name: string, fullname: string, defaultName: string, description: string, group: string, icon24x24: string, className: string, instance: gdBehavior, sharedDatasInstance: gdBehaviorsSharedData): gdBehaviorMetadata;
  addObject(name: string, fullname: string, description: string, icon24x24: string, instance: gdObjectConfiguration): gdObjectMetadata;
  addEffect(name: string): gdEffectMetadata;
  registerProperty(name: string): gdPropertyDescriptor;
  getFullName(): string;
  getName(): string;
  getCategory(): string;
  getDescription(): string;
  getAuthor(): string;
  getLicense(): string;
  getHelpPath(): string;
  getIconUrl(): string;
  getNameSpace(): string;
  addDuplicatedAction(newActionName: string, copiedActionName: string): gdInstructionMetadata;
  addDuplicatedCondition(newConditionName: string, copiedConditionName: string): gdInstructionMetadata;
  addDuplicatedExpression(newExpressionName: string, copiedExpressionName: string): gdExpressionMetadata;
  addDuplicatedStrExpression(newExpressionName: string, copiedExpressionName: string): gdExpressionMetadata;
  getExtensionObjectsTypes(): gdVectorString;
  getBehaviorsTypes(): gdVectorString;
  getExtensionEffectTypes(): gdVectorString;
  getObjectMetadata(type: string): gdObjectMetadata;
  getBehaviorMetadata(type: string): gdBehaviorMetadata;
  getEffectMetadata(type: string): gdEffectMetadata;
  getAllEvents(): gdMapStringEventMetadata;
  getAllActions(): gdMapStringInstructionMetadata;
  getAllConditions(): gdMapStringInstructionMetadata;
  getAllExpressions(): gdMapStringExpressionMetadata;
  getAllStrExpressions(): gdMapStringExpressionMetadata;
  getAllActionsForObject(objectType: string): gdMapStringInstructionMetadata;
  getAllConditionsForObject(objectType: string): gdMapStringInstructionMetadata;
  getAllExpressionsForObject(objectType: string): gdMapStringExpressionMetadata;
  getAllStrExpressionsForObject(objectType: string): gdMapStringExpressionMetadata;
  getAllActionsForBehavior(autoType: string): gdMapStringInstructionMetadata;
  getAllConditionsForBehavior(autoType: string): gdMapStringInstructionMetadata;
  getAllExpressionsForBehavior(autoType: string): gdMapStringExpressionMetadata;
  getAllStrExpressionsForBehavior(autoType: string): gdMapStringExpressionMetadata;
  getAllProperties(): gdMapStringPropertyDescriptor;
  getAllDependencies(): gdVectorDependencyMetadata;
  getAllSourceFiles(): gdVectorSourceFileMetadata;
  static getNamespaceSeparator(): string;
  static getBehaviorFullType(extensionName: string, behaviorName: string): string;
  static getObjectFullType(extensionName: string, objectName: string): string;
  static getExtensionFromFullObjectType(type: string): string;
  static getObjectNameFromFullObjectType(type: string): string;
  delete(): void;
  ptr: number;
};