// Automatically generated by GDevelop.js/scripts/generate-types.js
declare class gdEventsBasedObjectVariantsContainer {
  insertNewVariant(name: string, pos: number): gdEventsBasedObjectVariant;
  insertVariant(variant: gdEventsBasedObjectVariant, pos: number): gdEventsBasedObjectVariant;
  hasVariantNamed(name: string): boolean;
  getVariant(name: string): gdEventsBasedObjectVariant;
  getVariantAt(pos: number): gdEventsBasedObjectVariant;
  removeVariant(name: string): void;
  moveVariant(oldIndex: number, newIndex: number): void;
  getVariantsCount(): number;
  getVariantPosition(variant: gdEventsBasedObjectVariant): number;
  delete(): void;
  ptr: number;
};