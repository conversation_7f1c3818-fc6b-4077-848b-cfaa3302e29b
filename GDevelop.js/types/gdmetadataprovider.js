// Automatically generated by GDevelop.js/scripts/generate-types.js
declare class gdMetadataProvider {
  static getExtensionAndBehaviorMetadata(p: gdPlatform, type: string): gdExtensionAndBehaviorMetadata;
  static getExtensionAndObjectMetadata(p: gdPlatform, type: string): gdExtensionAndObjectMetadata;
  static getExtensionAndEffectMetadata(p: gdPlatform, type: string): gdExtensionAndEffectMetadata;
  static getExtensionAndActionMetadata(p: gdPlatform, type: string): gdExtensionAndInstructionMetadata;
  static getExtensionAndConditionMetadata(p: gdPlatform, type: string): gdExtensionAndInstructionMetadata;
  static getExtensionAndExpressionMetadata(p: gdPlatform, type: string): gdExtensionAndExpressionMetadata;
  static getExtensionAndObjectExpressionMetadata(p: gdPlatform, objectType: string, type: string): gdExtensionAndExpressionMetadata;
  static getExtensionAndBehaviorExpressionMetadata(p: gdPlatform, autoType: string, type: string): gdExtensionAndExpressionMetadata;
  static getExtensionAndStrExpressionMetadata(p: gdPlatform, type: string): gdExtensionAndExpressionMetadata;
  static getExtensionAndObjectStrExpressionMetadata(p: gdPlatform, objectType: string, type: string): gdExtensionAndExpressionMetadata;
  static getExtensionAndBehaviorStrExpressionMetadata(p: gdPlatform, autoType: string, type: string): gdExtensionAndExpressionMetadata;
  static getBehaviorMetadata(p: gdPlatform, type: string): gdBehaviorMetadata;
  static getObjectMetadata(p: gdPlatform, type: string): gdObjectMetadata;
  static getEffectMetadata(p: gdPlatform, type: string): gdEffectMetadata;
  static getActionMetadata(p: gdPlatform, type: string): gdInstructionMetadata;
  static getConditionMetadata(p: gdPlatform, type: string): gdInstructionMetadata;
  static getExpressionMetadata(p: gdPlatform, type: string): gdExpressionMetadata;
  static getObjectExpressionMetadata(p: gdPlatform, objectType: string, type: string): gdExpressionMetadata;
  static getBehaviorExpressionMetadata(p: gdPlatform, autoType: string, type: string): gdExpressionMetadata;
  static getStrExpressionMetadata(p: gdPlatform, type: string): gdExpressionMetadata;
  static getObjectStrExpressionMetadata(p: gdPlatform, objectType: string, type: string): gdExpressionMetadata;
  static getBehaviorStrExpressionMetadata(p: gdPlatform, autoType: string, type: string): gdExpressionMetadata;
  static isBadExpressionMetadata(metadata: gdExpressionMetadata): boolean;
  static isBadInstructionMetadata(metadata: gdInstructionMetadata): boolean;
  static isBadBehaviorMetadata(metadata: gdBehaviorMetadata): boolean;
  static isBadObjectMetadata(metadata: gdObjectMetadata): boolean;
  delete(): void;
  ptr: number;
};