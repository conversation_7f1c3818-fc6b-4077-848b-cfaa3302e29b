// Automatically generated by GDevelop.js/scripts/generate-types.js
declare class gdVariablesContainersList {
  has(name: string): boolean;
  get(name: string): gdVariable;
  getVariablesContainerFromVariableOrPropertyOrParameterName(variableName: string): gdVariablesContainer;
  getVariablesContainerFromVariableOrPropertyName(variableName: string): gdVariablesContainer;
  getVariablesContainerFromVariableNameOnly(variableName: string): gdVariablesContainer;
  getVariablesContainer(index: number): gdVariablesContainer;
  getVariablesContainersCount(): number;
  delete(): void;
  ptr: number;
};