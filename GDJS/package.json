{"name": "gdevelop-javascript-platform", "version": "0.0.0", "private": true, "license": "MIT", "homepage": "https://gdevelop.io", "//": "We use esbuild for a near instant build (important for quick feedback loop when developing). Typechecking is a separate process as much longer.", "devDependencies": {"@types/expect.js": "^0.3.29", "@types/mocha": "^5.2.7", "@types/node": "^14.11.1", "@types/sinon": "^10.0.13", "@types/three": "0.160.0", "esbuild": "^0.13.12", "lebab": "^3.1.0", "minimist": "^1.2.5", "patch-package": "^6.4.7", "pixi-spine": "4.0.4", "pixi.js": "7.4.2", "prettier": "3.4.2", "recursive-readdir": "^2.2.2", "shelljs": "^0.8.4", "typedoc": "^0.25.13", "typedoc-plugin-reference-excluder": "^1.1.3", "typescript": "5.4.5"}, "scripts": {"postinstall": "patch-package", "check-types": "tsc", "build": "node scripts/build.js", "test": "cd tests && npm run test-benchmark", "format": "prettier --write \"Runtime/**/*.ts\" \"../Extensions/**/*.ts\" \"../Extensions/**/JsExtension.js\" \"../Extensions/**/*.spec.js\"", "check-format": "prettier --list-different \"Runtime/**/*.ts\" \"../Extensions/**/*.ts\" \"../Extensions/**/JsExtension.js\" \"../Extensions/**/*.spec.js\"", "generate-doc": "typedoc --options docs/typedoc.json --plugin typedoc-plugin-reference-excluder"}}