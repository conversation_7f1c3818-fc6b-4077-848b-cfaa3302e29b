/*!
 * pixi.js-legacy - v7.4.2
 * Compiled Wed, 20 Mar 2024 19:55:28 UTC
 *
 * pixi.js-legacy is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 */var PIXI=function(v){"use strict";var be=(r=>(r[r.WEBGL_LEGACY=0]="WEBGL_LEGACY",r[r.WEBGL=1]="WEBGL",r[r.WEBGL2=2]="WEBGL2",r))(be||{}),cr=(r=>(r[r.UNKNOWN=0]="UNKNOWN",r[r.WEBGL=1]="WEBGL",r[r.CANVAS=2]="CANVAS",r))(cr||{}),dr=(r=>(r[r.COLOR=16384]="COLOR",r[r.DEPTH=256]="DEPTH",r[r.STENCIL=1024]="STENCIL",r))(dr||{}),C=(r=>(r[r.NORMAL=0]="NORMAL",r[r.ADD=1]="ADD",r[r.MULTIPLY=2]="MULTIPLY",r[r.SCREEN=3]="SCREEN",r[r.OVERLAY=4]="OVERLAY",r[r.DARKEN=5]="DARKEN",r[r.LIGHTEN=6]="LIGHTEN",r[r.COLOR_DODGE=7]="COLOR_DODGE",r[r.COLOR_BURN=8]="COLOR_BURN",r[r.HARD_LIGHT=9]="HARD_LIGHT",r[r.SOFT_LIGHT=10]="SOFT_LIGHT",r[r.DIFFERENCE=11]="DIFFERENCE",r[r.EXCLUSION=12]="EXCLUSION",r[r.HUE=13]="HUE",r[r.SATURATION=14]="SATURATION",r[r.COLOR=15]="COLOR",r[r.LUMINOSITY=16]="LUMINOSITY",r[r.NORMAL_NPM=17]="NORMAL_NPM",r[r.ADD_NPM=18]="ADD_NPM",r[r.SCREEN_NPM=19]="SCREEN_NPM",r[r.NONE=20]="NONE",r[r.SRC_OVER=0]="SRC_OVER",r[r.SRC_IN=21]="SRC_IN",r[r.SRC_OUT=22]="SRC_OUT",r[r.SRC_ATOP=23]="SRC_ATOP",r[r.DST_OVER=24]="DST_OVER",r[r.DST_IN=25]="DST_IN",r[r.DST_OUT=26]="DST_OUT",r[r.DST_ATOP=27]="DST_ATOP",r[r.ERASE=26]="ERASE",r[r.SUBTRACT=28]="SUBTRACT",r[r.XOR=29]="XOR",r))(C||{}),Ot=(r=>(r[r.POINTS=0]="POINTS",r[r.LINES=1]="LINES",r[r.LINE_LOOP=2]="LINE_LOOP",r[r.LINE_STRIP=3]="LINE_STRIP",r[r.TRIANGLES=4]="TRIANGLES",r[r.TRIANGLE_STRIP=5]="TRIANGLE_STRIP",r[r.TRIANGLE_FAN=6]="TRIANGLE_FAN",r))(Ot||{}),P=(r=>(r[r.RGBA=6408]="RGBA",r[r.RGB=6407]="RGB",r[r.RG=33319]="RG",r[r.RED=6403]="RED",r[r.RGBA_INTEGER=36249]="RGBA_INTEGER",r[r.RGB_INTEGER=36248]="RGB_INTEGER",r[r.RG_INTEGER=33320]="RG_INTEGER",r[r.RED_INTEGER=36244]="RED_INTEGER",r[r.ALPHA=6406]="ALPHA",r[r.LUMINANCE=6409]="LUMINANCE",r[r.LUMINANCE_ALPHA=6410]="LUMINANCE_ALPHA",r[r.DEPTH_COMPONENT=6402]="DEPTH_COMPONENT",r[r.DEPTH_STENCIL=34041]="DEPTH_STENCIL",r))(P||{}),Me=(r=>(r[r.TEXTURE_2D=3553]="TEXTURE_2D",r[r.TEXTURE_CUBE_MAP=34067]="TEXTURE_CUBE_MAP",r[r.TEXTURE_2D_ARRAY=35866]="TEXTURE_2D_ARRAY",r[r.TEXTURE_CUBE_MAP_POSITIVE_X=34069]="TEXTURE_CUBE_MAP_POSITIVE_X",r[r.TEXTURE_CUBE_MAP_NEGATIVE_X=34070]="TEXTURE_CUBE_MAP_NEGATIVE_X",r[r.TEXTURE_CUBE_MAP_POSITIVE_Y=34071]="TEXTURE_CUBE_MAP_POSITIVE_Y",r[r.TEXTURE_CUBE_MAP_NEGATIVE_Y=34072]="TEXTURE_CUBE_MAP_NEGATIVE_Y",r[r.TEXTURE_CUBE_MAP_POSITIVE_Z=34073]="TEXTURE_CUBE_MAP_POSITIVE_Z",r[r.TEXTURE_CUBE_MAP_NEGATIVE_Z=34074]="TEXTURE_CUBE_MAP_NEGATIVE_Z",r))(Me||{}),$=(r=>(r[r.UNSIGNED_BYTE=5121]="UNSIGNED_BYTE",r[r.UNSIGNED_SHORT=5123]="UNSIGNED_SHORT",r[r.UNSIGNED_SHORT_5_6_5=33635]="UNSIGNED_SHORT_5_6_5",r[r.UNSIGNED_SHORT_4_4_4_4=32819]="UNSIGNED_SHORT_4_4_4_4",r[r.UNSIGNED_SHORT_5_5_5_1=32820]="UNSIGNED_SHORT_5_5_5_1",r[r.UNSIGNED_INT=5125]="UNSIGNED_INT",r[r.UNSIGNED_INT_10F_11F_11F_REV=35899]="UNSIGNED_INT_10F_11F_11F_REV",r[r.UNSIGNED_INT_2_10_10_10_REV=33640]="UNSIGNED_INT_2_10_10_10_REV",r[r.UNSIGNED_INT_24_8=34042]="UNSIGNED_INT_24_8",r[r.UNSIGNED_INT_5_9_9_9_REV=35902]="UNSIGNED_INT_5_9_9_9_REV",r[r.BYTE=5120]="BYTE",r[r.SHORT=5122]="SHORT",r[r.INT=5124]="INT",r[r.FLOAT=5126]="FLOAT",r[r.FLOAT_32_UNSIGNED_INT_24_8_REV=36269]="FLOAT_32_UNSIGNED_INT_24_8_REV",r[r.HALF_FLOAT=36193]="HALF_FLOAT",r))($||{}),k=(r=>(r[r.FLOAT=0]="FLOAT",r[r.INT=1]="INT",r[r.UINT=2]="UINT",r))(k||{}),Bt=(r=>(r[r.NEAREST=0]="NEAREST",r[r.LINEAR=1]="LINEAR",r))(Bt||{}),Zt=(r=>(r[r.CLAMP=33071]="CLAMP",r[r.REPEAT=10497]="REPEAT",r[r.MIRRORED_REPEAT=33648]="MIRRORED_REPEAT",r))(Zt||{}),Ht=(r=>(r[r.OFF=0]="OFF",r[r.POW2=1]="POW2",r[r.ON=2]="ON",r[r.ON_MANUAL=3]="ON_MANUAL",r))(Ht||{}),wt=(r=>(r[r.NPM=0]="NPM",r[r.UNPACK=1]="UNPACK",r[r.PMA=2]="PMA",r[r.NO_PREMULTIPLIED_ALPHA=0]="NO_PREMULTIPLIED_ALPHA",r[r.PREMULTIPLY_ON_UPLOAD=1]="PREMULTIPLY_ON_UPLOAD",r[r.PREMULTIPLIED_ALPHA=2]="PREMULTIPLIED_ALPHA",r))(wt||{}),Vt=(r=>(r[r.NO=0]="NO",r[r.YES=1]="YES",r[r.AUTO=2]="AUTO",r[r.BLEND=0]="BLEND",r[r.CLEAR=1]="CLEAR",r[r.BLIT=2]="BLIT",r))(Vt||{}),fr=(r=>(r[r.AUTO=0]="AUTO",r[r.MANUAL=1]="MANUAL",r))(fr||{}),Rt=(r=>(r.LOW="lowp",r.MEDIUM="mediump",r.HIGH="highp",r))(Rt||{}),pt=(r=>(r[r.NONE=0]="NONE",r[r.SCISSOR=1]="SCISSOR",r[r.STENCIL=2]="STENCIL",r[r.SPRITE=3]="SPRITE",r[r.COLOR=4]="COLOR",r))(pt||{}),Da=(r=>(r[r.RED=1]="RED",r[r.GREEN=2]="GREEN",r[r.BLUE=4]="BLUE",r[r.ALPHA=8]="ALPHA",r))(Da||{}),ft=(r=>(r[r.NONE=0]="NONE",r[r.LOW=2]="LOW",r[r.MEDIUM=4]="MEDIUM",r[r.HIGH=8]="HIGH",r))(ft||{}),jt=(r=>(r[r.ELEMENT_ARRAY_BUFFER=34963]="ELEMENT_ARRAY_BUFFER",r[r.ARRAY_BUFFER=34962]="ARRAY_BUFFER",r[r.UNIFORM_BUFFER=35345]="UNIFORM_BUFFER",r))(jt||{});const Oa={createCanvas:(r,t)=>{const e=document.createElement("canvas");return e.width=r,e.height=t,e},getCanvasRenderingContext2D:()=>CanvasRenderingContext2D,getWebGLRenderingContext:()=>WebGLRenderingContext,getNavigator:()=>navigator,getBaseUrl:()=>{var r;return(r=document.baseURI)!=null?r:window.location.href},getFontFaceSet:()=>document.fonts,fetch:(r,t)=>fetch(r,t),parseXML:r=>new DOMParser().parseFromString(r,"text/xml")},N={ADAPTER:Oa,RESOLUTION:1,CREATE_IMAGE_BITMAP:!1,ROUND_PIXELS:!1};var Si=/iPhone/i,Ba=/iPod/i,Fa=/iPad/i,Na=/\biOS-universal(?:.+)Mac\b/i,Ci=/\bAndroid(?:.+)Mobile\b/i,La=/Android/i,ze=/(?:SD4930UR|\bSilk(?:.+)Mobile\b)/i,pr=/Silk/i,ue=/Windows Phone/i,Ua=/\bWindows(?:.+)ARM\b/i,ka=/BlackBerry/i,Ga=/BB10/i,$a=/Opera Mini/i,Ha=/\b(CriOS|Chrome)(?:.+)Mobile/i,Va=/Mobile(?:.+)Firefox\b/i,ja=function(r){return typeof r!="undefined"&&r.platform==="MacIntel"&&typeof r.maxTouchPoints=="number"&&r.maxTouchPoints>1&&typeof MSStream=="undefined"};function zu(r){return function(t){return t.test(r)}}function Xa(r){var t={userAgent:"",platform:"",maxTouchPoints:0};!r&&typeof navigator!="undefined"?t={userAgent:navigator.userAgent,platform:navigator.platform,maxTouchPoints:navigator.maxTouchPoints||0}:typeof r=="string"?t.userAgent=r:r&&r.userAgent&&(t={userAgent:r.userAgent,platform:r.platform,maxTouchPoints:r.maxTouchPoints||0});var e=t.userAgent,s=e.split("[FBAN");typeof s[1]!="undefined"&&(e=s[0]),s=e.split("Twitter"),typeof s[1]!="undefined"&&(e=s[0]);var i=zu(e),n={apple:{phone:i(Si)&&!i(ue),ipod:i(Ba),tablet:!i(Si)&&(i(Fa)||ja(t))&&!i(ue),universal:i(Na),device:(i(Si)||i(Ba)||i(Fa)||i(Na)||ja(t))&&!i(ue)},amazon:{phone:i(ze),tablet:!i(ze)&&i(pr),device:i(ze)||i(pr)},android:{phone:!i(ue)&&i(ze)||!i(ue)&&i(Ci),tablet:!i(ue)&&!i(ze)&&!i(Ci)&&(i(pr)||i(La)),device:!i(ue)&&(i(ze)||i(pr)||i(Ci)||i(La))||i(/\bokhttp\b/i)},windows:{phone:i(ue),tablet:i(Ua),device:i(ue)||i(Ua)},other:{blackberry:i(ka),blackberry10:i(Ga),opera:i($a),firefox:i(Va),chrome:i(Ha),device:i(ka)||i(Ga)||i($a)||i(Va)||i(Ha)},any:!1,phone:!1,tablet:!1};return n.any=n.apple.device||n.android.device||n.windows.device||n.other.device,n.phone=n.apple.phone||n.android.phone||n.windows.phone,n.tablet=n.apple.tablet||n.android.tablet||n.windows.tablet,n}var za;const Xt=((za=Xa.default)!=null?za:Xa)(globalThis.navigator);N.RETINA_PREFIX=/@([0-9\.]+)x/,N.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT=!1;var Ri=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function We(r){return r&&r.__esModule&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}function Tg(r){return r&&Object.prototype.hasOwnProperty.call(r,"default")?r.default:r}function Eg(r){return r&&Object.prototype.hasOwnProperty.call(r,"default")&&Object.keys(r).length===1?r.default:r}function Ag(r){if(r.__esModule)return r;var t=r.default;if(typeof t=="function"){var e=function s(){if(this instanceof s){var i=[null];i.push.apply(i,arguments);var n=Function.bind.apply(t,i);return new n}return t.apply(this,arguments)};e.prototype=t.prototype}else e={};return Object.defineProperty(e,"__esModule",{value:!0}),Object.keys(r).forEach(function(s){var i=Object.getOwnPropertyDescriptor(r,s);Object.defineProperty(e,s,i.get?i:{enumerable:!0,get:function(){return r[s]}})}),e}var Ii={exports:{}},wg=Ii.exports;(function(r){"use strict";var t=Object.prototype.hasOwnProperty,e="~";function s(){}Object.create&&(s.prototype=Object.create(null),new s().__proto__||(e=!1));function i(h,l,u){this.fn=h,this.context=l,this.once=u||!1}function n(h,l,u,c,d){if(typeof u!="function")throw new TypeError("The listener must be a function");var f=new i(u,c||h,d),p=e?e+l:l;return h._events[p]?h._events[p].fn?h._events[p]=[h._events[p],f]:h._events[p].push(f):(h._events[p]=f,h._eventsCount++),h}function a(h,l){--h._eventsCount===0?h._events=new s:delete h._events[l]}function o(){this._events=new s,this._eventsCount=0}o.prototype.eventNames=function(){var l=[],u,c;if(this._eventsCount===0)return l;for(c in u=this._events)t.call(u,c)&&l.push(e?c.slice(1):c);return Object.getOwnPropertySymbols?l.concat(Object.getOwnPropertySymbols(u)):l},o.prototype.listeners=function(l){var u=e?e+l:l,c=this._events[u];if(!c)return[];if(c.fn)return[c.fn];for(var d=0,f=c.length,p=new Array(f);d<f;d++)p[d]=c[d].fn;return p},o.prototype.listenerCount=function(l){var u=e?e+l:l,c=this._events[u];return c?c.fn?1:c.length:0},o.prototype.emit=function(l,u,c,d,f,p){var m=e?e+l:l;if(!this._events[m])return!1;var g=this._events[m],_=arguments.length,x,y;if(g.fn){switch(g.once&&this.removeListener(l,g.fn,void 0,!0),_){case 1:return g.fn.call(g.context),!0;case 2:return g.fn.call(g.context,u),!0;case 3:return g.fn.call(g.context,u,c),!0;case 4:return g.fn.call(g.context,u,c,d),!0;case 5:return g.fn.call(g.context,u,c,d,f),!0;case 6:return g.fn.call(g.context,u,c,d,f,p),!0}for(y=1,x=new Array(_-1);y<_;y++)x[y-1]=arguments[y];g.fn.apply(g.context,x)}else{var b=g.length,T;for(y=0;y<b;y++)switch(g[y].once&&this.removeListener(l,g[y].fn,void 0,!0),_){case 1:g[y].fn.call(g[y].context);break;case 2:g[y].fn.call(g[y].context,u);break;case 3:g[y].fn.call(g[y].context,u,c);break;case 4:g[y].fn.call(g[y].context,u,c,d);break;default:if(!x)for(T=1,x=new Array(_-1);T<_;T++)x[T-1]=arguments[T];g[y].fn.apply(g[y].context,x)}}return!0},o.prototype.on=function(l,u,c){return n(this,l,u,c,!1)},o.prototype.once=function(l,u,c){return n(this,l,u,c,!0)},o.prototype.removeListener=function(l,u,c,d){var f=e?e+l:l;if(!this._events[f])return this;if(!u)return a(this,f),this;var p=this._events[f];if(p.fn)p.fn===u&&(!d||p.once)&&(!c||p.context===c)&&a(this,f);else{for(var m=0,g=[],_=p.length;m<_;m++)(p[m].fn!==u||d&&!p[m].once||c&&p[m].context!==c)&&g.push(p[m]);g.length?this._events[f]=g.length===1?g[0]:g:a(this,f)}return this},o.prototype.removeAllListeners=function(l){var u;return l?(u=e?e+l:l,this._events[u]&&a(this,u)):(this._events=new s,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=e,o.EventEmitter=o,r.exports=o})(Ii);var Wu=Ii.exports,Ye=We(Wu),mr={exports:{}},Sg=mr.exports;mr.exports=gr;var Cg=mr.exports.default=gr;function gr(r,t,e){e=e||2;var s=t&&t.length,i=s?t[0]*e:r.length,n=Wa(r,0,i,e,!0),a=[];if(!n||n.next===n.prev)return a;var o,h,l,u,c,d,f;if(s&&(n=Qu(r,t,n,e)),r.length>80*e){o=l=r[0],h=u=r[1];for(var p=e;p<i;p+=e)c=r[p],d=r[p+1],c<o&&(o=c),d<h&&(h=d),c>l&&(l=c),d>u&&(u=d);f=Math.max(l-o,u-h),f=f!==0?32767/f:0}return cs(n,a,e,o,h,f,0),a}function Wa(r,t,e,s,i){var n,a;if(i===Di(r,t,e,s)>0)for(n=t;n<e;n+=s)a=Ka(n,r[n],r[n+1],a);else for(n=e-s;n>=t;n-=s)a=Ka(n,r[n],r[n+1],a);return a&&_r(a,a.next)&&(fs(a),a=a.next),a}function De(r,t){if(!r)return r;t||(t=r);var e=r,s;do if(s=!1,!e.steiner&&(_r(e,e.next)||ct(e.prev,e,e.next)===0)){if(fs(e),e=t=e.prev,e===e.next)break;s=!0}else e=e.next;while(s||e!==t);return t}function cs(r,t,e,s,i,n,a){if(r){!a&&n&&rc(r,s,i,n);for(var o=r,h,l;r.prev!==r.next;){if(h=r.prev,l=r.next,n?qu(r,s,i,n):Yu(r)){t.push(h.i/e|0),t.push(r.i/e|0),t.push(l.i/e|0),fs(r),r=l.next,o=l.next;continue}if(r=l,r===o){a?a===1?(r=Ku(De(r),t,e),cs(r,t,e,s,i,n,2)):a===2&&Zu(r,t,e,s,i,n):cs(De(r),t,e,s,i,n,1);break}}}}function Yu(r){var t=r.prev,e=r,s=r.next;if(ct(t,e,s)>=0)return!1;for(var i=t.x,n=e.x,a=s.x,o=t.y,h=e.y,l=s.y,u=i<n?i<a?i:a:n<a?n:a,c=o<h?o<l?o:l:h<l?h:l,d=i>n?i>a?i:a:n>a?n:a,f=o>h?o>l?o:l:h>l?h:l,p=s.next;p!==t;){if(p.x>=u&&p.x<=d&&p.y>=c&&p.y<=f&&qe(i,o,n,h,a,l,p.x,p.y)&&ct(p.prev,p,p.next)>=0)return!1;p=p.next}return!0}function qu(r,t,e,s){var i=r.prev,n=r,a=r.next;if(ct(i,n,a)>=0)return!1;for(var o=i.x,h=n.x,l=a.x,u=i.y,c=n.y,d=a.y,f=o<h?o<l?o:l:h<l?h:l,p=u<c?u<d?u:d:c<d?c:d,m=o>h?o>l?o:l:h>l?h:l,g=u>c?u>d?u:d:c>d?c:d,_=Pi(f,p,t,e,s),x=Pi(m,g,t,e,s),y=r.prevZ,b=r.nextZ;y&&y.z>=_&&b&&b.z<=x;){if(y.x>=f&&y.x<=m&&y.y>=p&&y.y<=g&&y!==i&&y!==a&&qe(o,u,h,c,l,d,y.x,y.y)&&ct(y.prev,y,y.next)>=0||(y=y.prevZ,b.x>=f&&b.x<=m&&b.y>=p&&b.y<=g&&b!==i&&b!==a&&qe(o,u,h,c,l,d,b.x,b.y)&&ct(b.prev,b,b.next)>=0))return!1;b=b.nextZ}for(;y&&y.z>=_;){if(y.x>=f&&y.x<=m&&y.y>=p&&y.y<=g&&y!==i&&y!==a&&qe(o,u,h,c,l,d,y.x,y.y)&&ct(y.prev,y,y.next)>=0)return!1;y=y.prevZ}for(;b&&b.z<=x;){if(b.x>=f&&b.x<=m&&b.y>=p&&b.y<=g&&b!==i&&b!==a&&qe(o,u,h,c,l,d,b.x,b.y)&&ct(b.prev,b,b.next)>=0)return!1;b=b.nextZ}return!0}function Ku(r,t,e){var s=r;do{var i=s.prev,n=s.next.next;!_r(i,n)&&Ya(i,s,s.next,n)&&ds(i,n)&&ds(n,i)&&(t.push(i.i/e|0),t.push(s.i/e|0),t.push(n.i/e|0),fs(s),fs(s.next),s=r=n),s=s.next}while(s!==r);return De(s)}function Zu(r,t,e,s,i,n){var a=r;do{for(var o=a.next.next;o!==a.prev;){if(a.i!==o.i&&ac(a,o)){var h=qa(a,o);a=De(a,a.next),h=De(h,h.next),cs(a,t,e,s,i,n,0),cs(h,t,e,s,i,n,0);return}o=o.next}a=a.next}while(a!==r)}function Qu(r,t,e,s){var i=[],n,a,o,h,l;for(n=0,a=t.length;n<a;n++)o=t[n]*s,h=n<a-1?t[n+1]*s:r.length,l=Wa(r,o,h,s,!1),l===l.next&&(l.steiner=!0),i.push(nc(l));for(i.sort(Ju),n=0;n<i.length;n++)e=tc(i[n],e);return e}function Ju(r,t){return r.x-t.x}function tc(r,t){var e=ec(r,t);if(!e)return t;var s=qa(e,r);return De(s,s.next),De(e,e.next)}function ec(r,t){var e=t,s=r.x,i=r.y,n=-1/0,a;do{if(i<=e.y&&i>=e.next.y&&e.next.y!==e.y){var o=e.x+(i-e.y)*(e.next.x-e.x)/(e.next.y-e.y);if(o<=s&&o>n&&(n=o,a=e.x<e.next.x?e:e.next,o===s))return a}e=e.next}while(e!==t);if(!a)return null;var h=a,l=a.x,u=a.y,c=1/0,d;e=a;do s>=e.x&&e.x>=l&&s!==e.x&&qe(i<u?s:n,i,l,u,i<u?n:s,i,e.x,e.y)&&(d=Math.abs(i-e.y)/(s-e.x),ds(e,r)&&(d<c||d===c&&(e.x>a.x||e.x===a.x&&sc(a,e)))&&(a=e,c=d)),e=e.next;while(e!==h);return a}function sc(r,t){return ct(r.prev,r,t.prev)<0&&ct(t.next,r,r.next)<0}function rc(r,t,e,s){var i=r;do i.z===0&&(i.z=Pi(i.x,i.y,t,e,s)),i.prevZ=i.prev,i.nextZ=i.next,i=i.next;while(i!==r);i.prevZ.nextZ=null,i.prevZ=null,ic(i)}function ic(r){var t,e,s,i,n,a,o,h,l=1;do{for(e=r,r=null,n=null,a=0;e;){for(a++,s=e,o=0,t=0;t<l&&(o++,s=s.nextZ,!!s);t++);for(h=l;o>0||h>0&&s;)o!==0&&(h===0||!s||e.z<=s.z)?(i=e,e=e.nextZ,o--):(i=s,s=s.nextZ,h--),n?n.nextZ=i:r=i,i.prevZ=n,n=i;e=s}n.nextZ=null,l*=2}while(a>1);return r}function Pi(r,t,e,s,i){return r=(r-e)*i|0,t=(t-s)*i|0,r=(r|r<<8)&16711935,r=(r|r<<4)&252645135,r=(r|r<<2)&858993459,r=(r|r<<1)&1431655765,t=(t|t<<8)&16711935,t=(t|t<<4)&252645135,t=(t|t<<2)&858993459,t=(t|t<<1)&1431655765,r|t<<1}function nc(r){var t=r,e=r;do(t.x<e.x||t.x===e.x&&t.y<e.y)&&(e=t),t=t.next;while(t!==r);return e}function qe(r,t,e,s,i,n,a,o){return(i-a)*(t-o)>=(r-a)*(n-o)&&(r-a)*(s-o)>=(e-a)*(t-o)&&(e-a)*(n-o)>=(i-a)*(s-o)}function ac(r,t){return r.next.i!==t.i&&r.prev.i!==t.i&&!oc(r,t)&&(ds(r,t)&&ds(t,r)&&hc(r,t)&&(ct(r.prev,r,t.prev)||ct(r,t.prev,t))||_r(r,t)&&ct(r.prev,r,r.next)>0&&ct(t.prev,t,t.next)>0)}function ct(r,t,e){return(t.y-r.y)*(e.x-t.x)-(t.x-r.x)*(e.y-t.y)}function _r(r,t){return r.x===t.x&&r.y===t.y}function Ya(r,t,e,s){var i=yr(ct(r,t,e)),n=yr(ct(r,t,s)),a=yr(ct(e,s,r)),o=yr(ct(e,s,t));return!!(i!==n&&a!==o||i===0&&vr(r,e,t)||n===0&&vr(r,s,t)||a===0&&vr(e,r,s)||o===0&&vr(e,t,s))}function vr(r,t,e){return t.x<=Math.max(r.x,e.x)&&t.x>=Math.min(r.x,e.x)&&t.y<=Math.max(r.y,e.y)&&t.y>=Math.min(r.y,e.y)}function yr(r){return r>0?1:r<0?-1:0}function oc(r,t){var e=r;do{if(e.i!==r.i&&e.next.i!==r.i&&e.i!==t.i&&e.next.i!==t.i&&Ya(e,e.next,r,t))return!0;e=e.next}while(e!==r);return!1}function ds(r,t){return ct(r.prev,r,r.next)<0?ct(r,t,r.next)>=0&&ct(r,r.prev,t)>=0:ct(r,t,r.prev)<0||ct(r,r.next,t)<0}function hc(r,t){var e=r,s=!1,i=(r.x+t.x)/2,n=(r.y+t.y)/2;do e.y>n!=e.next.y>n&&e.next.y!==e.y&&i<(e.next.x-e.x)*(n-e.y)/(e.next.y-e.y)+e.x&&(s=!s),e=e.next;while(e!==r);return s}function qa(r,t){var e=new Mi(r.i,r.x,r.y),s=new Mi(t.i,t.x,t.y),i=r.next,n=t.prev;return r.next=t,t.prev=r,e.next=i,i.prev=e,s.next=e,e.prev=s,n.next=s,s.prev=n,s}function Ka(r,t,e,s){var i=new Mi(r,t,e);return s?(i.next=s.next,i.prev=s,s.next.prev=i,s.next=i):(i.prev=i,i.next=i),i}function fs(r){r.next.prev=r.prev,r.prev.next=r.next,r.prevZ&&(r.prevZ.nextZ=r.nextZ),r.nextZ&&(r.nextZ.prevZ=r.prevZ)}function Mi(r,t,e){this.i=r,this.x=t,this.y=e,this.prev=null,this.next=null,this.z=0,this.prevZ=null,this.nextZ=null,this.steiner=!1}gr.deviation=function(r,t,e,s){var i=t&&t.length,n=i?t[0]*e:r.length,a=Math.abs(Di(r,0,n,e));if(i)for(var o=0,h=t.length;o<h;o++){var l=t[o]*e,u=o<h-1?t[o+1]*e:r.length;a-=Math.abs(Di(r,l,u,e))}var c=0;for(o=0;o<s.length;o+=3){var d=s[o]*e,f=s[o+1]*e,p=s[o+2]*e;c+=Math.abs((r[d]-r[p])*(r[f+1]-r[d+1])-(r[d]-r[f])*(r[p+1]-r[d+1]))}return a===0&&c===0?0:Math.abs((c-a)/a)};function Di(r,t,e,s){for(var i=0,n=t,a=e-s;n<e;n+=s)i+=(r[a]-r[n])*(r[n+1]+r[a+1]),a=n;return i}gr.flatten=function(r){for(var t=r[0][0].length,e={vertices:[],holes:[],dimensions:t},s=0,i=0;i<r.length;i++){for(var n=0;n<r[i].length;n++)for(var a=0;a<t;a++)e.vertices.push(r[i][n][a]);i>0&&(s+=r[i-1].length,e.holes.push(s))}return e};var lc=mr.exports,Za=We(lc),ps={},xr={exports:{}};/*! https://mths.be/punycode v1.3.2 by @mathias */var Rg=xr.exports;(function(r,t){(function(e){var s=t&&!t.nodeType&&t,i=r&&!r.nodeType&&r,n=typeof Ri=="object"&&Ri;(n.global===n||n.window===n||n.self===n)&&(e=n);var a,o=2147483647,h=36,l=1,u=26,c=38,d=700,f=72,p=128,m="-",g=/^xn--/,_=/[^\x20-\x7E]/,x=/[\x2E\u3002\uFF0E\uFF61]/g,y={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},b=h-l,T=Math.floor,S=String.fromCharCode,A;function w(F){throw RangeError(y[F])}function R(F,O){for(var Z=F.length,Q=[];Z--;)Q[Z]=O(F[Z]);return Q}function M(F,O){var Z=F.split("@"),Q="";Z.length>1&&(Q=Z[0]+"@",F=Z[1]),F=F.replace(x,".");var J=F.split("."),st=R(J,O).join(".");return Q+st}function H(F){for(var O=[],Z=0,Q=F.length,J,st;Z<Q;)J=F.charCodeAt(Z++),J>=55296&&J<=56319&&Z<Q?(st=F.charCodeAt(Z++),(st&64512)==56320?O.push(((J&1023)<<10)+(st&1023)+65536):(O.push(J),Z--)):O.push(J);return O}function B(F){return R(F,function(O){var Z="";return O>65535&&(O-=65536,Z+=S(O>>>10&1023|55296),O=56320|O&1023),Z+=S(O),Z}).join("")}function E(F){return F-48<10?F-22:F-65<26?F-65:F-97<26?F-97:h}function I(F,O){return F+22+75*(F<26)-((O!=0)<<5)}function V(F,O,Z){var Q=0;for(F=Z?T(F/d):F>>1,F+=T(F/O);F>b*u>>1;Q+=h)F=T(F/b);return T(Q+(b+1)*F/(F+c))}function q(F){var O=[],Z=F.length,Q,J=0,st=p,et=f,it,lt,vt,nt,ut,mt,yt,oe,he;for(it=F.lastIndexOf(m),it<0&&(it=0),lt=0;lt<it;++lt)F.charCodeAt(lt)>=128&&w("not-basic"),O.push(F.charCodeAt(lt));for(vt=it>0?it+1:0;vt<Z;){for(nt=J,ut=1,mt=h;vt>=Z&&w("invalid-input"),yt=E(F.charCodeAt(vt++)),(yt>=h||yt>T((o-J)/ut))&&w("overflow"),J+=yt*ut,oe=mt<=et?l:mt>=et+u?u:mt-et,!(yt<oe);mt+=h)he=h-oe,ut>T(o/he)&&w("overflow"),ut*=he;Q=O.length+1,et=V(J-nt,Q,nt==0),T(J/Q)>o-st&&w("overflow"),st+=T(J/Q),J%=Q,O.splice(J++,0,st)}return B(O)}function j(F){var O,Z,Q,J,st,et,it,lt,vt,nt,ut,mt=[],yt,oe,he,nr;for(F=H(F),yt=F.length,O=p,Z=0,st=f,et=0;et<yt;++et)ut=F[et],ut<128&&mt.push(S(ut));for(Q=J=mt.length,J&&mt.push(m);Q<yt;){for(it=o,et=0;et<yt;++et)ut=F[et],ut>=O&&ut<it&&(it=ut);for(oe=Q+1,it-O>T((o-Z)/oe)&&w("overflow"),Z+=(it-O)*oe,O=it,et=0;et<yt;++et)if(ut=F[et],ut<O&&++Z>o&&w("overflow"),ut==O){for(lt=Z,vt=h;nt=vt<=st?l:vt>=st+u?u:vt-st,!(lt<nt);vt+=h)nr=lt-nt,he=h-nt,mt.push(S(I(nt+nr%he,0))),lt=T(nr/he);mt.push(S(I(lt,0))),st=V(Z,oe,Q==J),Z=0,++Q}++Z,++O}return mt.join("")}function W(F){return M(F,function(O){return g.test(O)?q(O.slice(4).toLowerCase()):O})}function ht(F){return M(F,function(O){return _.test(O)?"xn--"+j(O):O})}if(a={version:"1.3.2",ucs2:{decode:H,encode:B},decode:q,encode:j,toASCII:ht,toUnicode:W},s&&i)if(r.exports==s)i.exports=a;else for(A in a)a.hasOwnProperty(A)&&(s[A]=a[A]);else e.punycode=a})(Ri)})(xr,xr.exports);var Qa=xr.exports,Ig=We(Qa),Ja={isString:function(r){return typeof r=="string"},isObject:function(r){return typeof r=="object"&&r!==null},isNull:function(r){return r===null},isNullOrUndefined:function(r){return r==null}},Pg=We(Ja),ms={};function uc(r,t){return Object.prototype.hasOwnProperty.call(r,t)}var to=function(r,t,e,s){t=t||"&",e=e||"=";var i={};if(typeof r!="string"||r.length===0)return i;var n=/\+/g;r=r.split(t);var a=1e3;s&&typeof s.maxKeys=="number"&&(a=s.maxKeys);var o=r.length;a>0&&o>a&&(o=a);for(var h=0;h<o;++h){var l=r[h].replace(n,"%20"),u=l.indexOf(e),c,d,f,p;u>=0?(c=l.substr(0,u),d=l.substr(u+1)):(c=l,d=""),f=decodeURIComponent(c),p=decodeURIComponent(d),uc(i,f)?Array.isArray(i[f])?i[f].push(p):i[f]=[i[f],p]:i[f]=p}return i},Mg=We(to),gs=function(r){switch(typeof r){case"string":return r;case"boolean":return r?"true":"false";case"number":return isFinite(r)?r:"";default:return""}},eo=function(r,t,e,s){return t=t||"&",e=e||"=",r===null&&(r=void 0),typeof r=="object"?Object.keys(r).map(function(i){var n=encodeURIComponent(gs(i))+e;return Array.isArray(r[i])?r[i].map(function(a){return n+encodeURIComponent(gs(a))}).join(t):n+encodeURIComponent(gs(r[i]))}).join(t):s?encodeURIComponent(gs(s))+e+encodeURIComponent(gs(r)):""},Dg=We(eo),cc,dc,Og=ms.decode=dc=ms.parse=to,Bg=ms.encode=cc=ms.stringify=eo,fc=Qa,Qt=Ja,pc=ps.parse=_s,mc=ps.resolve=Sc,Fg=ps.resolveObject=Cc,gc=ps.format=wc,Ng=ps.Url=Ft;function Ft(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}var _c=/^([a-z0-9.+-]+:)/i,vc=/:[0-9]*$/,yc=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,xc=["<",">",'"',"`"," ","\r",`
`,"	"],bc=["{","}","|","\\","^","`"].concat(xc),Oi=["'"].concat(bc),so=["%","/","?",";","#"].concat(Oi),ro=["/","?","#"],Tc=255,io=/^[+a-z0-9A-Z_-]{0,63}$/,Ec=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,Ac={javascript:!0,"javascript:":!0},Bi={javascript:!0,"javascript:":!0},Ke={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},Fi=ms;function _s(r,t,e){if(r&&Qt.isObject(r)&&r instanceof Ft)return r;var s=new Ft;return s.parse(r,t,e),s}Ft.prototype.parse=function(r,t,e){if(!Qt.isString(r))throw new TypeError("Parameter 'url' must be a string, not "+typeof r);var s=r.indexOf("?"),i=s!==-1&&s<r.indexOf("#")?"?":"#",n=r.split(i),a=/\\/g;n[0]=n[0].replace(a,"/"),r=n.join(i);var o=r;if(o=o.trim(),!e&&r.split("#").length===1){var h=yc.exec(o);if(h)return this.path=o,this.href=o,this.pathname=h[1],h[2]?(this.search=h[2],t?this.query=Fi.parse(this.search.substr(1)):this.query=this.search.substr(1)):t&&(this.search="",this.query={}),this}var l=_c.exec(o);if(l){l=l[0];var u=l.toLowerCase();this.protocol=u,o=o.substr(l.length)}if(e||l||o.match(/^\/\/[^@\/]+@[^@\/]+/)){var c=o.substr(0,2)==="//";c&&!(l&&Bi[l])&&(o=o.substr(2),this.slashes=!0)}if(!Bi[l]&&(c||l&&!Ke[l])){for(var d=-1,f=0;f<ro.length;f++){var p=o.indexOf(ro[f]);p!==-1&&(d===-1||p<d)&&(d=p)}var m,g;d===-1?g=o.lastIndexOf("@"):g=o.lastIndexOf("@",d),g!==-1&&(m=o.slice(0,g),o=o.slice(g+1),this.auth=decodeURIComponent(m)),d=-1;for(var f=0;f<so.length;f++){var p=o.indexOf(so[f]);p!==-1&&(d===-1||p<d)&&(d=p)}d===-1&&(d=o.length),this.host=o.slice(0,d),o=o.slice(d),this.parseHost(),this.hostname=this.hostname||"";var _=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!_)for(var x=this.hostname.split(/\./),f=0,y=x.length;f<y;f++){var b=x[f];if(b&&!b.match(io)){for(var T="",S=0,A=b.length;S<A;S++)b.charCodeAt(S)>127?T+="x":T+=b[S];if(!T.match(io)){var w=x.slice(0,f),R=x.slice(f+1),M=b.match(Ec);M&&(w.push(M[1]),R.unshift(M[2])),R.length&&(o="/"+R.join(".")+o),this.hostname=w.join(".");break}}}this.hostname.length>Tc?this.hostname="":this.hostname=this.hostname.toLowerCase(),_||(this.hostname=fc.toASCII(this.hostname));var H=this.port?":"+this.port:"",B=this.hostname||"";this.host=B+H,this.href+=this.host,_&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),o[0]!=="/"&&(o="/"+o))}if(!Ac[u])for(var f=0,y=Oi.length;f<y;f++){var E=Oi[f];if(o.indexOf(E)!==-1){var I=encodeURIComponent(E);I===E&&(I=escape(E)),o=o.split(E).join(I)}}var V=o.indexOf("#");V!==-1&&(this.hash=o.substr(V),o=o.slice(0,V));var q=o.indexOf("?");if(q!==-1?(this.search=o.substr(q),this.query=o.substr(q+1),t&&(this.query=Fi.parse(this.query)),o=o.slice(0,q)):t&&(this.search="",this.query={}),o&&(this.pathname=o),Ke[u]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){var H=this.pathname||"",j=this.search||"";this.path=H+j}return this.href=this.format(),this};function wc(r){return Qt.isString(r)&&(r=_s(r)),r instanceof Ft?r.format():Ft.prototype.format.call(r)}Ft.prototype.format=function(){var r=this.auth||"";r&&(r=encodeURIComponent(r),r=r.replace(/%3A/i,":"),r+="@");var t=this.protocol||"",e=this.pathname||"",s=this.hash||"",i=!1,n="";this.host?i=r+this.host:this.hostname&&(i=r+(this.hostname.indexOf(":")===-1?this.hostname:"["+this.hostname+"]"),this.port&&(i+=":"+this.port)),this.query&&Qt.isObject(this.query)&&Object.keys(this.query).length&&(n=Fi.stringify(this.query));var a=this.search||n&&"?"+n||"";return t&&t.substr(-1)!==":"&&(t+=":"),this.slashes||(!t||Ke[t])&&i!==!1?(i="//"+(i||""),e&&e.charAt(0)!=="/"&&(e="/"+e)):i||(i=""),s&&s.charAt(0)!=="#"&&(s="#"+s),a&&a.charAt(0)!=="?"&&(a="?"+a),e=e.replace(/[?#]/g,function(o){return encodeURIComponent(o)}),a=a.replace("#","%23"),t+i+e+a+s};function Sc(r,t){return _s(r,!1,!0).resolve(t)}Ft.prototype.resolve=function(r){return this.resolveObject(_s(r,!1,!0)).format()};function Cc(r,t){return r?_s(r,!1,!0).resolveObject(t):t}Ft.prototype.resolveObject=function(r){if(Qt.isString(r)){var t=new Ft;t.parse(r,!1,!0),r=t}for(var e=new Ft,s=Object.keys(this),i=0;i<s.length;i++){var n=s[i];e[n]=this[n]}if(e.hash=r.hash,r.href==="")return e.href=e.format(),e;if(r.slashes&&!r.protocol){for(var a=Object.keys(r),o=0;o<a.length;o++){var h=a[o];h!=="protocol"&&(e[h]=r[h])}return Ke[e.protocol]&&e.hostname&&!e.pathname&&(e.path=e.pathname="/"),e.href=e.format(),e}if(r.protocol&&r.protocol!==e.protocol){if(!Ke[r.protocol]){for(var l=Object.keys(r),u=0;u<l.length;u++){var c=l[u];e[c]=r[c]}return e.href=e.format(),e}if(e.protocol=r.protocol,!r.host&&!Bi[r.protocol]){for(var y=(r.pathname||"").split("/");y.length&&!(r.host=y.shift()););r.host||(r.host=""),r.hostname||(r.hostname=""),y[0]!==""&&y.unshift(""),y.length<2&&y.unshift(""),e.pathname=y.join("/")}else e.pathname=r.pathname;if(e.search=r.search,e.query=r.query,e.host=r.host||"",e.auth=r.auth,e.hostname=r.hostname||r.host,e.port=r.port,e.pathname||e.search){var d=e.pathname||"",f=e.search||"";e.path=d+f}return e.slashes=e.slashes||r.slashes,e.href=e.format(),e}var p=e.pathname&&e.pathname.charAt(0)==="/",m=r.host||r.pathname&&r.pathname.charAt(0)==="/",g=m||p||e.host&&r.pathname,_=g,x=e.pathname&&e.pathname.split("/")||[],y=r.pathname&&r.pathname.split("/")||[],b=e.protocol&&!Ke[e.protocol];if(b&&(e.hostname="",e.port=null,e.host&&(x[0]===""?x[0]=e.host:x.unshift(e.host)),e.host="",r.protocol&&(r.hostname=null,r.port=null,r.host&&(y[0]===""?y[0]=r.host:y.unshift(r.host)),r.host=null),g=g&&(y[0]===""||x[0]==="")),m)e.host=r.host||r.host===""?r.host:e.host,e.hostname=r.hostname||r.hostname===""?r.hostname:e.hostname,e.search=r.search,e.query=r.query,x=y;else if(y.length)x||(x=[]),x.pop(),x=x.concat(y),e.search=r.search,e.query=r.query;else if(!Qt.isNullOrUndefined(r.search)){if(b){e.hostname=e.host=x.shift();var T=e.host&&e.host.indexOf("@")>0?e.host.split("@"):!1;T&&(e.auth=T.shift(),e.host=e.hostname=T.shift())}return e.search=r.search,e.query=r.query,(!Qt.isNull(e.pathname)||!Qt.isNull(e.search))&&(e.path=(e.pathname?e.pathname:"")+(e.search?e.search:"")),e.href=e.format(),e}if(!x.length)return e.pathname=null,e.search?e.path="/"+e.search:e.path=null,e.href=e.format(),e;for(var S=x.slice(-1)[0],A=(e.host||r.host||x.length>1)&&(S==="."||S==="..")||S==="",w=0,R=x.length;R>=0;R--)S=x[R],S==="."?x.splice(R,1):S===".."?(x.splice(R,1),w++):w&&(x.splice(R,1),w--);if(!g&&!_)for(;w--;w)x.unshift("..");g&&x[0]!==""&&(!x[0]||x[0].charAt(0)!=="/")&&x.unshift(""),A&&x.join("/").substr(-1)!=="/"&&x.push("");var M=x[0]===""||x[0]&&x[0].charAt(0)==="/";if(b){e.hostname=e.host=M?"":x.length?x.shift():"";var T=e.host&&e.host.indexOf("@")>0?e.host.split("@"):!1;T&&(e.auth=T.shift(),e.host=e.hostname=T.shift())}return g=g||e.host&&x.length,g&&!M&&x.unshift(""),x.length?e.pathname=x.join("/"):(e.pathname=null,e.path=null),(!Qt.isNull(e.pathname)||!Qt.isNull(e.search))&&(e.path=(e.pathname?e.pathname:"")+(e.search?e.search:"")),e.auth=r.auth||e.auth,e.slashes=e.slashes||r.slashes,e.href=e.format(),e},Ft.prototype.parseHost=function(){var r=this.host,t=vc.exec(r);t&&(t=t[0],t!==":"&&(this.port=t.substr(1)),r=r.substr(0,r.length-t.length)),r&&(this.hostname=r)};const no={};function ao(r,t,e=3){if(no[t])return;let s=new Error().stack;typeof s=="undefined"?console.warn("PixiJS Deprecation Warning: ",`${t}
Deprecated since v${r}`):(s=s.split(`
`).splice(e).join(`
`),console.groupCollapsed?(console.groupCollapsed("%cPixiJS Deprecation Warning: %c%s","color:#614108;background:#fffbe6","font-weight:normal;color:#614108;background:#fffbe6",`${t}
Deprecated since v${r}`),console.warn(s),console.groupEnd()):(console.warn("PixiJS Deprecation Warning: ",`${t}
Deprecated since v${r}`),console.warn(s))),no[t]=!0}const Rc={get parse(){return pc},get format(){return gc},get resolve(){return mc}};function zt(r){if(typeof r!="string")throw new TypeError(`Path must be a string. Received ${JSON.stringify(r)}`)}function vs(r){return r.split("?")[0].split("#")[0]}function Ic(r){return r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function Pc(r,t,e){return r.replace(new RegExp(Ic(t),"g"),e)}function Mc(r,t){let e="",s=0,i=-1,n=0,a=-1;for(let o=0;o<=r.length;++o){if(o<r.length)a=r.charCodeAt(o);else{if(a===47)break;a=47}if(a===47){if(!(i===o-1||n===1))if(i!==o-1&&n===2){if(e.length<2||s!==2||e.charCodeAt(e.length-1)!==46||e.charCodeAt(e.length-2)!==46){if(e.length>2){const h=e.lastIndexOf("/");if(h!==e.length-1){h===-1?(e="",s=0):(e=e.slice(0,h),s=e.length-1-e.lastIndexOf("/")),i=o,n=0;continue}}else if(e.length===2||e.length===1){e="",s=0,i=o,n=0;continue}}t&&(e.length>0?e+="/..":e="..",s=2)}else e.length>0?e+=`/${r.slice(i+1,o)}`:e=r.slice(i+1,o),s=o-i-1;i=o,n=0}else a===46&&n!==-1?++n:n=-1}return e}const gt={toPosix(r){return Pc(r,"\\","/")},isUrl(r){return/^https?:/.test(this.toPosix(r))},isDataUrl(r){return/^data:([a-z]+\/[a-z0-9-+.]+(;[a-z0-9-.!#$%*+.{}|~`]+=[a-z0-9-.!#$%*+.{}()_|~`]+)*)?(;base64)?,([a-z0-9!$&',()*+;=\-._~:@\/?%\s<>]*?)$/i.test(r)},isBlobUrl(r){return r.startsWith("blob:")},hasProtocol(r){return/^[^/:]+:/.test(this.toPosix(r))},getProtocol(r){zt(r),r=this.toPosix(r);const t=/^file:\/\/\//.exec(r);if(t)return t[0];const e=/^[^/:]+:\/{0,2}/.exec(r);return e?e[0]:""},toAbsolute(r,t,e){if(zt(r),this.isDataUrl(r)||this.isBlobUrl(r))return r;const s=vs(this.toPosix(t!=null?t:N.ADAPTER.getBaseUrl())),i=vs(this.toPosix(e!=null?e:this.rootname(s)));return r=this.toPosix(r),r.startsWith("/")?gt.join(i,r.slice(1)):this.isAbsolute(r)?r:this.join(s,r)},normalize(r){if(zt(r),r.length===0)return".";if(this.isDataUrl(r)||this.isBlobUrl(r))return r;r=this.toPosix(r);let t="";const e=r.startsWith("/");this.hasProtocol(r)&&(t=this.rootname(r),r=r.slice(t.length));const s=r.endsWith("/");return r=Mc(r,!1),r.length>0&&s&&(r+="/"),e?`/${r}`:t+r},isAbsolute(r){return zt(r),r=this.toPosix(r),this.hasProtocol(r)?!0:r.startsWith("/")},join(...r){var t;if(r.length===0)return".";let e;for(let s=0;s<r.length;++s){const i=r[s];if(zt(i),i.length>0)if(e===void 0)e=i;else{const n=(t=r[s-1])!=null?t:"";this.joinExtensions.includes(this.extname(n).toLowerCase())?e+=`/../${i}`:e+=`/${i}`}}return e===void 0?".":this.normalize(e)},dirname(r){if(zt(r),r.length===0)return".";r=this.toPosix(r);let t=r.charCodeAt(0);const e=t===47;let s=-1,i=!0;const n=this.getProtocol(r),a=r;r=r.slice(n.length);for(let o=r.length-1;o>=1;--o)if(t=r.charCodeAt(o),t===47){if(!i){s=o;break}}else i=!1;return s===-1?e?"/":this.isUrl(a)?n+r:n:e&&s===1?"//":n+r.slice(0,s)},rootname(r){zt(r),r=this.toPosix(r);let t="";if(r.startsWith("/")?t="/":t=this.getProtocol(r),this.isUrl(r)){const e=r.indexOf("/",t.length);e!==-1?t=r.slice(0,e):t=r,t.endsWith("/")||(t+="/")}return t},basename(r,t){zt(r),t&&zt(t),r=vs(this.toPosix(r));let e=0,s=-1,i=!0,n;if(t!==void 0&&t.length>0&&t.length<=r.length){if(t.length===r.length&&t===r)return"";let a=t.length-1,o=-1;for(n=r.length-1;n>=0;--n){const h=r.charCodeAt(n);if(h===47){if(!i){e=n+1;break}}else o===-1&&(i=!1,o=n+1),a>=0&&(h===t.charCodeAt(a)?--a===-1&&(s=n):(a=-1,s=o))}return e===s?s=o:s===-1&&(s=r.length),r.slice(e,s)}for(n=r.length-1;n>=0;--n)if(r.charCodeAt(n)===47){if(!i){e=n+1;break}}else s===-1&&(i=!1,s=n+1);return s===-1?"":r.slice(e,s)},extname(r){zt(r),r=vs(this.toPosix(r));let t=-1,e=0,s=-1,i=!0,n=0;for(let a=r.length-1;a>=0;--a){const o=r.charCodeAt(a);if(o===47){if(!i){e=a+1;break}continue}s===-1&&(i=!1,s=a+1),o===46?t===-1?t=a:n!==1&&(n=1):t!==-1&&(n=-1)}return t===-1||s===-1||n===0||n===1&&t===s-1&&t===e+1?"":r.slice(t,s)},parse(r){zt(r);const t={root:"",dir:"",base:"",ext:"",name:""};if(r.length===0)return t;r=vs(this.toPosix(r));let e=r.charCodeAt(0);const s=this.isAbsolute(r);let i;const n="";t.root=this.rootname(r),s||this.hasProtocol(r)?i=1:i=0;let a=-1,o=0,h=-1,l=!0,u=r.length-1,c=0;for(;u>=i;--u){if(e=r.charCodeAt(u),e===47){if(!l){o=u+1;break}continue}h===-1&&(l=!1,h=u+1),e===46?a===-1?a=u:c!==1&&(c=1):a!==-1&&(c=-1)}return a===-1||h===-1||c===0||c===1&&a===h-1&&a===o+1?h!==-1&&(o===0&&s?t.base=t.name=r.slice(1,h):t.base=t.name=r.slice(o,h)):(o===0&&s?(t.name=r.slice(1,a),t.base=r.slice(1,h)):(t.name=r.slice(o,a),t.base=r.slice(o,h)),t.ext=r.slice(a,h)),t.dir=this.dirname(r),n&&(t.dir=n+t.dir),t},sep:"/",delimiter:":",joinExtensions:[".html"]};let Ni;async function oo(){return Ni!=null||(Ni=(async()=>{var r;const t=document.createElement("canvas").getContext("webgl");if(!t)return wt.UNPACK;const e=await new Promise(a=>{const o=document.createElement("video");o.onloadeddata=()=>a(o),o.onerror=()=>a(null),o.autoplay=!1,o.crossOrigin="anonymous",o.preload="auto",o.src="data:video/webm;base64,GkXfo59ChoEBQveBAULygQRC84EIQoKEd2VibUKHgQJChYECGFOAZwEAAAAAAAHTEU2bdLpNu4tTq4QVSalmU6yBoU27i1OrhBZUrmtTrIHGTbuMU6uEElTDZ1OsggEXTbuMU6uEHFO7a1OsggG97AEAAAAAAABZAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVSalmoCrXsYMPQkBNgIRMYXZmV0GETGF2ZkSJiEBEAAAAAAAAFlSua8yuAQAAAAAAAEPXgQFzxYgAAAAAAAAAAZyBACK1nIN1bmSIgQCGhVZfVlA5g4EBI+ODhAJiWgDglLCBArqBApqBAlPAgQFVsIRVuYEBElTDZ9Vzc9JjwItjxYgAAAAAAAAAAWfInEWjh0VOQ09ERVJEh49MYXZjIGxpYnZweC12cDlnyKJFo4hEVVJBVElPTkSHlDAwOjAwOjAwLjA0MDAwMDAwMAAAH0O2dcfngQCgwqGggQAAAIJJg0IAABAAFgA4JBwYSgAAICAAEb///4r+AAB1oZ2mm+6BAaWWgkmDQgAAEAAWADgkHBhKAAAgIABIQBxTu2uRu4+zgQC3iveBAfGCAXHwgQM=",o.load()});if(!e)return wt.UNPACK;const s=t.createTexture();t.bindTexture(t.TEXTURE_2D,s);const i=t.createFramebuffer();t.bindFramebuffer(t.FRAMEBUFFER,i),t.framebufferTexture2D(t.FRAMEBUFFER,t.COLOR_ATTACHMENT0,t.TEXTURE_2D,s,0),t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),t.pixelStorei(t.UNPACK_COLORSPACE_CONVERSION_WEBGL,t.NONE),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,t.RGBA,t.UNSIGNED_BYTE,e);const n=new Uint8Array(4);return t.readPixels(0,0,1,1,t.RGBA,t.UNSIGNED_BYTE,n),t.deleteFramebuffer(i),t.deleteTexture(s),(r=t.getExtension("WEBGL_lose_context"))==null||r.loseContext(),n[0]<=n[3]?wt.PMA:wt.UNPACK})()),Ni}function Dc(){}function Oc(){}let Li;function ho(){return typeof Li=="undefined"&&(Li=function(){var r;const t={stencil:!0,failIfMajorPerformanceCaveat:N.FAIL_IF_MAJOR_PERFORMANCE_CAVEAT};try{if(!N.ADAPTER.getWebGLRenderingContext())return!1;const e=N.ADAPTER.createCanvas();let s=e.getContext("webgl",t)||e.getContext("experimental-webgl",t);const i=!!((r=s==null?void 0:s.getContextAttributes())!=null&&r.stencil);if(s){const n=s.getExtension("WEBGL_lose_context");n&&n.loseContext()}return s=null,i}catch(e){return!1}}()),Li}var Bc={grad:.9,turn:360,rad:360/(2*Math.PI)},ce=function(r){return typeof r=="string"?r.length>0:typeof r=="number"},xt=function(r,t,e){return t===void 0&&(t=0),e===void 0&&(e=Math.pow(10,t)),Math.round(e*r)/e+0},Nt=function(r,t,e){return t===void 0&&(t=0),e===void 0&&(e=1),r>e?e:r>t?r:t},lo=function(r){return(r=isFinite(r)?r%360:0)>0?r:r+360},uo=function(r){return{r:Nt(r.r,0,255),g:Nt(r.g,0,255),b:Nt(r.b,0,255),a:Nt(r.a)}},Ui=function(r){return{r:xt(r.r),g:xt(r.g),b:xt(r.b),a:xt(r.a,3)}},Fc=/^#([0-9a-f]{3,8})$/i,br=function(r){var t=r.toString(16);return t.length<2?"0"+t:t},co=function(r){var t=r.r,e=r.g,s=r.b,i=r.a,n=Math.max(t,e,s),a=n-Math.min(t,e,s),o=a?n===t?(e-s)/a:n===e?2+(s-t)/a:4+(t-e)/a:0;return{h:60*(o<0?o+6:o),s:n?a/n*100:0,v:n/255*100,a:i}},fo=function(r){var t=r.h,e=r.s,s=r.v,i=r.a;t=t/360*6,e/=100,s/=100;var n=Math.floor(t),a=s*(1-e),o=s*(1-(t-n)*e),h=s*(1-(1-t+n)*e),l=n%6;return{r:255*[s,o,a,a,h,s][l],g:255*[h,s,s,o,a,a][l],b:255*[a,a,h,s,s,o][l],a:i}},po=function(r){return{h:lo(r.h),s:Nt(r.s,0,100),l:Nt(r.l,0,100),a:Nt(r.a)}},mo=function(r){return{h:xt(r.h),s:xt(r.s),l:xt(r.l),a:xt(r.a,3)}},go=function(r){return fo((e=(t=r).s,{h:t.h,s:(e*=((s=t.l)<50?s:100-s)/100)>0?2*e/(s+e)*100:0,v:s+e,a:t.a}));var t,e,s},ys=function(r){return{h:(t=co(r)).h,s:(i=(200-(e=t.s))*(s=t.v)/100)>0&&i<200?e*s/100/(i<=100?i:200-i)*100:0,l:i/2,a:t.a};var t,e,s,i},Nc=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s*,\s*([+-]?\d*\.?\d+)%\s*,\s*([+-]?\d*\.?\d+)%\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Lc=/^hsla?\(\s*([+-]?\d*\.?\d+)(deg|rad|grad|turn)?\s+([+-]?\d*\.?\d+)%\s+([+-]?\d*\.?\d+)%\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,Uc=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*,\s*([+-]?\d*\.?\d+)(%)?\s*(?:,\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,kc=/^rgba?\(\s*([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s+([+-]?\d*\.?\d+)(%)?\s*(?:\/\s*([+-]?\d*\.?\d+)(%)?\s*)?\)$/i,ki={string:[[function(r){var t=Fc.exec(r);return t?(r=t[1]).length<=4?{r:parseInt(r[0]+r[0],16),g:parseInt(r[1]+r[1],16),b:parseInt(r[2]+r[2],16),a:r.length===4?xt(parseInt(r[3]+r[3],16)/255,2):1}:r.length===6||r.length===8?{r:parseInt(r.substr(0,2),16),g:parseInt(r.substr(2,2),16),b:parseInt(r.substr(4,2),16),a:r.length===8?xt(parseInt(r.substr(6,2),16)/255,2):1}:null:null},"hex"],[function(r){var t=Uc.exec(r)||kc.exec(r);return t?t[2]!==t[4]||t[4]!==t[6]?null:uo({r:Number(t[1])/(t[2]?100/255:1),g:Number(t[3])/(t[4]?100/255:1),b:Number(t[5])/(t[6]?100/255:1),a:t[7]===void 0?1:Number(t[7])/(t[8]?100:1)}):null},"rgb"],[function(r){var t=Nc.exec(r)||Lc.exec(r);if(!t)return null;var e,s,i=po({h:(e=t[1],s=t[2],s===void 0&&(s="deg"),Number(e)*(Bc[s]||1)),s:Number(t[3]),l:Number(t[4]),a:t[5]===void 0?1:Number(t[5])/(t[6]?100:1)});return go(i)},"hsl"]],object:[[function(r){var t=r.r,e=r.g,s=r.b,i=r.a,n=i===void 0?1:i;return ce(t)&&ce(e)&&ce(s)?uo({r:Number(t),g:Number(e),b:Number(s),a:Number(n)}):null},"rgb"],[function(r){var t=r.h,e=r.s,s=r.l,i=r.a,n=i===void 0?1:i;if(!ce(t)||!ce(e)||!ce(s))return null;var a=po({h:Number(t),s:Number(e),l:Number(s),a:Number(n)});return go(a)},"hsl"],[function(r){var t=r.h,e=r.s,s=r.v,i=r.a,n=i===void 0?1:i;if(!ce(t)||!ce(e)||!ce(s))return null;var a=function(o){return{h:lo(o.h),s:Nt(o.s,0,100),v:Nt(o.v,0,100),a:Nt(o.a)}}({h:Number(t),s:Number(e),v:Number(s),a:Number(n)});return fo(a)},"hsv"]]},_o=function(r,t){for(var e=0;e<t.length;e++){var s=t[e][0](r);if(s)return[s,t[e][1]]}return[null,void 0]},vo=function(r){return typeof r=="string"?_o(r.trim(),ki.string):typeof r=="object"&&r!==null?_o(r,ki.object):[null,void 0]},Lg=function(r){return vo(r)[1]},Gi=function(r,t){var e=ys(r);return{h:e.h,s:Nt(e.s+100*t,0,100),l:e.l,a:e.a}},$i=function(r){return(299*r.r+587*r.g+114*r.b)/1e3/255},yo=function(r,t){var e=ys(r);return{h:e.h,s:e.s,l:Nt(e.l+100*t,0,100),a:e.a}},Tr=function(){function r(t){this.parsed=vo(t)[0],this.rgba=this.parsed||{r:0,g:0,b:0,a:1}}return r.prototype.isValid=function(){return this.parsed!==null},r.prototype.brightness=function(){return xt($i(this.rgba),2)},r.prototype.isDark=function(){return $i(this.rgba)<.5},r.prototype.isLight=function(){return $i(this.rgba)>=.5},r.prototype.toHex=function(){return t=Ui(this.rgba),e=t.r,s=t.g,i=t.b,a=(n=t.a)<1?br(xt(255*n)):"","#"+br(e)+br(s)+br(i)+a;var t,e,s,i,n,a},r.prototype.toRgb=function(){return Ui(this.rgba)},r.prototype.toRgbString=function(){return t=Ui(this.rgba),e=t.r,s=t.g,i=t.b,(n=t.a)<1?"rgba("+e+", "+s+", "+i+", "+n+")":"rgb("+e+", "+s+", "+i+")";var t,e,s,i,n},r.prototype.toHsl=function(){return mo(ys(this.rgba))},r.prototype.toHslString=function(){return t=mo(ys(this.rgba)),e=t.h,s=t.s,i=t.l,(n=t.a)<1?"hsla("+e+", "+s+"%, "+i+"%, "+n+")":"hsl("+e+", "+s+"%, "+i+"%)";var t,e,s,i,n},r.prototype.toHsv=function(){return t=co(this.rgba),{h:xt(t.h),s:xt(t.s),v:xt(t.v),a:xt(t.a,3)};var t},r.prototype.invert=function(){return Jt({r:255-(t=this.rgba).r,g:255-t.g,b:255-t.b,a:t.a});var t},r.prototype.saturate=function(t){return t===void 0&&(t=.1),Jt(Gi(this.rgba,t))},r.prototype.desaturate=function(t){return t===void 0&&(t=.1),Jt(Gi(this.rgba,-t))},r.prototype.grayscale=function(){return Jt(Gi(this.rgba,-1))},r.prototype.lighten=function(t){return t===void 0&&(t=.1),Jt(yo(this.rgba,t))},r.prototype.darken=function(t){return t===void 0&&(t=.1),Jt(yo(this.rgba,-t))},r.prototype.rotate=function(t){return t===void 0&&(t=15),this.hue(this.hue()+t)},r.prototype.alpha=function(t){return typeof t=="number"?Jt({r:(e=this.rgba).r,g:e.g,b:e.b,a:t}):xt(this.rgba.a,3);var e},r.prototype.hue=function(t){var e=ys(this.rgba);return typeof t=="number"?Jt({h:t,s:e.s,l:e.l,a:e.a}):xt(e.h)},r.prototype.isEqual=function(t){return this.toHex()===Jt(t).toHex()},r}(),Jt=function(r){return r instanceof Tr?r:new Tr(r)},xo=[],Gc=function(r){r.forEach(function(t){xo.indexOf(t)<0&&(t(Tr,ki),xo.push(t))})},Ug=function(){return new Tr({r:255*Math.random(),g:255*Math.random(),b:255*Math.random()})};function $c(r,t){var e={white:"#ffffff",bisque:"#ffe4c4",blue:"#0000ff",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",antiquewhite:"#faebd7",aqua:"#00ffff",azure:"#f0ffff",whitesmoke:"#f5f5f5",papayawhip:"#ffefd5",plum:"#dda0dd",blanchedalmond:"#ffebcd",black:"#000000",gold:"#ffd700",goldenrod:"#daa520",gainsboro:"#dcdcdc",cornsilk:"#fff8dc",cornflowerblue:"#6495ed",burlywood:"#deb887",aquamarine:"#7fffd4",beige:"#f5f5dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkkhaki:"#bdb76b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",peachpuff:"#ffdab9",darkmagenta:"#8b008b",darkred:"#8b0000",darkorchid:"#9932cc",darkorange:"#ff8c00",darkslateblue:"#483d8b",gray:"#808080",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",deeppink:"#ff1493",deepskyblue:"#00bfff",wheat:"#f5deb3",firebrick:"#b22222",floralwhite:"#fffaf0",ghostwhite:"#f8f8ff",darkviolet:"#9400d3",magenta:"#ff00ff",green:"#008000",dodgerblue:"#1e90ff",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",blueviolet:"#8a2be2",forestgreen:"#228b22",lawngreen:"#7cfc00",indianred:"#cd5c5c",indigo:"#4b0082",fuchsia:"#ff00ff",brown:"#a52a2a",maroon:"#800000",mediumblue:"#0000cd",lightcoral:"#f08080",darkturquoise:"#00ced1",lightcyan:"#e0ffff",ivory:"#fffff0",lightyellow:"#ffffe0",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",linen:"#faf0e6",mediumaquamarine:"#66cdaa",lemonchiffon:"#fffacd",lime:"#00ff00",khaki:"#f0e68c",mediumseagreen:"#3cb371",limegreen:"#32cd32",mediumspringgreen:"#00fa9a",lightskyblue:"#87cefa",lightblue:"#add8e6",midnightblue:"#191970",lightpink:"#ffb6c1",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",mintcream:"#f5fffa",lightslategray:"#778899",lightslategrey:"#778899",navajowhite:"#ffdead",navy:"#000080",mediumvioletred:"#c71585",powderblue:"#b0e0e6",palegoldenrod:"#eee8aa",oldlace:"#fdf5e6",paleturquoise:"#afeeee",mediumturquoise:"#48d1cc",mediumorchid:"#ba55d3",rebeccapurple:"#663399",lightsteelblue:"#b0c4de",mediumslateblue:"#7b68ee",thistle:"#d8bfd8",tan:"#d2b48c",orchid:"#da70d6",mediumpurple:"#9370db",purple:"#800080",pink:"#ffc0cb",skyblue:"#87ceeb",springgreen:"#00ff7f",palegreen:"#98fb98",red:"#ff0000",yellow:"#ffff00",slateblue:"#6a5acd",lavenderblush:"#fff0f5",peru:"#cd853f",palevioletred:"#db7093",violet:"#ee82ee",teal:"#008080",slategray:"#708090",slategrey:"#708090",aliceblue:"#f0f8ff",darkseagreen:"#8fbc8f",darkolivegreen:"#556b2f",greenyellow:"#adff2f",seagreen:"#2e8b57",seashell:"#fff5ee",tomato:"#ff6347",silver:"#c0c0c0",sienna:"#a0522d",lavender:"#e6e6fa",lightgreen:"#90ee90",orange:"#ffa500",orangered:"#ff4500",steelblue:"#4682b4",royalblue:"#4169e1",turquoise:"#40e0d0",yellowgreen:"#9acd32",salmon:"#fa8072",saddlebrown:"#8b4513",sandybrown:"#f4a460",rosybrown:"#bc8f8f",darksalmon:"#e9967a",lightgoldenrodyellow:"#fafad2",snow:"#fffafa",lightgrey:"#d3d3d3",lightgray:"#d3d3d3",dimgray:"#696969",dimgrey:"#696969",olivedrab:"#6b8e23",olive:"#808000"},s={};for(var i in e)s[e[i]]=i;var n={};r.prototype.toName=function(a){if(!(this.rgba.a||this.rgba.r||this.rgba.g||this.rgba.b))return"transparent";var o,h,l=s[this.toHex()];if(l)return l;if(a!=null&&a.closest){var u=this.toRgb(),c=1/0,d="black";if(!n.length)for(var f in e)n[f]=new r(e[f]).toRgb();for(var p in e){var m=(o=u,h=n[p],Math.pow(o.r-h.r,2)+Math.pow(o.g-h.g,2)+Math.pow(o.b-h.b,2));m<c&&(c=m,d=p)}return d}},t.string.push([function(a){var o=a.toLowerCase(),h=o==="transparent"?"#0000":e[o];return h?new r(h).toRgb():null},"name"])}var Hc=Object.defineProperty,bo=Object.getOwnPropertySymbols,Vc=Object.prototype.hasOwnProperty,jc=Object.prototype.propertyIsEnumerable,To=(r,t,e)=>t in r?Hc(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,Xc=(r,t)=>{for(var e in t||(t={}))Vc.call(t,e)&&To(r,e,t[e]);if(bo)for(var e of bo(t))jc.call(t,e)&&To(r,e,t[e]);return r};Gc([$c]);const Ze=class bi{constructor(t=16777215){this._value=null,this._components=new Float32Array(4),this._components.fill(1),this._int=16777215,this.value=t}get red(){return this._components[0]}get green(){return this._components[1]}get blue(){return this._components[2]}get alpha(){return this._components[3]}setValue(t){return this.value=t,this}set value(t){if(t instanceof bi)this._value=this.cloneSource(t._value),this._int=t._int,this._components.set(t._components);else{if(t===null)throw new Error("Cannot set PIXI.Color#value to null");(this._value===null||!this.isSourceEqual(this._value,t))&&(this.normalize(t),this._value=this.cloneSource(t))}}get value(){return this._value}cloneSource(t){return typeof t=="string"||typeof t=="number"||t instanceof Number||t===null?t:Array.isArray(t)||ArrayBuffer.isView(t)?t.slice(0):typeof t=="object"&&t!==null?Xc({},t):t}isSourceEqual(t,e){const s=typeof t;if(s!==typeof e)return!1;if(s==="number"||s==="string"||t instanceof Number)return t===e;if(Array.isArray(t)&&Array.isArray(e)||ArrayBuffer.isView(t)&&ArrayBuffer.isView(e))return t.length!==e.length?!1:t.every((i,n)=>i===e[n]);if(t!==null&&e!==null){const i=Object.keys(t),n=Object.keys(e);return i.length!==n.length?!1:i.every(a=>t[a]===e[a])}return t===e}toRgba(){const[t,e,s,i]=this._components;return{r:t,g:e,b:s,a:i}}toRgb(){const[t,e,s]=this._components;return{r:t,g:e,b:s}}toRgbaString(){const[t,e,s]=this.toUint8RgbArray();return`rgba(${t},${e},${s},${this.alpha})`}toUint8RgbArray(t){const[e,s,i]=this._components;return t=t!=null?t:[],t[0]=Math.round(e*255),t[1]=Math.round(s*255),t[2]=Math.round(i*255),t}toRgbArray(t){t=t!=null?t:[];const[e,s,i]=this._components;return t[0]=e,t[1]=s,t[2]=i,t}toNumber(){return this._int}toLittleEndianNumber(){const t=this._int;return(t>>16)+(t&65280)+((t&255)<<16)}multiply(t){const[e,s,i,n]=bi.temp.setValue(t)._components;return this._components[0]*=e,this._components[1]*=s,this._components[2]*=i,this._components[3]*=n,this.refreshInt(),this._value=null,this}premultiply(t,e=!0){return e&&(this._components[0]*=t,this._components[1]*=t,this._components[2]*=t),this._components[3]=t,this.refreshInt(),this._value=null,this}toPremultiplied(t,e=!0){if(t===1)return(255<<24)+this._int;if(t===0)return e?0:this._int;let s=this._int>>16&255,i=this._int>>8&255,n=this._int&255;return e&&(s=s*t+.5|0,i=i*t+.5|0,n=n*t+.5|0),(t*255<<24)+(s<<16)+(i<<8)+n}toHex(){const t=this._int.toString(16);return`#${"000000".substring(0,6-t.length)+t}`}toHexa(){const t=Math.round(this._components[3]*255).toString(16);return this.toHex()+"00".substring(0,2-t.length)+t}setAlpha(t){return this._components[3]=this._clamp(t),this}round(t){const[e,s,i]=this._components;return this._components[0]=Math.round(e*t)/t,this._components[1]=Math.round(s*t)/t,this._components[2]=Math.round(i*t)/t,this.refreshInt(),this._value=null,this}toArray(t){t=t!=null?t:[];const[e,s,i,n]=this._components;return t[0]=e,t[1]=s,t[2]=i,t[3]=n,t}normalize(t){let e,s,i,n;if((typeof t=="number"||t instanceof Number)&&t>=0&&t<=16777215){const a=t;e=(a>>16&255)/255,s=(a>>8&255)/255,i=(a&255)/255,n=1}else if((Array.isArray(t)||t instanceof Float32Array)&&t.length>=3&&t.length<=4)t=this._clamp(t),[e,s,i,n=1]=t;else if((t instanceof Uint8Array||t instanceof Uint8ClampedArray)&&t.length>=3&&t.length<=4)t=this._clamp(t,0,255),[e,s,i,n=255]=t,e/=255,s/=255,i/=255,n/=255;else if(typeof t=="string"||typeof t=="object"){if(typeof t=="string"){const o=bi.HEX_PATTERN.exec(t);o&&(t=`#${o[2]}`)}const a=Jt(t);a.isValid()&&({r:e,g:s,b:i,a:n}=a.rgba,e/=255,s/=255,i/=255)}if(e!==void 0)this._components[0]=e,this._components[1]=s,this._components[2]=i,this._components[3]=n,this.refreshInt();else throw new Error(`Unable to convert color ${t}`)}refreshInt(){this._clamp(this._components);const[t,e,s]=this._components;this._int=(t*255<<16)+(e*255<<8)+(s*255|0)}_clamp(t,e=0,s=1){return typeof t=="number"?Math.min(Math.max(t,e),s):(t.forEach((i,n)=>{t[n]=Math.min(Math.max(i,e),s)}),t)}};Ze.shared=new Ze,Ze.temp=new Ze,Ze.HEX_PATTERN=/^(#|0x)?(([a-f0-9]{3}){1,2}([a-f0-9]{2})?)$/i;let Y=Ze;function zc(r,t=[]){return Y.shared.setValue(r).toRgbArray(t)}function Eo(r){return Y.shared.setValue(r).toHex()}function Wc(r){return Y.shared.setValue(r).toNumber()}function Ao(r){return Y.shared.setValue(r).toNumber()}function Yc(){const r=[],t=[];for(let s=0;s<32;s++)r[s]=s,t[s]=s;r[C.NORMAL_NPM]=C.NORMAL,r[C.ADD_NPM]=C.ADD,r[C.SCREEN_NPM]=C.SCREEN,t[C.NORMAL]=C.NORMAL_NPM,t[C.ADD]=C.ADD_NPM,t[C.SCREEN]=C.SCREEN_NPM;const e=[];return e.push(t),e.push(r),e}const Hi=Yc();function Vi(r,t){return Hi[t?1:0][r]}function qc(r,t,e,s=!0){return Y.shared.setValue(r).premultiply(t,s).toArray(e!=null?e:new Float32Array(4))}function Kc(r,t){return Y.shared.setValue(r).toPremultiplied(t)}function Zc(r,t,e,s=!0){return Y.shared.setValue(r).premultiply(t,s).toArray(e!=null?e:new Float32Array(4))}const wo=/^\s*data:(?:([\w-]+)\/([\w+.-]+))?(?:;charset=([\w-]+))?(?:;(base64))?,(.*)/i;function So(r,t=null){const e=r*6;if(t=t||new Uint16Array(e),t.length!==e)throw new Error(`Out buffer length is incorrect, got ${t.length} and expected ${e}`);for(let s=0,i=0;s<e;s+=6,i+=4)t[s+0]=i+0,t[s+1]=i+1,t[s+2]=i+2,t[s+3]=i+0,t[s+4]=i+2,t[s+5]=i+3;return t}function Er(r){if(r.BYTES_PER_ELEMENT===4)return r instanceof Float32Array?"Float32Array":r instanceof Uint32Array?"Uint32Array":"Int32Array";if(r.BYTES_PER_ELEMENT===2){if(r instanceof Uint16Array)return"Uint16Array"}else if(r.BYTES_PER_ELEMENT===1&&r instanceof Uint8Array)return"Uint8Array";return null}const Qc={Float32Array,Uint32Array,Int32Array,Uint8Array};function Jc(r,t){let e=0,s=0;const i={};for(let h=0;h<r.length;h++)s+=t[h],e+=r[h].length;const n=new ArrayBuffer(e*4);let a=null,o=0;for(let h=0;h<r.length;h++){const l=t[h],u=r[h],c=Er(u);i[c]||(i[c]=new Qc[c](n)),a=i[c];for(let d=0;d<u.length;d++){const f=(d/l|0)*s+o,p=d%l;a[f+p]=u[d]}o+=l}return new Float32Array(n)}function xs(r){return r+=r===0?1:0,--r,r|=r>>>1,r|=r>>>2,r|=r>>>4,r|=r>>>8,r|=r>>>16,r+1}function ji(r){return!(r&r-1)&&!!r}function Xi(r){let t=(r>65535?1:0)<<4;r>>>=t;let e=(r>255?1:0)<<3;return r>>>=e,t|=e,e=(r>15?1:0)<<2,r>>>=e,t|=e,e=(r>3?1:0)<<1,r>>>=e,t|=e,t|r>>1}function Oe(r,t,e){const s=r.length;let i;if(t>=s||e===0)return;e=t+e>s?s-t:e;const n=s-e;for(i=t;i<n;++i)r[i]=r[i+e];r.length=n}function de(r){return r===0?0:r<0?-1:1}let td=0;function Te(){return++td}const zi=class{constructor(t,e,s,i){this.left=t,this.top=e,this.right=s,this.bottom=i}get width(){return this.right-this.left}get height(){return this.bottom-this.top}isEmpty(){return this.left===this.right||this.top===this.bottom}};zi.EMPTY=new zi(0,0,0,0);let Wi=zi;const Yi={},St=Object.create(null),It=Object.create(null);function ed(){let r;for(r in St)St[r].destroy();for(r in It)It[r].destroy()}function sd(){let r;for(r in St)delete St[r];for(r in It)delete It[r]}class bs{constructor(t,e,s){this._canvas=N.ADAPTER.createCanvas(),this._context=this._canvas.getContext("2d"),this.resolution=s||N.RESOLUTION,this.resize(t,e)}clear(){this._checkDestroyed(),this._context.setTransform(1,0,0,1,0,0),this._context.clearRect(0,0,this._canvas.width,this._canvas.height)}resize(t,e){this._checkDestroyed(),this._canvas.width=Math.round(t*this.resolution),this._canvas.height=Math.round(e*this.resolution)}destroy(){this._context=null,this._canvas=null}get width(){return this._checkDestroyed(),this._canvas.width}set width(t){this._checkDestroyed(),this._canvas.width=Math.round(t)}get height(){return this._checkDestroyed(),this._canvas.height}set height(t){this._checkDestroyed(),this._canvas.height=Math.round(t)}get canvas(){return this._checkDestroyed(),this._canvas}get context(){return this._checkDestroyed(),this._context}_checkDestroyed(){this._canvas}}function Co(r,t,e){for(let s=0,i=4*e*t;s<t;++s,i+=4)if(r[i+3]!==0)return!1;return!0}function Ro(r,t,e,s,i){const n=4*t;for(let a=s,o=s*n+4*e;a<=i;++a,o+=n)if(r[o+3]!==0)return!1;return!0}function Io(r){const{width:t,height:e}=r,s=r.getContext("2d",{willReadFrequently:!0});if(s===null)throw new TypeError("Failed to get canvas 2D context");const i=s.getImageData(0,0,t,e).data;let n=0,a=0,o=t-1,h=e-1;for(;a<e&&Co(i,t,a);)++a;if(a===e)return Wi.EMPTY;for(;Co(i,t,h);)--h;for(;Ro(i,t,n,a,h);)++n;for(;Ro(i,t,o,a,h);)--o;return++o,++h,new Wi(n,a,o,h)}function Po(r){const t=Io(r),{width:e,height:s}=t;let i=null;if(!t.isEmpty()){const n=r.getContext("2d");if(n===null)throw new TypeError("Failed to get canvas 2D context");i=n.getImageData(t.left,t.top,e,s)}return{width:e,height:s,data:i}}function rd(r){const t=wo.exec(r);if(t)return{mediaType:t[1]?t[1].toLowerCase():void 0,subType:t[2]?t[2].toLowerCase():void 0,charset:t[3]?t[3].toLowerCase():void 0,encoding:t[4]?t[4].toLowerCase():void 0,data:t[5]}}function Mo(r,t=globalThis.location){if(r.startsWith("data:"))return"";t=t||globalThis.location;const e=new URL(r,document.baseURI);return e.hostname!==t.hostname||e.port!==t.port||e.protocol!==t.protocol?"anonymous":""}function te(r,t=1){var e;const s=(e=N.RETINA_PREFIX)==null?void 0:e.exec(r);return s?parseFloat(s[1]):t}var Do={__proto__:null,BaseTextureCache:It,BoundingBox:Wi,CanvasRenderTarget:bs,DATA_URI:wo,EventEmitter:Ye,ProgramCache:Yi,TextureCache:St,clearTextureCache:sd,correctBlendMode:Vi,createIndicesForQuads:So,decomposeDataUri:rd,deprecation:ao,destroyTextureCache:ed,detectVideoAlphaMode:oo,determineCrossOrigin:Mo,earcut:Za,getBufferType:Er,getCanvasBoundingBox:Io,getResolutionOfUrl:te,hex2rgb:zc,hex2string:Eo,interleaveTypedArrays:Jc,isMobile:Xt,isPow2:ji,isWebGLSupported:ho,log2:Xi,nextPow2:xs,path:gt,premultiplyBlendMode:Hi,premultiplyRgba:qc,premultiplyTint:Kc,premultiplyTintToRgba:Zc,removeItems:Oe,rgb2hex:Ao,sayHello:Oc,sign:de,skipHello:Dc,string2hex:Wc,trimCanvas:Po,uid:Te,url:Rc},id=Object.defineProperty,nd=Object.defineProperties,ad=Object.getOwnPropertyDescriptors,Oo=Object.getOwnPropertySymbols,od=Object.prototype.hasOwnProperty,hd=Object.prototype.propertyIsEnumerable,Bo=(r,t,e)=>t in r?id(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,Fo=(r,t)=>{for(var e in t||(t={}))od.call(t,e)&&Bo(r,e,t[e]);if(Oo)for(var e of Oo(t))hd.call(t,e)&&Bo(r,e,t[e]);return r},ld=(r,t)=>nd(r,ad(t)),D=(r=>(r.Renderer="renderer",r.Application="application",r.RendererSystem="renderer-webgl-system",r.RendererPlugin="renderer-webgl-plugin",r.CanvasRendererSystem="renderer-canvas-system",r.CanvasRendererPlugin="renderer-canvas-plugin",r.Asset="asset",r.LoadParser="load-parser",r.ResolveParser="resolve-parser",r.CacheParser="cache-parser",r.DetectionParser="detection-parser",r))(D||{});const qi=r=>{if(typeof r=="function"||typeof r=="object"&&r.extension){const t=typeof r.extension!="object"?{type:r.extension}:r.extension;r=ld(Fo({},t),{ref:r})}if(typeof r=="object")r=Fo({},r);else throw new Error("Invalid extension type");return typeof r.type=="string"&&(r.type=[r.type]),r},No=(r,t)=>{var e;return(e=qi(r).priority)!=null?e:t},U={_addHandlers:{},_removeHandlers:{},_queue:{},remove(...r){return r.map(qi).forEach(t=>{t.type.forEach(e=>{var s,i;return(i=(s=this._removeHandlers)[e])==null?void 0:i.call(s,t)})}),this},add(...r){return r.map(qi).forEach(t=>{t.type.forEach(e=>{var s,i;const n=this._addHandlers,a=this._queue;n[e]?(i=n[e])==null||i.call(n,t):(a[e]=a[e]||[],(s=a[e])==null||s.push(t))})}),this},handle(r,t,e){var s;const i=this._addHandlers,n=this._removeHandlers;i[r]=t,n[r]=e;const a=this._queue;return a[r]&&((s=a[r])==null||s.forEach(o=>t(o)),delete a[r]),this},handleByMap(r,t){return this.handle(r,e=>{e.name&&(t[e.name]=e.ref)},e=>{e.name&&delete t[e.name]})},handleByList(r,t,e=-1){return this.handle(r,s=>{t.includes(s.ref)||(t.push(s.ref),t.sort((i,n)=>No(n,e)-No(i,e)))},s=>{const i=t.indexOf(s.ref);i!==-1&&t.splice(i,1)})}};class Ar{constructor(t){typeof t=="number"?this.rawBinaryData=new ArrayBuffer(t):t instanceof Uint8Array?this.rawBinaryData=t.buffer:this.rawBinaryData=t,this.uint32View=new Uint32Array(this.rawBinaryData),this.float32View=new Float32Array(this.rawBinaryData)}get int8View(){return this._int8View||(this._int8View=new Int8Array(this.rawBinaryData)),this._int8View}get uint8View(){return this._uint8View||(this._uint8View=new Uint8Array(this.rawBinaryData)),this._uint8View}get int16View(){return this._int16View||(this._int16View=new Int16Array(this.rawBinaryData)),this._int16View}get uint16View(){return this._uint16View||(this._uint16View=new Uint16Array(this.rawBinaryData)),this._uint16View}get int32View(){return this._int32View||(this._int32View=new Int32Array(this.rawBinaryData)),this._int32View}view(t){return this[`${t}View`]}destroy(){this.rawBinaryData=null,this._int8View=null,this._uint8View=null,this._int16View=null,this._uint16View=null,this._int32View=null,this.uint32View=null,this.float32View=null}static sizeOf(t){switch(t){case"int8":case"uint8":return 1;case"int16":case"uint16":return 2;case"int32":case"uint32":case"float32":return 4;default:throw new Error(`${t} isn't a valid view type`)}}}const ud=["precision mediump float;","void main(void){","float test = 0.1;","%forloop%","gl_FragColor = vec4(0.0);","}"].join(`
`);function cd(r){let t="";for(let e=0;e<r;++e)e>0&&(t+=`
else `),e<r-1&&(t+=`if(test == ${e}.0){}`);return t}function Lo(r,t){if(r===0)throw new Error("Invalid value of `0` passed to `checkMaxIfStatementsInShader`");const e=t.createShader(t.FRAGMENT_SHADER);for(;;){const s=ud.replace(/%forloop%/gi,cd(r));if(t.shaderSource(e,s),t.compileShader(e),!t.getShaderParameter(e,t.COMPILE_STATUS))r=r/2|0;else break}return r}const Ki=0,Zi=1,Qi=2,Ji=3,tn=4,en=5;class ee{constructor(){this.data=0,this.blendMode=C.NORMAL,this.polygonOffset=0,this.blend=!0,this.depthMask=!0}get blend(){return!!(this.data&1<<Ki)}set blend(t){!!(this.data&1<<Ki)!==t&&(this.data^=1<<Ki)}get offsets(){return!!(this.data&1<<Zi)}set offsets(t){!!(this.data&1<<Zi)!==t&&(this.data^=1<<Zi)}get culling(){return!!(this.data&1<<Qi)}set culling(t){!!(this.data&1<<Qi)!==t&&(this.data^=1<<Qi)}get depthTest(){return!!(this.data&1<<Ji)}set depthTest(t){!!(this.data&1<<Ji)!==t&&(this.data^=1<<Ji)}get depthMask(){return!!(this.data&1<<en)}set depthMask(t){!!(this.data&1<<en)!==t&&(this.data^=1<<en)}get clockwiseFrontFace(){return!!(this.data&1<<tn)}set clockwiseFrontFace(t){!!(this.data&1<<tn)!==t&&(this.data^=1<<tn)}get blendMode(){return this._blendMode}set blendMode(t){this.blend=t!==C.NONE,this._blendMode=t}get polygonOffset(){return this._polygonOffset}set polygonOffset(t){this.offsets=!!t,this._polygonOffset=t}static for2d(){const t=new ee;return t.depthTest=!1,t.blend=!0,t}}const wr=[];function sn(r,t){if(!r)return null;let e="";if(typeof r=="string"){const s=/\.(\w{3,4})(?:$|\?|#)/i.exec(r);s&&(e=s[1].toLowerCase())}for(let s=wr.length-1;s>=0;--s){const i=wr[s];if(i.test&&i.test(r,e))return new i(r,t)}throw new Error("Unrecognized source type to auto-detect Resource")}class Pt{constructor(t){this.items=[],this._name=t,this._aliasCount=0}emit(t,e,s,i,n,a,o,h){if(arguments.length>8)throw new Error("max arguments reached");const{name:l,items:u}=this;this._aliasCount++;for(let c=0,d=u.length;c<d;c++)u[c][l](t,e,s,i,n,a,o,h);return u===this.items&&this._aliasCount--,this}ensureNonAliasedItems(){this._aliasCount>0&&this.items.length>1&&(this._aliasCount=0,this.items=this.items.slice(0))}add(t){return t[this._name]&&(this.ensureNonAliasedItems(),this.remove(t),this.items.push(t)),this}remove(t){const e=this.items.indexOf(t);return e!==-1&&(this.ensureNonAliasedItems(),this.items.splice(e,1)),this}contains(t){return this.items.includes(t)}removeAll(){return this.ensureNonAliasedItems(),this.items.length=0,this}destroy(){this.removeAll(),this.items.length=0,this._name=""}get empty(){return this.items.length===0}get name(){return this._name}}Object.defineProperties(Pt.prototype,{dispatch:{value:Pt.prototype.emit},run:{value:Pt.prototype.emit}});class Qe{constructor(t=0,e=0){this._width=t,this._height=e,this.destroyed=!1,this.internal=!1,this.onResize=new Pt("setRealSize"),this.onUpdate=new Pt("update"),this.onError=new Pt("onError")}bind(t){this.onResize.add(t),this.onUpdate.add(t),this.onError.add(t),(this._width||this._height)&&this.onResize.emit(this._width,this._height)}unbind(t){this.onResize.remove(t),this.onUpdate.remove(t),this.onError.remove(t)}resize(t,e){(t!==this._width||e!==this._height)&&(this._width=t,this._height=e,this.onResize.emit(t,e))}get valid(){return!!this._width&&!!this._height}update(){this.destroyed||this.onUpdate.emit()}load(){return Promise.resolve(this)}get width(){return this._width}get height(){return this._height}style(t,e,s){return!1}dispose(){}destroy(){this.destroyed||(this.destroyed=!0,this.dispose(),this.onError.removeAll(),this.onError=null,this.onResize.removeAll(),this.onResize=null,this.onUpdate.removeAll(),this.onUpdate=null)}static test(t,e){return!1}}class Ts extends Qe{constructor(t,e){var s;const{width:i,height:n}=e||{};if(!i||!n)throw new Error("BufferResource width or height invalid");super(i,n),this.data=t,this.unpackAlignment=(s=e.unpackAlignment)!=null?s:4}upload(t,e,s){const i=t.gl;i.pixelStorei(i.UNPACK_ALIGNMENT,this.unpackAlignment),i.pixelStorei(i.UNPACK_PREMULTIPLY_ALPHA_WEBGL,e.alphaMode===wt.UNPACK);const n=e.realWidth,a=e.realHeight;return s.width===n&&s.height===a?i.texSubImage2D(e.target,0,0,0,n,a,e.format,s.type,this.data):(s.width=n,s.height=a,i.texImage2D(e.target,0,s.internalFormat,n,a,0,e.format,s.type,this.data)),!0}dispose(){this.data=null}static test(t){return t===null||t instanceof Int8Array||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array}}var dd=Object.defineProperty,Uo=Object.getOwnPropertySymbols,fd=Object.prototype.hasOwnProperty,pd=Object.prototype.propertyIsEnumerable,ko=(r,t,e)=>t in r?dd(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,md=(r,t)=>{for(var e in t||(t={}))fd.call(t,e)&&ko(r,e,t[e]);if(Uo)for(var e of Uo(t))pd.call(t,e)&&ko(r,e,t[e]);return r};const gd={scaleMode:Bt.NEAREST,alphaMode:wt.NPM},rn=class os extends Ye{constructor(t=null,e=null){super(),e=Object.assign({},os.defaultOptions,e);const{alphaMode:s,mipmap:i,anisotropicLevel:n,scaleMode:a,width:o,height:h,wrapMode:l,format:u,type:c,target:d,resolution:f,resourceOptions:p}=e;t&&!(t instanceof Qe)&&(t=sn(t,p),t.internal=!0),this.resolution=f||N.RESOLUTION,this.width=Math.round((o||0)*this.resolution)/this.resolution,this.height=Math.round((h||0)*this.resolution)/this.resolution,this._mipmap=i,this.anisotropicLevel=n,this._wrapMode=l,this._scaleMode=a,this.format=u,this.type=c,this.target=d,this.alphaMode=s,this.uid=Te(),this.touched=0,this.isPowerOfTwo=!1,this._refreshPOT(),this._glTextures={},this.dirtyId=0,this.dirtyStyleId=0,this.cacheId=null,this.valid=o>0&&h>0,this.textureCacheIds=[],this.destroyed=!1,this.resource=null,this._batchEnabled=0,this._batchLocation=0,this.parentTextureArray=null,this.setResource(t)}get realWidth(){return Math.round(this.width*this.resolution)}get realHeight(){return Math.round(this.height*this.resolution)}get mipmap(){return this._mipmap}set mipmap(t){this._mipmap!==t&&(this._mipmap=t,this.dirtyStyleId++)}get scaleMode(){return this._scaleMode}set scaleMode(t){this._scaleMode!==t&&(this._scaleMode=t,this.dirtyStyleId++)}get wrapMode(){return this._wrapMode}set wrapMode(t){this._wrapMode!==t&&(this._wrapMode=t,this.dirtyStyleId++)}setStyle(t,e){let s;return t!==void 0&&t!==this.scaleMode&&(this.scaleMode=t,s=!0),e!==void 0&&e!==this.mipmap&&(this.mipmap=e,s=!0),s&&this.dirtyStyleId++,this}setSize(t,e,s){return s=s||this.resolution,this.setRealSize(t*s,e*s,s)}setRealSize(t,e,s){return this.resolution=s||this.resolution,this.width=Math.round(t)/this.resolution,this.height=Math.round(e)/this.resolution,this._refreshPOT(),this.update(),this}_refreshPOT(){this.isPowerOfTwo=ji(this.realWidth)&&ji(this.realHeight)}setResolution(t){const e=this.resolution;return e===t?this:(this.resolution=t,this.valid&&(this.width=Math.round(this.width*e)/t,this.height=Math.round(this.height*e)/t,this.emit("update",this)),this._refreshPOT(),this)}setResource(t){if(this.resource===t)return this;if(this.resource)throw new Error("Resource can be set only once");return t.bind(this),this.resource=t,this}update(){this.valid?(this.dirtyId++,this.dirtyStyleId++,this.emit("update",this)):this.width>0&&this.height>0&&(this.valid=!0,this.emit("loaded",this),this.emit("update",this))}onError(t){this.emit("error",this,t)}destroy(){this.resource&&(this.resource.unbind(this),this.resource.internal&&this.resource.destroy(),this.resource=null),this.cacheId&&(delete It[this.cacheId],delete St[this.cacheId],this.cacheId=null),this.valid=!1,this.dispose(),os.removeFromCache(this),this.textureCacheIds=null,this.destroyed=!0,this.emit("destroyed",this),this.removeAllListeners()}dispose(){this.emit("dispose",this)}castToBaseTexture(){return this}static from(t,e,s=N.STRICT_TEXTURE_CACHE){const i=typeof t=="string";let n=null;if(i)n=t;else{if(!t._pixiId){const o=(e==null?void 0:e.pixiIdPrefix)||"pixiid";t._pixiId=`${o}_${Te()}`}n=t._pixiId}let a=It[n];if(i&&s&&!a)throw new Error(`The cacheId "${n}" does not exist in BaseTextureCache.`);return a||(a=new os(t,e),a.cacheId=n,os.addToCache(a,n)),a}static fromBuffer(t,e,s,i){t=t||new Float32Array(e*s*4);const n=new Ts(t,md({width:e,height:s},i==null?void 0:i.resourceOptions));let a,o;return t instanceof Float32Array?(a=P.RGBA,o=$.FLOAT):t instanceof Int32Array?(a=P.RGBA_INTEGER,o=$.INT):t instanceof Uint32Array?(a=P.RGBA_INTEGER,o=$.UNSIGNED_INT):t instanceof Int16Array?(a=P.RGBA_INTEGER,o=$.SHORT):t instanceof Uint16Array?(a=P.RGBA_INTEGER,o=$.UNSIGNED_SHORT):t instanceof Int8Array?(a=P.RGBA,o=$.BYTE):(a=P.RGBA,o=$.UNSIGNED_BYTE),n.internal=!0,new os(n,Object.assign({},gd,{type:o,format:a},i))}static addToCache(t,e){e&&(t.textureCacheIds.includes(e)||t.textureCacheIds.push(e),It[e]&&It[e]!==t&&console.warn(`BaseTexture added to the cache with an id [${e}] that already had an entry`),It[e]=t)}static removeFromCache(t){if(typeof t=="string"){const e=It[t];if(e){const s=e.textureCacheIds.indexOf(t);return s>-1&&e.textureCacheIds.splice(s,1),delete It[t],e}}else if(t!=null&&t.textureCacheIds){for(let e=0;e<t.textureCacheIds.length;++e)delete It[t.textureCacheIds[e]];return t.textureCacheIds.length=0,t}return null}};rn.defaultOptions={mipmap:Ht.POW2,anisotropicLevel:0,scaleMode:Bt.LINEAR,wrapMode:Zt.CLAMP,alphaMode:wt.UNPACK,target:Me.TEXTURE_2D,format:P.RGBA,type:$.UNSIGNED_BYTE},rn._globalBatch=0;let X=rn;class Sr{constructor(){this.texArray=null,this.blend=0,this.type=Ot.TRIANGLES,this.start=0,this.size=0,this.data=null}}let _d=0;class dt{constructor(t,e=!0,s=!1){this.data=t||new Float32Array(1),this._glBuffers={},this._updateID=0,this.index=s,this.static=e,this.id=_d++,this.disposeRunner=new Pt("disposeBuffer")}update(t){t instanceof Array&&(t=new Float32Array(t)),this.data=t||this.data,this._updateID++}dispose(){this.disposeRunner.emit(this,!1)}destroy(){this.dispose(),this.data=null}set index(t){this.type=t?jt.ELEMENT_ARRAY_BUFFER:jt.ARRAY_BUFFER}get index(){return this.type===jt.ELEMENT_ARRAY_BUFFER}static from(t){return t instanceof Array&&(t=new Float32Array(t)),new dt(t)}}class Es{constructor(t,e=0,s=!1,i=$.FLOAT,n,a,o,h=1){this.buffer=t,this.size=e,this.normalized=s,this.type=i,this.stride=n,this.start=a,this.instance=o,this.divisor=h}destroy(){this.buffer=null}static from(t,e,s,i,n){return new Es(t,e,s,i,n)}}const vd={Float32Array,Uint32Array,Int32Array,Uint8Array};function yd(r,t){let e=0,s=0;const i={};for(let h=0;h<r.length;h++)s+=t[h],e+=r[h].length;const n=new ArrayBuffer(e*4);let a=null,o=0;for(let h=0;h<r.length;h++){const l=t[h],u=r[h],c=Er(u);i[c]||(i[c]=new vd[c](n)),a=i[c];for(let d=0;d<u.length;d++){const f=(d/l|0)*s+o,p=d%l;a[f+p]=u[d]}o+=l}return new Float32Array(n)}const Go={5126:4,5123:2,5121:1};let xd=0;const bd={Float32Array,Uint32Array,Int32Array,Uint8Array,Uint16Array};class fe{constructor(t=[],e={}){this.buffers=t,this.indexBuffer=null,this.attributes=e,this.glVertexArrayObjects={},this.id=xd++,this.instanced=!1,this.instanceCount=1,this.disposeRunner=new Pt("disposeGeometry"),this.refCount=0}addAttribute(t,e,s=0,i=!1,n,a,o,h=!1){if(!e)throw new Error("You must pass a buffer when creating an attribute");e instanceof dt||(e instanceof Array&&(e=new Float32Array(e)),e=new dt(e));const l=t.split("|");if(l.length>1){for(let c=0;c<l.length;c++)this.addAttribute(l[c],e,s,i,n);return this}let u=this.buffers.indexOf(e);return u===-1&&(this.buffers.push(e),u=this.buffers.length-1),this.attributes[t]=new Es(u,s,i,n,a,o,h),this.instanced=this.instanced||h,this}getAttribute(t){return this.attributes[t]}getBuffer(t){return this.buffers[this.getAttribute(t).buffer]}addIndex(t){return t instanceof dt||(t instanceof Array&&(t=new Uint16Array(t)),t=new dt(t)),t.type=jt.ELEMENT_ARRAY_BUFFER,this.indexBuffer=t,this.buffers.includes(t)||this.buffers.push(t),this}getIndex(){return this.indexBuffer}interleave(){if(this.buffers.length===1||this.buffers.length===2&&this.indexBuffer)return this;const t=[],e=[],s=new dt;let i;for(i in this.attributes){const n=this.attributes[i],a=this.buffers[n.buffer];t.push(a.data),e.push(n.size*Go[n.type]/4),n.buffer=0}for(s.data=yd(t,e),i=0;i<this.buffers.length;i++)this.buffers[i]!==this.indexBuffer&&this.buffers[i].destroy();return this.buffers=[s],this.indexBuffer&&this.buffers.push(this.indexBuffer),this}getSize(){for(const t in this.attributes){const e=this.attributes[t];return this.buffers[e.buffer].data.length/(e.stride/4||e.size)}return 0}dispose(){this.disposeRunner.emit(this,!1)}destroy(){this.dispose(),this.buffers=null,this.indexBuffer=null,this.attributes=null}clone(){const t=new fe;for(let e=0;e<this.buffers.length;e++)t.buffers[e]=new dt(this.buffers[e].data.slice(0));for(const e in this.attributes){const s=this.attributes[e];t.attributes[e]=new Es(s.buffer,s.size,s.normalized,s.type,s.stride,s.start,s.instance)}return this.indexBuffer&&(t.indexBuffer=t.buffers[this.buffers.indexOf(this.indexBuffer)],t.indexBuffer.type=jt.ELEMENT_ARRAY_BUFFER),t}static merge(t){const e=new fe,s=[],i=[],n=[];let a;for(let o=0;o<t.length;o++){a=t[o];for(let h=0;h<a.buffers.length;h++)i[h]=i[h]||0,i[h]+=a.buffers[h].data.length,n[h]=0}for(let o=0;o<a.buffers.length;o++)s[o]=new bd[Er(a.buffers[o].data)](i[o]),e.buffers[o]=new dt(s[o]);for(let o=0;o<t.length;o++){a=t[o];for(let h=0;h<a.buffers.length;h++)s[h].set(a.buffers[h].data,n[h]),n[h]+=a.buffers[h].data.length}if(e.attributes=a.attributes,a.indexBuffer){e.indexBuffer=e.buffers[a.buffers.indexOf(a.indexBuffer)],e.indexBuffer.type=jt.ELEMENT_ARRAY_BUFFER;let o=0,h=0,l=0,u=0;for(let c=0;c<a.buffers.length;c++)if(a.buffers[c]!==a.indexBuffer){u=c;break}for(const c in a.attributes){const d=a.attributes[c];(d.buffer|0)===u&&(h+=d.size*Go[d.type]/4)}for(let c=0;c<t.length;c++){const d=t[c].indexBuffer.data;for(let f=0;f<d.length;f++)e.indexBuffer.data[f+l]+=o;o+=t[c].buffers[u].data.length/h,l+=d.length}}return e}}class nn extends fe{constructor(t=!1){super(),this._buffer=new dt(null,t,!1),this._indexBuffer=new dt(null,t,!0),this.addAttribute("aVertexPosition",this._buffer,2,!1,$.FLOAT).addAttribute("aTextureCoord",this._buffer,2,!1,$.FLOAT).addAttribute("aColor",this._buffer,4,!0,$.UNSIGNED_BYTE).addAttribute("aTextureId",this._buffer,1,!0,$.FLOAT).addIndex(this._indexBuffer)}}const As=Math.PI*2,$o=180/Math.PI,Ho=Math.PI/180;var rt=(r=>(r[r.POLY=0]="POLY",r[r.RECT=1]="RECT",r[r.CIRC=2]="CIRC",r[r.ELIP=3]="ELIP",r[r.RREC=4]="RREC",r))(rt||{});class K{constructor(t=0,e=0){this.x=0,this.y=0,this.x=t,this.y=e}clone(){return new K(this.x,this.y)}copyFrom(t){return this.set(t.x,t.y),this}copyTo(t){return t.set(this.x,this.y),t}equals(t){return t.x===this.x&&t.y===this.y}set(t=0,e=t){return this.x=t,this.y=e,this}}const Cr=[new K,new K,new K,new K];class z{constructor(t=0,e=0,s=0,i=0){this.x=Number(t),this.y=Number(e),this.width=Number(s),this.height=Number(i),this.type=rt.RECT}get left(){return this.x}get right(){return this.x+this.width}get top(){return this.y}get bottom(){return this.y+this.height}static get EMPTY(){return new z(0,0,0,0)}clone(){return new z(this.x,this.y,this.width,this.height)}copyFrom(t){return this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height,this}copyTo(t){return t.x=this.x,t.y=this.y,t.width=this.width,t.height=this.height,t}contains(t,e){return this.width<=0||this.height<=0?!1:t>=this.x&&t<this.x+this.width&&e>=this.y&&e<this.y+this.height}intersects(t,e){if(!e){const w=this.x<t.x?t.x:this.x;if((this.right>t.right?t.right:this.right)<=w)return!1;const R=this.y<t.y?t.y:this.y;return(this.bottom>t.bottom?t.bottom:this.bottom)>R}const s=this.left,i=this.right,n=this.top,a=this.bottom;if(i<=s||a<=n)return!1;const o=Cr[0].set(t.left,t.top),h=Cr[1].set(t.left,t.bottom),l=Cr[2].set(t.right,t.top),u=Cr[3].set(t.right,t.bottom);if(l.x<=o.x||h.y<=o.y)return!1;const c=Math.sign(e.a*e.d-e.b*e.c);if(c===0||(e.apply(o,o),e.apply(h,h),e.apply(l,l),e.apply(u,u),Math.max(o.x,h.x,l.x,u.x)<=s||Math.min(o.x,h.x,l.x,u.x)>=i||Math.max(o.y,h.y,l.y,u.y)<=n||Math.min(o.y,h.y,l.y,u.y)>=a))return!1;const d=c*(h.y-o.y),f=c*(o.x-h.x),p=d*s+f*n,m=d*i+f*n,g=d*s+f*a,_=d*i+f*a;if(Math.max(p,m,g,_)<=d*o.x+f*o.y||Math.min(p,m,g,_)>=d*u.x+f*u.y)return!1;const x=c*(o.y-l.y),y=c*(l.x-o.x),b=x*s+y*n,T=x*i+y*n,S=x*s+y*a,A=x*i+y*a;return!(Math.max(b,T,S,A)<=x*o.x+y*o.y||Math.min(b,T,S,A)>=x*u.x+y*u.y)}pad(t=0,e=t){return this.x-=t,this.y-=e,this.width+=t*2,this.height+=e*2,this}fit(t){const e=Math.max(this.x,t.x),s=Math.min(this.x+this.width,t.x+t.width),i=Math.max(this.y,t.y),n=Math.min(this.y+this.height,t.y+t.height);return this.x=e,this.width=Math.max(s-e,0),this.y=i,this.height=Math.max(n-i,0),this}ceil(t=1,e=.001){const s=Math.ceil((this.x+this.width-e)*t)/t,i=Math.ceil((this.y+this.height-e)*t)/t;return this.x=Math.floor((this.x+e)*t)/t,this.y=Math.floor((this.y+e)*t)/t,this.width=s-this.x,this.height=i-this.y,this}enlarge(t){const e=Math.min(this.x,t.x),s=Math.max(this.x+this.width,t.x+t.width),i=Math.min(this.y,t.y),n=Math.max(this.y+this.height,t.y+t.height);return this.x=e,this.width=s-e,this.y=i,this.height=n-i,this}}class Rr{constructor(t=0,e=0,s=0){this.x=t,this.y=e,this.radius=s,this.type=rt.CIRC}clone(){return new Rr(this.x,this.y,this.radius)}contains(t,e){if(this.radius<=0)return!1;const s=this.radius*this.radius;let i=this.x-t,n=this.y-e;return i*=i,n*=n,i+n<=s}getBounds(){return new z(this.x-this.radius,this.y-this.radius,this.radius*2,this.radius*2)}}class Ir{constructor(t=0,e=0,s=0,i=0){this.x=t,this.y=e,this.width=s,this.height=i,this.type=rt.ELIP}clone(){return new Ir(this.x,this.y,this.width,this.height)}contains(t,e){if(this.width<=0||this.height<=0)return!1;let s=(t-this.x)/this.width,i=(e-this.y)/this.height;return s*=s,i*=i,s+i<=1}getBounds(){return new z(this.x-this.width,this.y-this.height,this.width,this.height)}}class Be{constructor(...t){let e=Array.isArray(t[0])?t[0]:t;if(typeof e[0]!="number"){const s=[];for(let i=0,n=e.length;i<n;i++)s.push(e[i].x,e[i].y);e=s}this.points=e,this.type=rt.POLY,this.closeStroke=!0}clone(){const t=this.points.slice(),e=new Be(t);return e.closeStroke=this.closeStroke,e}contains(t,e){let s=!1;const i=this.points.length/2;for(let n=0,a=i-1;n<i;a=n++){const o=this.points[n*2],h=this.points[n*2+1],l=this.points[a*2],u=this.points[a*2+1];h>e!=u>e&&t<(l-o)*((e-h)/(u-h))+o&&(s=!s)}return s}}class Pr{constructor(t=0,e=0,s=0,i=0,n=20){this.x=t,this.y=e,this.width=s,this.height=i,this.radius=n,this.type=rt.RREC}clone(){return new Pr(this.x,this.y,this.width,this.height,this.radius)}contains(t,e){if(this.width<=0||this.height<=0)return!1;if(t>=this.x&&t<=this.x+this.width&&e>=this.y&&e<=this.y+this.height){const s=Math.max(0,Math.min(this.radius,Math.min(this.width,this.height)/2));if(e>=this.y+s&&e<=this.y+this.height-s||t>=this.x+s&&t<=this.x+this.width-s)return!0;let i=t-(this.x+s),n=e-(this.y+s);const a=s*s;if(i*i+n*n<=a||(i=t-(this.x+this.width-s),i*i+n*n<=a)||(n=e-(this.y+this.height-s),i*i+n*n<=a)||(i=t-(this.x+s),i*i+n*n<=a))return!0}return!1}}class tt{constructor(t=1,e=0,s=0,i=1,n=0,a=0){this.array=null,this.a=t,this.b=e,this.c=s,this.d=i,this.tx=n,this.ty=a}fromArray(t){this.a=t[0],this.b=t[1],this.c=t[3],this.d=t[4],this.tx=t[2],this.ty=t[5]}set(t,e,s,i,n,a){return this.a=t,this.b=e,this.c=s,this.d=i,this.tx=n,this.ty=a,this}toArray(t,e){this.array||(this.array=new Float32Array(9));const s=e||this.array;return t?(s[0]=this.a,s[1]=this.b,s[2]=0,s[3]=this.c,s[4]=this.d,s[5]=0,s[6]=this.tx,s[7]=this.ty,s[8]=1):(s[0]=this.a,s[1]=this.c,s[2]=this.tx,s[3]=this.b,s[4]=this.d,s[5]=this.ty,s[6]=0,s[7]=0,s[8]=1),s}apply(t,e){e=e||new K;const s=t.x,i=t.y;return e.x=this.a*s+this.c*i+this.tx,e.y=this.b*s+this.d*i+this.ty,e}applyInverse(t,e){e=e||new K;const s=1/(this.a*this.d+this.c*-this.b),i=t.x,n=t.y;return e.x=this.d*s*i+-this.c*s*n+(this.ty*this.c-this.tx*this.d)*s,e.y=this.a*s*n+-this.b*s*i+(-this.ty*this.a+this.tx*this.b)*s,e}translate(t,e){return this.tx+=t,this.ty+=e,this}scale(t,e){return this.a*=t,this.d*=e,this.c*=t,this.b*=e,this.tx*=t,this.ty*=e,this}rotate(t){const e=Math.cos(t),s=Math.sin(t),i=this.a,n=this.c,a=this.tx;return this.a=i*e-this.b*s,this.b=i*s+this.b*e,this.c=n*e-this.d*s,this.d=n*s+this.d*e,this.tx=a*e-this.ty*s,this.ty=a*s+this.ty*e,this}append(t){const e=this.a,s=this.b,i=this.c,n=this.d;return this.a=t.a*e+t.b*i,this.b=t.a*s+t.b*n,this.c=t.c*e+t.d*i,this.d=t.c*s+t.d*n,this.tx=t.tx*e+t.ty*i+this.tx,this.ty=t.tx*s+t.ty*n+this.ty,this}setTransform(t,e,s,i,n,a,o,h,l){return this.a=Math.cos(o+l)*n,this.b=Math.sin(o+l)*n,this.c=-Math.sin(o-h)*a,this.d=Math.cos(o-h)*a,this.tx=t-(s*this.a+i*this.c),this.ty=e-(s*this.b+i*this.d),this}prepend(t){const e=this.tx;if(t.a!==1||t.b!==0||t.c!==0||t.d!==1){const s=this.a,i=this.c;this.a=s*t.a+this.b*t.c,this.b=s*t.b+this.b*t.d,this.c=i*t.a+this.d*t.c,this.d=i*t.b+this.d*t.d}return this.tx=e*t.a+this.ty*t.c+t.tx,this.ty=e*t.b+this.ty*t.d+t.ty,this}decompose(t){const e=this.a,s=this.b,i=this.c,n=this.d,a=t.pivot,o=-Math.atan2(-i,n),h=Math.atan2(s,e),l=Math.abs(o+h);return l<1e-5||Math.abs(As-l)<1e-5?(t.rotation=h,t.skew.x=t.skew.y=0):(t.rotation=0,t.skew.x=o,t.skew.y=h),t.scale.x=Math.sqrt(e*e+s*s),t.scale.y=Math.sqrt(i*i+n*n),t.position.x=this.tx+(a.x*e+a.y*i),t.position.y=this.ty+(a.x*s+a.y*n),t}invert(){const t=this.a,e=this.b,s=this.c,i=this.d,n=this.tx,a=t*i-e*s;return this.a=i/a,this.b=-e/a,this.c=-s/a,this.d=t/a,this.tx=(s*this.ty-i*n)/a,this.ty=-(t*this.ty-e*n)/a,this}identity(){return this.a=1,this.b=0,this.c=0,this.d=1,this.tx=0,this.ty=0,this}clone(){const t=new tt;return t.a=this.a,t.b=this.b,t.c=this.c,t.d=this.d,t.tx=this.tx,t.ty=this.ty,t}copyTo(t){return t.a=this.a,t.b=this.b,t.c=this.c,t.d=this.d,t.tx=this.tx,t.ty=this.ty,t}copyFrom(t){return this.a=t.a,this.b=t.b,this.c=t.c,this.d=t.d,this.tx=t.tx,this.ty=t.ty,this}static get IDENTITY(){return new tt}static get TEMP_MATRIX(){return new tt}}const Fe=[1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1,0,1],Ne=[0,1,1,1,0,-1,-1,-1,0,1,1,1,0,-1,-1,-1],Le=[0,-1,-1,-1,0,1,1,1,0,1,1,1,0,-1,-1,-1],Ue=[1,1,0,-1,-1,-1,0,1,-1,-1,0,1,1,1,0,-1],an=[],Vo=[],Mr=Math.sign;function Td(){for(let r=0;r<16;r++){const t=[];an.push(t);for(let e=0;e<16;e++){const s=Mr(Fe[r]*Fe[e]+Le[r]*Ne[e]),i=Mr(Ne[r]*Fe[e]+Ue[r]*Ne[e]),n=Mr(Fe[r]*Le[e]+Le[r]*Ue[e]),a=Mr(Ne[r]*Le[e]+Ue[r]*Ue[e]);for(let o=0;o<16;o++)if(Fe[o]===s&&Ne[o]===i&&Le[o]===n&&Ue[o]===a){t.push(o);break}}}for(let r=0;r<16;r++){const t=new tt;t.set(Fe[r],Ne[r],Le[r],Ue[r],0,0),Vo.push(t)}}Td();const at={E:0,SE:1,S:2,SW:3,W:4,NW:5,N:6,NE:7,MIRROR_VERTICAL:8,MAIN_DIAGONAL:10,MIRROR_HORIZONTAL:12,REVERSE_DIAGONAL:14,uX:r=>Fe[r],uY:r=>Ne[r],vX:r=>Le[r],vY:r=>Ue[r],inv:r=>r&8?r&15:-r&7,add:(r,t)=>an[r][t],sub:(r,t)=>an[r][at.inv(t)],rotate180:r=>r^4,isVertical:r=>(r&3)===2,byDirection:(r,t)=>Math.abs(r)*2<=Math.abs(t)?t>=0?at.S:at.N:Math.abs(t)*2<=Math.abs(r)?r>0?at.E:at.W:t>0?r>0?at.SE:at.SW:r>0?at.NE:at.NW,matrixAppendRotationInv:(r,t,e=0,s=0)=>{const i=Vo[at.inv(t)];i.tx=e,i.ty=s,r.append(i)}};class pe{constructor(t,e,s=0,i=0){this._x=s,this._y=i,this.cb=t,this.scope=e}clone(t=this.cb,e=this.scope){return new pe(t,e,this._x,this._y)}set(t=0,e=t){return(this._x!==t||this._y!==e)&&(this._x=t,this._y=e,this.cb.call(this.scope)),this}copyFrom(t){return(this._x!==t.x||this._y!==t.y)&&(this._x=t.x,this._y=t.y,this.cb.call(this.scope)),this}copyTo(t){return t.set(this._x,this._y),t}equals(t){return t.x===this._x&&t.y===this._y}get x(){return this._x}set x(t){this._x!==t&&(this._x=t,this.cb.call(this.scope))}get y(){return this._y}set y(t){this._y!==t&&(this._y=t,this.cb.call(this.scope))}}const on=class{constructor(){this.worldTransform=new tt,this.localTransform=new tt,this.position=new pe(this.onChange,this,0,0),this.scale=new pe(this.onChange,this,1,1),this.pivot=new pe(this.onChange,this,0,0),this.skew=new pe(this.updateSkew,this,0,0),this._rotation=0,this._cx=1,this._sx=0,this._cy=0,this._sy=1,this._localID=0,this._currentLocalID=0,this._worldID=0,this._parentID=0}onChange(){this._localID++}updateSkew(){this._cx=Math.cos(this._rotation+this.skew.y),this._sx=Math.sin(this._rotation+this.skew.y),this._cy=-Math.sin(this._rotation-this.skew.x),this._sy=Math.cos(this._rotation-this.skew.x),this._localID++}updateLocalTransform(){const t=this.localTransform;this._localID!==this._currentLocalID&&(t.a=this._cx*this.scale.x,t.b=this._sx*this.scale.x,t.c=this._cy*this.scale.y,t.d=this._sy*this.scale.y,t.tx=this.position.x-(this.pivot.x*t.a+this.pivot.y*t.c),t.ty=this.position.y-(this.pivot.x*t.b+this.pivot.y*t.d),this._currentLocalID=this._localID,this._parentID=-1)}updateTransform(t){const e=this.localTransform;if(this._localID!==this._currentLocalID&&(e.a=this._cx*this.scale.x,e.b=this._sx*this.scale.x,e.c=this._cy*this.scale.y,e.d=this._sy*this.scale.y,e.tx=this.position.x-(this.pivot.x*e.a+this.pivot.y*e.c),e.ty=this.position.y-(this.pivot.x*e.b+this.pivot.y*e.d),this._currentLocalID=this._localID,this._parentID=-1),this._parentID!==t._worldID){const s=t.worldTransform,i=this.worldTransform;i.a=e.a*s.a+e.b*s.c,i.b=e.a*s.b+e.b*s.d,i.c=e.c*s.a+e.d*s.c,i.d=e.c*s.b+e.d*s.d,i.tx=e.tx*s.a+e.ty*s.c+s.tx,i.ty=e.tx*s.b+e.ty*s.d+s.ty,this._parentID=t._worldID,this._worldID++}}setFromMatrix(t){t.decompose(this),this._localID++}get rotation(){return this._rotation}set rotation(t){this._rotation!==t&&(this._rotation=t,this.updateSkew())}};on.IDENTITY=new on;let Dr=on;var Ed=`varying vec2 vTextureCoord;

uniform sampler2D uSampler;

void main(void){
   gl_FragColor *= texture2D(uSampler, vTextureCoord);
}`,Ad=`attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;

uniform mat3 projectionMatrix;

varying vec2 vTextureCoord;

void main(void){
   gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
   vTextureCoord = aTextureCoord;
}
`;function jo(r,t,e){const s=r.createShader(t);return r.shaderSource(s,e),r.compileShader(s),s}function hn(r){const t=new Array(r);for(let e=0;e<t.length;e++)t[e]=!1;return t}function Xo(r,t){switch(r){case"float":return 0;case"vec2":return new Float32Array(2*t);case"vec3":return new Float32Array(3*t);case"vec4":return new Float32Array(4*t);case"int":case"uint":case"sampler2D":case"sampler2DArray":return 0;case"ivec2":return new Int32Array(2*t);case"ivec3":return new Int32Array(3*t);case"ivec4":return new Int32Array(4*t);case"uvec2":return new Uint32Array(2*t);case"uvec3":return new Uint32Array(3*t);case"uvec4":return new Uint32Array(4*t);case"bool":return!1;case"bvec2":return hn(2*t);case"bvec3":return hn(3*t);case"bvec4":return hn(4*t);case"mat2":return new Float32Array([1,0,0,1]);case"mat3":return new Float32Array([1,0,0,0,1,0,0,0,1]);case"mat4":return new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1])}return null}const ke=[{test:r=>r.type==="float"&&r.size===1&&!r.isArray,code:r=>`
            if(uv["${r}"] !== ud["${r}"].value)
            {
                ud["${r}"].value = uv["${r}"]
                gl.uniform1f(ud["${r}"].location, uv["${r}"])
            }
            `},{test:(r,t)=>(r.type==="sampler2D"||r.type==="samplerCube"||r.type==="sampler2DArray")&&r.size===1&&!r.isArray&&(t==null||t.castToBaseTexture!==void 0),code:r=>`t = syncData.textureCount++;

            renderer.texture.bind(uv["${r}"], t);

            if(ud["${r}"].value !== t)
            {
                ud["${r}"].value = t;
                gl.uniform1i(ud["${r}"].location, t);
; // eslint-disable-line max-len
            }`},{test:(r,t)=>r.type==="mat3"&&r.size===1&&!r.isArray&&t.a!==void 0,code:r=>`
            gl.uniformMatrix3fv(ud["${r}"].location, false, uv["${r}"].toArray(true));
            `,codeUbo:r=>`
                var ${r}_matrix = uv.${r}.toArray(true);

                data[offset] = ${r}_matrix[0];
                data[offset+1] = ${r}_matrix[1];
                data[offset+2] = ${r}_matrix[2];
        
                data[offset + 4] = ${r}_matrix[3];
                data[offset + 5] = ${r}_matrix[4];
                data[offset + 6] = ${r}_matrix[5];
        
                data[offset + 8] = ${r}_matrix[6];
                data[offset + 9] = ${r}_matrix[7];
                data[offset + 10] = ${r}_matrix[8];
            `},{test:(r,t)=>r.type==="vec2"&&r.size===1&&!r.isArray&&t.x!==void 0,code:r=>`
                cv = ud["${r}"].value;
                v = uv["${r}"];

                if(cv[0] !== v.x || cv[1] !== v.y)
                {
                    cv[0] = v.x;
                    cv[1] = v.y;
                    gl.uniform2f(ud["${r}"].location, v.x, v.y);
                }`,codeUbo:r=>`
                v = uv.${r};

                data[offset] = v.x;
                data[offset+1] = v.y;
            `},{test:r=>r.type==="vec2"&&r.size===1&&!r.isArray,code:r=>`
                cv = ud["${r}"].value;
                v = uv["${r}"];

                if(cv[0] !== v[0] || cv[1] !== v[1])
                {
                    cv[0] = v[0];
                    cv[1] = v[1];
                    gl.uniform2f(ud["${r}"].location, v[0], v[1]);
                }
            `},{test:(r,t)=>r.type==="vec4"&&r.size===1&&!r.isArray&&t.width!==void 0,code:r=>`
                cv = ud["${r}"].value;
                v = uv["${r}"];

                if(cv[0] !== v.x || cv[1] !== v.y || cv[2] !== v.width || cv[3] !== v.height)
                {
                    cv[0] = v.x;
                    cv[1] = v.y;
                    cv[2] = v.width;
                    cv[3] = v.height;
                    gl.uniform4f(ud["${r}"].location, v.x, v.y, v.width, v.height)
                }`,codeUbo:r=>`
                    v = uv.${r};

                    data[offset] = v.x;
                    data[offset+1] = v.y;
                    data[offset+2] = v.width;
                    data[offset+3] = v.height;
                `},{test:(r,t)=>r.type==="vec4"&&r.size===1&&!r.isArray&&t.red!==void 0,code:r=>`
                cv = ud["${r}"].value;
                v = uv["${r}"];

                if(cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue || cv[3] !== v.alpha)
                {
                    cv[0] = v.red;
                    cv[1] = v.green;
                    cv[2] = v.blue;
                    cv[3] = v.alpha;
                    gl.uniform4f(ud["${r}"].location, v.red, v.green, v.blue, v.alpha)
                }`,codeUbo:r=>`
                    v = uv.${r};

                    data[offset] = v.red;
                    data[offset+1] = v.green;
                    data[offset+2] = v.blue;
                    data[offset+3] = v.alpha;
                `},{test:(r,t)=>r.type==="vec3"&&r.size===1&&!r.isArray&&t.red!==void 0,code:r=>`
                cv = ud["${r}"].value;
                v = uv["${r}"];

                if(cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue || cv[3] !== v.a)
                {
                    cv[0] = v.red;
                    cv[1] = v.green;
                    cv[2] = v.blue;
    
                    gl.uniform3f(ud["${r}"].location, v.red, v.green, v.blue)
                }`,codeUbo:r=>`
                    v = uv.${r};

                    data[offset] = v.red;
                    data[offset+1] = v.green;
                    data[offset+2] = v.blue;
                `},{test:r=>r.type==="vec4"&&r.size===1&&!r.isArray,code:r=>`
                cv = ud["${r}"].value;
                v = uv["${r}"];

                if(cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])
                {
                    cv[0] = v[0];
                    cv[1] = v[1];
                    cv[2] = v[2];
                    cv[3] = v[3];

                    gl.uniform4f(ud["${r}"].location, v[0], v[1], v[2], v[3])
                }`}],wd={float:`
    if (cv !== v)
    {
        cu.value = v;
        gl.uniform1f(location, v);
    }`,vec2:`
    if (cv[0] !== v[0] || cv[1] !== v[1])
    {
        cv[0] = v[0];
        cv[1] = v[1];

        gl.uniform2f(location, v[0], v[1])
    }`,vec3:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];

        gl.uniform3f(location, v[0], v[1], v[2])
    }`,vec4:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];
        cv[3] = v[3];

        gl.uniform4f(location, v[0], v[1], v[2], v[3]);
    }`,int:`
    if (cv !== v)
    {
        cu.value = v;

        gl.uniform1i(location, v);
    }`,ivec2:`
    if (cv[0] !== v[0] || cv[1] !== v[1])
    {
        cv[0] = v[0];
        cv[1] = v[1];

        gl.uniform2i(location, v[0], v[1]);
    }`,ivec3:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];

        gl.uniform3i(location, v[0], v[1], v[2]);
    }`,ivec4:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];
        cv[3] = v[3];

        gl.uniform4i(location, v[0], v[1], v[2], v[3]);
    }`,uint:`
    if (cv !== v)
    {
        cu.value = v;

        gl.uniform1ui(location, v);
    }`,uvec2:`
    if (cv[0] !== v[0] || cv[1] !== v[1])
    {
        cv[0] = v[0];
        cv[1] = v[1];

        gl.uniform2ui(location, v[0], v[1]);
    }`,uvec3:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];

        gl.uniform3ui(location, v[0], v[1], v[2]);
    }`,uvec4:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];
        cv[3] = v[3];

        gl.uniform4ui(location, v[0], v[1], v[2], v[3]);
    }`,bool:`
    if (cv !== v)
    {
        cu.value = v;
        gl.uniform1i(location, v);
    }`,bvec2:`
    if (cv[0] != v[0] || cv[1] != v[1])
    {
        cv[0] = v[0];
        cv[1] = v[1];

        gl.uniform2i(location, v[0], v[1]);
    }`,bvec3:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];

        gl.uniform3i(location, v[0], v[1], v[2]);
    }`,bvec4:`
    if (cv[0] !== v[0] || cv[1] !== v[1] || cv[2] !== v[2] || cv[3] !== v[3])
    {
        cv[0] = v[0];
        cv[1] = v[1];
        cv[2] = v[2];
        cv[3] = v[3];

        gl.uniform4i(location, v[0], v[1], v[2], v[3]);
    }`,mat2:"gl.uniformMatrix2fv(location, false, v)",mat3:"gl.uniformMatrix3fv(location, false, v)",mat4:"gl.uniformMatrix4fv(location, false, v)",sampler2D:`
    if (cv !== v)
    {
        cu.value = v;

        gl.uniform1i(location, v);
    }`,samplerCube:`
    if (cv !== v)
    {
        cu.value = v;

        gl.uniform1i(location, v);
    }`,sampler2DArray:`
    if (cv !== v)
    {
        cu.value = v;

        gl.uniform1i(location, v);
    }`},Sd={float:"gl.uniform1fv(location, v)",vec2:"gl.uniform2fv(location, v)",vec3:"gl.uniform3fv(location, v)",vec4:"gl.uniform4fv(location, v)",mat4:"gl.uniformMatrix4fv(location, false, v)",mat3:"gl.uniformMatrix3fv(location, false, v)",mat2:"gl.uniformMatrix2fv(location, false, v)",int:"gl.uniform1iv(location, v)",ivec2:"gl.uniform2iv(location, v)",ivec3:"gl.uniform3iv(location, v)",ivec4:"gl.uniform4iv(location, v)",uint:"gl.uniform1uiv(location, v)",uvec2:"gl.uniform2uiv(location, v)",uvec3:"gl.uniform3uiv(location, v)",uvec4:"gl.uniform4uiv(location, v)",bool:"gl.uniform1iv(location, v)",bvec2:"gl.uniform2iv(location, v)",bvec3:"gl.uniform3iv(location, v)",bvec4:"gl.uniform4iv(location, v)",sampler2D:"gl.uniform1iv(location, v)",samplerCube:"gl.uniform1iv(location, v)",sampler2DArray:"gl.uniform1iv(location, v)"};function Cd(r,t){var e;const s=[`
        var v = null;
        var cv = null;
        var cu = null;
        var t = 0;
        var gl = renderer.gl;
    `];for(const i in r.uniforms){const n=t[i];if(!n){((e=r.uniforms[i])==null?void 0:e.group)===!0&&(r.uniforms[i].ubo?s.push(`
                        renderer.shader.syncUniformBufferGroup(uv.${i}, '${i}');
                    `):s.push(`
                        renderer.shader.syncUniformGroup(uv.${i}, syncData);
                    `));continue}const a=r.uniforms[i];let o=!1;for(let h=0;h<ke.length;h++)if(ke[h].test(n,a)){s.push(ke[h].code(i,a)),o=!0;break}if(!o){const h=(n.size===1&&!n.isArray?wd:Sd)[n.type].replace("location",`ud["${i}"].location`);s.push(`
            cu = ud["${i}"];
            cv = cu.value;
            v = uv["${i}"];
            ${h};`)}}return new Function("ud","uv","renderer","syncData",s.join(`
`))}const zo={};let ws=zo;function Wo(){if(ws===zo||ws!=null&&ws.isContextLost()){const r=N.ADAPTER.createCanvas();let t;N.PREFER_ENV>=be.WEBGL2&&(t=r.getContext("webgl2",{})),t||(t=r.getContext("webgl",{})||r.getContext("experimental-webgl",{}),t?t.getExtension("WEBGL_draw_buffers"):t=null),ws=t}return ws}let Or;function Rd(){if(!Or){Or=Rt.MEDIUM;const r=Wo();if(r&&r.getShaderPrecisionFormat){const t=r.getShaderPrecisionFormat(r.FRAGMENT_SHADER,r.HIGH_FLOAT);t&&(Or=t.precision?Rt.HIGH:Rt.MEDIUM)}}return Or}function Yo(r,t){const e=r.getShaderSource(t).split(`
`).map((l,u)=>`${u}: ${l}`),s=r.getShaderInfoLog(t),i=s.split(`
`),n={},a=i.map(l=>parseFloat(l.replace(/^ERROR\: 0\:([\d]+)\:.*$/,"$1"))).filter(l=>l&&!n[l]?(n[l]=!0,!0):!1),o=[""];a.forEach(l=>{e[l-1]=`%c${e[l-1]}%c`,o.push("background: #FF0000; color:#FFFFFF; font-size: 10px","font-size: 10px")});const h=e.join(`
`);o[0]=h,console.error(s),console.groupCollapsed("click to view full shader code"),console.warn(...o),console.groupEnd()}function Id(r,t,e,s){r.getProgramParameter(t,r.LINK_STATUS)||(r.getShaderParameter(e,r.COMPILE_STATUS)||Yo(r,e),r.getShaderParameter(s,r.COMPILE_STATUS)||Yo(r,s),console.error("PixiJS Error: Could not initialize shader."),r.getProgramInfoLog(t)!==""&&console.warn("PixiJS Warning: gl.getProgramInfoLog()",r.getProgramInfoLog(t)))}const Pd={float:1,vec2:2,vec3:3,vec4:4,int:1,ivec2:2,ivec3:3,ivec4:4,uint:1,uvec2:2,uvec3:3,uvec4:4,bool:1,bvec2:2,bvec3:3,bvec4:4,mat2:4,mat3:9,mat4:16,sampler2D:1};function qo(r){return Pd[r]}let Br=null;const Ko={FLOAT:"float",FLOAT_VEC2:"vec2",FLOAT_VEC3:"vec3",FLOAT_VEC4:"vec4",INT:"int",INT_VEC2:"ivec2",INT_VEC3:"ivec3",INT_VEC4:"ivec4",UNSIGNED_INT:"uint",UNSIGNED_INT_VEC2:"uvec2",UNSIGNED_INT_VEC3:"uvec3",UNSIGNED_INT_VEC4:"uvec4",BOOL:"bool",BOOL_VEC2:"bvec2",BOOL_VEC3:"bvec3",BOOL_VEC4:"bvec4",FLOAT_MAT2:"mat2",FLOAT_MAT3:"mat3",FLOAT_MAT4:"mat4",SAMPLER_2D:"sampler2D",INT_SAMPLER_2D:"sampler2D",UNSIGNED_INT_SAMPLER_2D:"sampler2D",SAMPLER_CUBE:"samplerCube",INT_SAMPLER_CUBE:"samplerCube",UNSIGNED_INT_SAMPLER_CUBE:"samplerCube",SAMPLER_2D_ARRAY:"sampler2DArray",INT_SAMPLER_2D_ARRAY:"sampler2DArray",UNSIGNED_INT_SAMPLER_2D_ARRAY:"sampler2DArray"};function Zo(r,t){if(!Br){const e=Object.keys(Ko);Br={};for(let s=0;s<e.length;++s){const i=e[s];Br[r[i]]=Ko[i]}}return Br[t]}function Qo(r,t,e){if(r.substring(0,9)!=="precision"){let s=t;return t===Rt.HIGH&&e!==Rt.HIGH&&(s=Rt.MEDIUM),`precision ${s} float;
${r}`}else if(e!==Rt.HIGH&&r.substring(0,15)==="precision highp")return r.replace("precision highp","precision mediump");return r}let Ss;function Jo(){if(typeof Ss=="boolean")return Ss;try{Ss=new Function("param1","param2","param3","return param1[param2] === param3;")({a:"b"},"a","b")===!0}catch(r){Ss=!1}return Ss}let Md=0;const Fr={},ln=class hs{constructor(t,e,s="pixi-shader",i={}){this.extra={},this.id=Md++,this.vertexSrc=t||hs.defaultVertexSrc,this.fragmentSrc=e||hs.defaultFragmentSrc,this.vertexSrc=this.vertexSrc.trim(),this.fragmentSrc=this.fragmentSrc.trim(),this.extra=i,this.vertexSrc.substring(0,8)!=="#version"&&(s=s.replace(/\s+/g,"-"),Fr[s]?(Fr[s]++,s+=`-${Fr[s]}`):Fr[s]=1,this.vertexSrc=`#define SHADER_NAME ${s}
${this.vertexSrc}`,this.fragmentSrc=`#define SHADER_NAME ${s}
${this.fragmentSrc}`,this.vertexSrc=Qo(this.vertexSrc,hs.defaultVertexPrecision,Rt.HIGH),this.fragmentSrc=Qo(this.fragmentSrc,hs.defaultFragmentPrecision,Rd())),this.glPrograms={},this.syncUniforms=null}static get defaultVertexSrc(){return Ad}static get defaultFragmentSrc(){return Ed}static from(t,e,s){const i=t+e;let n=Yi[i];return n||(Yi[i]=n=new hs(t,e,s)),n}};ln.defaultVertexPrecision=Rt.HIGH,ln.defaultFragmentPrecision=Xt.apple.device?Rt.HIGH:Rt.MEDIUM;let se=ln,Dd=0;class Lt{constructor(t,e,s){this.group=!0,this.syncUniforms={},this.dirtyId=0,this.id=Dd++,this.static=!!e,this.ubo=!!s,t instanceof dt?(this.buffer=t,this.buffer.type=jt.UNIFORM_BUFFER,this.autoManage=!1,this.ubo=!0):(this.uniforms=t,this.ubo&&(this.buffer=new dt(new Float32Array(1)),this.buffer.type=jt.UNIFORM_BUFFER,this.autoManage=!0))}update(){this.dirtyId++,!this.autoManage&&this.buffer&&this.buffer.update()}add(t,e,s){if(!this.ubo)this.uniforms[t]=new Lt(e,s);else throw new Error("[UniformGroup] uniform groups in ubo mode cannot be modified, or have uniform groups nested in them")}static from(t,e,s){return new Lt(t,e,s)}static uboFrom(t,e){return new Lt(t,e!=null?e:!0,!0)}}class Wt{constructor(t,e){this.uniformBindCount=0,this.program=t,e?e instanceof Lt?this.uniformGroup=e:this.uniformGroup=new Lt(e):this.uniformGroup=new Lt({}),this.disposeRunner=new Pt("disposeShader")}checkUniformExists(t,e){if(e.uniforms[t])return!0;for(const s in e.uniforms){const i=e.uniforms[s];if(i.group===!0&&this.checkUniformExists(t,i))return!0}return!1}destroy(){this.uniformGroup=null,this.disposeRunner.emit(this),this.disposeRunner.destroy()}get uniforms(){return this.uniformGroup.uniforms}static from(t,e,s){const i=se.from(t,e);return new Wt(i,s)}}class th{constructor(t,e){if(this.vertexSrc=t,this.fragTemplate=e,this.programCache={},this.defaultGroupCache={},!e.includes("%count%"))throw new Error('Fragment template must contain "%count%".');if(!e.includes("%forloop%"))throw new Error('Fragment template must contain "%forloop%".')}generateShader(t){if(!this.programCache[t]){const s=new Int32Array(t);for(let n=0;n<t;n++)s[n]=n;this.defaultGroupCache[t]=Lt.from({uSamplers:s},!0);let i=this.fragTemplate;i=i.replace(/%count%/gi,`${t}`),i=i.replace(/%forloop%/gi,this.generateSampleSrc(t)),this.programCache[t]=new se(this.vertexSrc,i)}const e={tint:new Float32Array([1,1,1,1]),translationMatrix:new tt,default:this.defaultGroupCache[t]};return new Wt(this.programCache[t],e)}generateSampleSrc(t){let e="";e+=`
`,e+=`
`;for(let s=0;s<t;s++)s>0&&(e+=`
else `),s<t-1&&(e+=`if(vTextureId < ${s}.5)`),e+=`
{`,e+=`
	color = texture2D(uSamplers[${s}], vTextureCoord);`,e+=`
}`;return e+=`
`,e+=`
`,e}}class Nr{constructor(){this.elements=[],this.ids=[],this.count=0}clear(){for(let t=0;t<this.count;t++)this.elements[t]=null;this.count=0}}function Od(){return!Xt.apple.device}function Bd(r){let t=!0;const e=N.ADAPTER.getNavigator();if(Xt.tablet||Xt.phone){if(Xt.apple.device){const s=e.userAgent.match(/OS (\d+)_(\d+)?/);s&&parseInt(s[1],10)<11&&(t=!1)}if(Xt.android.device){const s=e.userAgent.match(/Android\s([0-9.]*)/);s&&parseInt(s[1],10)<7&&(t=!1)}}return t?r:4}class Cs{constructor(t){this.renderer=t}flush(){}destroy(){this.renderer=null}start(){}stop(){this.flush()}render(t){}}var Fd=`varying vec2 vTextureCoord;
varying vec4 vColor;
varying float vTextureId;
uniform sampler2D uSamplers[%count%];

void main(void){
    vec4 color;
    %forloop%
    gl_FragColor = color * vColor;
}
`,Nd=`precision highp float;
attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;
attribute vec4 aColor;
attribute float aTextureId;

uniform mat3 projectionMatrix;
uniform mat3 translationMatrix;
uniform vec4 tint;

varying vec2 vTextureCoord;
varying vec4 vColor;
varying float vTextureId;

void main(void){
    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = aTextureCoord;
    vTextureId = aTextureId;
    vColor = aColor * tint;
}
`;const Rs=class Kt extends Cs{constructor(t){super(t),this.setShaderGenerator(),this.geometryClass=nn,this.vertexSize=6,this.state=ee.for2d(),this.size=Kt.defaultBatchSize*4,this._vertexCount=0,this._indexCount=0,this._bufferedElements=[],this._bufferedTextures=[],this._bufferSize=0,this._shader=null,this._packedGeometries=[],this._packedGeometryPoolSize=2,this._flushId=0,this._aBuffers={},this._iBuffers={},this.maxTextures=1,this.renderer.on("prerender",this.onPrerender,this),t.runners.contextChange.add(this),this._dcIndex=0,this._aIndex=0,this._iIndex=0,this._attributeBuffer=null,this._indexBuffer=null,this._tempBoundTextures=[]}static get defaultMaxTextures(){var t;return this._defaultMaxTextures=(t=this._defaultMaxTextures)!=null?t:Bd(32),this._defaultMaxTextures}static set defaultMaxTextures(t){this._defaultMaxTextures=t}static get canUploadSameBuffer(){var t;return this._canUploadSameBuffer=(t=this._canUploadSameBuffer)!=null?t:Od(),this._canUploadSameBuffer}static set canUploadSameBuffer(t){this._canUploadSameBuffer=t}get MAX_TEXTURES(){return this.maxTextures}static get defaultVertexSrc(){return Nd}static get defaultFragmentTemplate(){return Fd}setShaderGenerator({vertex:t=Kt.defaultVertexSrc,fragment:e=Kt.defaultFragmentTemplate}={}){this.shaderGenerator=new th(t,e)}contextChange(){const t=this.renderer.gl;N.PREFER_ENV===be.WEBGL_LEGACY?this.maxTextures=1:(this.maxTextures=Math.min(t.getParameter(t.MAX_TEXTURE_IMAGE_UNITS),Kt.defaultMaxTextures),this.maxTextures=Lo(this.maxTextures,t)),this._shader=this.shaderGenerator.generateShader(this.maxTextures);for(let e=0;e<this._packedGeometryPoolSize;e++)this._packedGeometries[e]=new this.geometryClass;this.initFlushBuffers()}initFlushBuffers(){const{_drawCallPool:t,_textureArrayPool:e}=Kt,s=this.size/4,i=Math.floor(s/this.maxTextures)+1;for(;t.length<s;)t.push(new Sr);for(;e.length<i;)e.push(new Nr);for(let n=0;n<this.maxTextures;n++)this._tempBoundTextures[n]=null}onPrerender(){this._flushId=0}render(t){t._texture.valid&&(this._vertexCount+t.vertexData.length/2>this.size&&this.flush(),this._vertexCount+=t.vertexData.length/2,this._indexCount+=t.indices.length,this._bufferedTextures[this._bufferSize]=t._texture.baseTexture,this._bufferedElements[this._bufferSize++]=t)}buildTexturesAndDrawCalls(){const{_bufferedTextures:t,maxTextures:e}=this,s=Kt._textureArrayPool,i=this.renderer.batch,n=this._tempBoundTextures,a=this.renderer.textureGC.count;let o=++X._globalBatch,h=0,l=s[0],u=0;i.copyBoundTextures(n,e);for(let c=0;c<this._bufferSize;++c){const d=t[c];t[c]=null,d._batchEnabled!==o&&(l.count>=e&&(i.boundArray(l,n,o,e),this.buildDrawCalls(l,u,c),u=c,l=s[++h],++o),d._batchEnabled=o,d.touched=a,l.elements[l.count++]=d)}l.count>0&&(i.boundArray(l,n,o,e),this.buildDrawCalls(l,u,this._bufferSize),++h,++o);for(let c=0;c<n.length;c++)n[c]=null;X._globalBatch=o}buildDrawCalls(t,e,s){const{_bufferedElements:i,_attributeBuffer:n,_indexBuffer:a,vertexSize:o}=this,h=Kt._drawCallPool;let l=this._dcIndex,u=this._aIndex,c=this._iIndex,d=h[l];d.start=this._iIndex,d.texArray=t;for(let f=e;f<s;++f){const p=i[f],m=p._texture.baseTexture,g=Hi[m.alphaMode?1:0][p.blendMode];i[f]=null,e<f&&d.blend!==g&&(d.size=c-d.start,e=f,d=h[++l],d.texArray=t,d.start=c),this.packInterleavedGeometry(p,n,a,u,c),u+=p.vertexData.length/2*o,c+=p.indices.length,d.blend=g}e<s&&(d.size=c-d.start,++l),this._dcIndex=l,this._aIndex=u,this._iIndex=c}bindAndClearTexArray(t){const e=this.renderer.texture;for(let s=0;s<t.count;s++)e.bind(t.elements[s],t.ids[s]),t.elements[s]=null;t.count=0}updateGeometry(){const{_packedGeometries:t,_attributeBuffer:e,_indexBuffer:s}=this;Kt.canUploadSameBuffer?(t[this._flushId]._buffer.update(e.rawBinaryData),t[this._flushId]._indexBuffer.update(s),this.renderer.geometry.updateBuffers()):(this._packedGeometryPoolSize<=this._flushId&&(this._packedGeometryPoolSize++,t[this._flushId]=new this.geometryClass),t[this._flushId]._buffer.update(e.rawBinaryData),t[this._flushId]._indexBuffer.update(s),this.renderer.geometry.bind(t[this._flushId]),this.renderer.geometry.updateBuffers(),this._flushId++)}drawBatches(){const t=this._dcIndex,{gl:e,state:s}=this.renderer,i=Kt._drawCallPool;let n=null;for(let a=0;a<t;a++){const{texArray:o,type:h,size:l,start:u,blend:c}=i[a];n!==o&&(n=o,this.bindAndClearTexArray(o)),this.state.blendMode=c,s.set(this.state),e.drawElements(h,l,e.UNSIGNED_SHORT,u*2)}}flush(){this._vertexCount!==0&&(this._attributeBuffer=this.getAttributeBuffer(this._vertexCount),this._indexBuffer=this.getIndexBuffer(this._indexCount),this._aIndex=0,this._iIndex=0,this._dcIndex=0,this.buildTexturesAndDrawCalls(),this.updateGeometry(),this.drawBatches(),this._bufferSize=0,this._vertexCount=0,this._indexCount=0)}start(){this.renderer.state.set(this.state),this.renderer.texture.ensureSamplerType(this.maxTextures),this.renderer.shader.bind(this._shader),Kt.canUploadSameBuffer&&this.renderer.geometry.bind(this._packedGeometries[this._flushId])}stop(){this.flush()}destroy(){for(let t=0;t<this._packedGeometryPoolSize;t++)this._packedGeometries[t]&&this._packedGeometries[t].destroy();this.renderer.off("prerender",this.onPrerender,this),this._aBuffers=null,this._iBuffers=null,this._packedGeometries=null,this._attributeBuffer=null,this._indexBuffer=null,this._shader&&(this._shader.destroy(),this._shader=null),super.destroy()}getAttributeBuffer(t){const e=xs(Math.ceil(t/8)),s=Xi(e),i=e*8;this._aBuffers.length<=s&&(this._iBuffers.length=s+1);let n=this._aBuffers[i];return n||(this._aBuffers[i]=n=new Ar(i*this.vertexSize*4)),n}getIndexBuffer(t){const e=xs(Math.ceil(t/12)),s=Xi(e),i=e*12;this._iBuffers.length<=s&&(this._iBuffers.length=s+1);let n=this._iBuffers[s];return n||(this._iBuffers[s]=n=new Uint16Array(i)),n}packInterleavedGeometry(t,e,s,i,n){const{uint32View:a,float32View:o}=e,h=i/this.vertexSize,l=t.uvs,u=t.indices,c=t.vertexData,d=t._texture.baseTexture._batchLocation,f=Math.min(t.worldAlpha,1),p=Y.shared.setValue(t._tintRGB).toPremultiplied(f,t._texture.baseTexture.alphaMode>0);for(let m=0;m<c.length;m+=2)o[i++]=c[m],o[i++]=c[m+1],o[i++]=l[m],o[i++]=l[m+1],a[i++]=p,o[i++]=d;for(let m=0;m<u.length;m++)s[n++]=h+u[m]}};Rs.defaultBatchSize=4096,Rs.extension={name:"batch",type:D.RendererPlugin},Rs._drawCallPool=[],Rs._textureArrayPool=[];let Ee=Rs;U.add(Ee);var Ld=`varying vec2 vTextureCoord;

uniform sampler2D uSampler;

void main(void){
   gl_FragColor = texture2D(uSampler, vTextureCoord);
}
`,Ud=`attribute vec2 aVertexPosition;

uniform mat3 projectionMatrix;

varying vec2 vTextureCoord;

uniform vec4 inputSize;
uniform vec4 outputFrame;

vec4 filterVertexPosition( void )
{
    vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;

    return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
}

vec2 filterTextureCoord( void )
{
    return aVertexPosition * (outputFrame.zw * inputSize.zw);
}

void main(void)
{
    gl_Position = filterVertexPosition();
    vTextureCoord = filterTextureCoord();
}
`;const un=class ar extends Wt{constructor(t,e,s){const i=se.from(t||ar.defaultVertexSrc,e||ar.defaultFragmentSrc);super(i,s),this.padding=0,this.resolution=ar.defaultResolution,this.multisample=ar.defaultMultisample,this.enabled=!0,this.autoFit=!0,this.state=new ee}apply(t,e,s,i,n){t.applyFilter(this,e,s,i)}get blendMode(){return this.state.blendMode}set blendMode(t){this.state.blendMode=t}get resolution(){return this._resolution}set resolution(t){this._resolution=t}static get defaultVertexSrc(){return Ud}static get defaultFragmentSrc(){return Ld}};un.defaultResolution=1,un.defaultMultisample=ft.NONE;let Et=un;class Is{constructor(){this.clearBeforeRender=!0,this._backgroundColor=new Y(0),this.alpha=1}init(t){this.clearBeforeRender=t.clearBeforeRender;const{backgroundColor:e,background:s,backgroundAlpha:i}=t,n=s!=null?s:e;n!==void 0&&(this.color=n),this.alpha=i}get color(){return this._backgroundColor.value}set color(t){this._backgroundColor.setValue(t)}get alpha(){return this._backgroundColor.alpha}set alpha(t){this._backgroundColor.setAlpha(t)}get backgroundColor(){return this._backgroundColor}destroy(){}}Is.defaultOptions={backgroundAlpha:1,backgroundColor:0,clearBeforeRender:!0},Is.extension={type:[D.RendererSystem,D.CanvasRendererSystem],name:"background"},U.add(Is);class cn{constructor(t){this.renderer=t,this.emptyRenderer=new Cs(t),this.currentRenderer=this.emptyRenderer}setObjectRenderer(t){this.currentRenderer!==t&&(this.currentRenderer.stop(),this.currentRenderer=t,this.currentRenderer.start())}flush(){this.setObjectRenderer(this.emptyRenderer)}reset(){this.setObjectRenderer(this.emptyRenderer)}copyBoundTextures(t,e){const{boundTextures:s}=this.renderer.texture;for(let i=e-1;i>=0;--i)t[i]=s[i]||null,t[i]&&(t[i]._batchLocation=i)}boundArray(t,e,s,i){const{elements:n,ids:a,count:o}=t;let h=0;for(let l=0;l<o;l++){const u=n[l],c=u._batchLocation;if(c>=0&&c<i&&e[c]===u){a[l]=c;continue}for(;h<i;){const d=e[h];if(d&&d._batchEnabled===s&&d._batchLocation===h){h++;continue}a[l]=h,u._batchLocation=h,e[h]=u;break}}}destroy(){this.renderer=null}}cn.extension={type:D.RendererSystem,name:"batch"},U.add(cn);let eh=0;class Ps{constructor(t){this.renderer=t,this.webGLVersion=1,this.extensions={},this.supports={uint32Indices:!1},this.handleContextLost=this.handleContextLost.bind(this),this.handleContextRestored=this.handleContextRestored.bind(this)}get isLost(){return!this.gl||this.gl.isContextLost()}contextChange(t){this.gl=t,this.renderer.gl=t,this.renderer.CONTEXT_UID=eh++}init(t){if(t.context)this.initFromContext(t.context);else{const e=this.renderer.background.alpha<1,s=t.premultipliedAlpha;this.preserveDrawingBuffer=t.preserveDrawingBuffer,this.useContextAlpha=t.useContextAlpha,this.powerPreference=t.powerPreference,this.initFromOptions({alpha:e,premultipliedAlpha:s,antialias:t.antialias,stencil:!0,preserveDrawingBuffer:t.preserveDrawingBuffer,powerPreference:t.powerPreference})}}initFromContext(t){this.gl=t,this.validateContext(t),this.renderer.gl=t,this.renderer.CONTEXT_UID=eh++,this.renderer.runners.contextChange.emit(t);const e=this.renderer.view;e.addEventListener!==void 0&&(e.addEventListener("webglcontextlost",this.handleContextLost,!1),e.addEventListener("webglcontextrestored",this.handleContextRestored,!1))}initFromOptions(t){const e=this.createContext(this.renderer.view,t);this.initFromContext(e)}createContext(t,e){let s;if(N.PREFER_ENV>=be.WEBGL2&&(s=t.getContext("webgl2",e)),s)this.webGLVersion=2;else if(this.webGLVersion=1,s=t.getContext("webgl",e)||t.getContext("experimental-webgl",e),!s)throw new Error("This browser does not support WebGL. Try using the canvas renderer");return this.gl=s,this.getExtensions(),this.gl}getExtensions(){const{gl:t}=this,e={loseContext:t.getExtension("WEBGL_lose_context"),anisotropicFiltering:t.getExtension("EXT_texture_filter_anisotropic"),floatTextureLinear:t.getExtension("OES_texture_float_linear"),s3tc:t.getExtension("WEBGL_compressed_texture_s3tc"),s3tc_sRGB:t.getExtension("WEBGL_compressed_texture_s3tc_srgb"),etc:t.getExtension("WEBGL_compressed_texture_etc"),etc1:t.getExtension("WEBGL_compressed_texture_etc1"),pvrtc:t.getExtension("WEBGL_compressed_texture_pvrtc")||t.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),atc:t.getExtension("WEBGL_compressed_texture_atc"),astc:t.getExtension("WEBGL_compressed_texture_astc"),bptc:t.getExtension("EXT_texture_compression_bptc")};this.webGLVersion===1?Object.assign(this.extensions,e,{drawBuffers:t.getExtension("WEBGL_draw_buffers"),depthTexture:t.getExtension("WEBGL_depth_texture"),vertexArrayObject:t.getExtension("OES_vertex_array_object")||t.getExtension("MOZ_OES_vertex_array_object")||t.getExtension("WEBKIT_OES_vertex_array_object"),uint32ElementIndex:t.getExtension("OES_element_index_uint"),floatTexture:t.getExtension("OES_texture_float"),floatTextureLinear:t.getExtension("OES_texture_float_linear"),textureHalfFloat:t.getExtension("OES_texture_half_float"),textureHalfFloatLinear:t.getExtension("OES_texture_half_float_linear")}):this.webGLVersion===2&&Object.assign(this.extensions,e,{colorBufferFloat:t.getExtension("EXT_color_buffer_float")})}handleContextLost(t){t.preventDefault(),setTimeout(()=>{this.gl.isContextLost()&&this.extensions.loseContext&&this.extensions.loseContext.restoreContext()},0)}handleContextRestored(){this.renderer.runners.contextChange.emit(this.gl)}destroy(){const t=this.renderer.view;this.renderer=null,t.removeEventListener!==void 0&&(t.removeEventListener("webglcontextlost",this.handleContextLost),t.removeEventListener("webglcontextrestored",this.handleContextRestored)),this.gl.useProgram(null),this.extensions.loseContext&&this.extensions.loseContext.loseContext()}postrender(){this.renderer.objectRenderer.renderingToScreen&&this.gl.flush()}validateContext(t){const e=t.getContextAttributes(),s="WebGL2RenderingContext"in globalThis&&t instanceof globalThis.WebGL2RenderingContext;s&&(this.webGLVersion=2),e&&!e.stencil&&console.warn("Provided WebGL context does not have a stencil buffer, masks may not render correctly");const i=s||!!t.getExtension("OES_element_index_uint");this.supports.uint32Indices=i,i||console.warn("Provided WebGL context does not support 32 index buffer, complex graphics may not render correctly")}}Ps.defaultOptions={context:null,antialias:!1,premultipliedAlpha:!0,preserveDrawingBuffer:!1,powerPreference:"default"},Ps.extension={type:D.RendererSystem,name:"context"},U.add(Ps);class Lr{constructor(t,e){if(this.width=Math.round(t),this.height=Math.round(e),!this.width||!this.height)throw new Error("Framebuffer width or height is zero");this.stencil=!1,this.depth=!1,this.dirtyId=0,this.dirtyFormat=0,this.dirtySize=0,this.depthTexture=null,this.colorTextures=[],this.glFramebuffers={},this.disposeRunner=new Pt("disposeFramebuffer"),this.multisample=ft.NONE}get colorTexture(){return this.colorTextures[0]}addColorTexture(t=0,e){return this.colorTextures[t]=e||new X(null,{scaleMode:Bt.NEAREST,resolution:1,mipmap:Ht.OFF,width:this.width,height:this.height}),this.dirtyId++,this.dirtyFormat++,this}addDepthTexture(t){return this.depthTexture=t||new X(null,{scaleMode:Bt.NEAREST,resolution:1,width:this.width,height:this.height,mipmap:Ht.OFF,format:P.DEPTH_COMPONENT,type:$.UNSIGNED_SHORT}),this.dirtyId++,this.dirtyFormat++,this}enableDepth(){return this.depth=!0,this.dirtyId++,this.dirtyFormat++,this}enableStencil(){return this.stencil=!0,this.dirtyId++,this.dirtyFormat++,this}resize(t,e){if(t=Math.round(t),e=Math.round(e),!t||!e)throw new Error("Framebuffer width and height must not be zero");if(!(t===this.width&&e===this.height)){this.width=t,this.height=e,this.dirtyId++,this.dirtySize++;for(let s=0;s<this.colorTextures.length;s++){const i=this.colorTextures[s],n=i.resolution;i.setSize(t/n,e/n)}if(this.depthTexture){const s=this.depthTexture.resolution;this.depthTexture.setSize(t/s,e/s)}}}dispose(){this.disposeRunner.emit(this,!1)}destroyDepthTexture(){this.depthTexture&&(this.depthTexture.destroy(),this.depthTexture=null,++this.dirtyId,++this.dirtyFormat)}}class Ur extends X{constructor(t={}){var e,s,i;if(typeof t=="number"){const n=arguments[0],a=arguments[1],o=arguments[2],h=arguments[3];t={width:n,height:a,scaleMode:o,resolution:h}}t.width=(e=t.width)!=null?e:100,t.height=(s=t.height)!=null?s:100,(i=t.multisample)!=null||(t.multisample=ft.NONE),super(null,t),this.mipmap=Ht.OFF,this.valid=!0,this._clear=new Y([0,0,0,0]),this.framebuffer=new Lr(this.realWidth,this.realHeight).addColorTexture(0,this),this.framebuffer.multisample=t.multisample,this.maskStack=[],this.filterStack=[{}]}set clearColor(t){this._clear.setValue(t)}get clearColor(){return this._clear.value}get clear(){return this._clear}get multisample(){return this.framebuffer.multisample}set multisample(t){this.framebuffer.multisample=t}resize(t,e){this.framebuffer.resize(t*this.resolution,e*this.resolution),this.setRealSize(this.framebuffer.width,this.framebuffer.height)}dispose(){this.framebuffer.dispose(),super.dispose()}destroy(){super.destroy(),this.framebuffer.destroyDepthTexture(),this.framebuffer=null}}class re extends Qe{constructor(t){const e=t,s=e.naturalWidth||e.videoWidth||e.displayWidth||e.width,i=e.naturalHeight||e.videoHeight||e.displayHeight||e.height;super(s,i),this.source=t,this.noSubImage=!1}static crossOrigin(t,e,s){s===void 0&&!e.startsWith("data:")?t.crossOrigin=Mo(e):s!==!1&&(t.crossOrigin=typeof s=="string"?s:"anonymous")}upload(t,e,s,i){const n=t.gl,a=e.realWidth,o=e.realHeight;if(i=i||this.source,typeof HTMLImageElement!="undefined"&&i instanceof HTMLImageElement){if(!i.complete||i.naturalWidth===0)return!1}else if(typeof HTMLVideoElement!="undefined"&&i instanceof HTMLVideoElement&&i.readyState<=1)return!1;return n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,e.alphaMode===wt.UNPACK),!this.noSubImage&&e.target===n.TEXTURE_2D&&s.width===a&&s.height===o?n.texSubImage2D(n.TEXTURE_2D,0,0,0,e.format,s.type,i):(s.width=a,s.height=o,n.texImage2D(e.target,0,s.internalFormat,e.format,s.type,i)),!0}update(){if(this.destroyed)return;const t=this.source,e=t.naturalWidth||t.videoWidth||t.width,s=t.naturalHeight||t.videoHeight||t.height;this.resize(e,s),super.update()}dispose(){this.source=null}}class dn extends re{constructor(t,e){var s;if(e=e||{},typeof t=="string"){const i=new Image;re.crossOrigin(i,t,e.crossorigin),i.src=t,t=i}super(t),!t.complete&&this._width&&this._height&&(this._width=0,this._height=0),this.url=t.src,this._process=null,this.preserveBitmap=!1,this.createBitmap=((s=e.createBitmap)!=null?s:N.CREATE_IMAGE_BITMAP)&&!!globalThis.createImageBitmap,this.alphaMode=typeof e.alphaMode=="number"?e.alphaMode:null,this.bitmap=null,this._load=null,e.autoLoad!==!1&&this.load()}load(t){return this._load?this._load:(t!==void 0&&(this.createBitmap=t),this._load=new Promise((e,s)=>{const i=this.source;this.url=i.src;const n=()=>{this.destroyed||(i.onload=null,i.onerror=null,this.update(),this._load=null,this.createBitmap?e(this.process()):e(this))};i.complete&&i.src?n():(i.onload=n,i.onerror=a=>{s(a),this.onError.emit(a)})}),this._load)}process(){const t=this.source;if(this._process!==null)return this._process;if(this.bitmap!==null||!globalThis.createImageBitmap)return Promise.resolve(this);const e=globalThis.createImageBitmap,s=!t.crossOrigin||t.crossOrigin==="anonymous";return this._process=fetch(t.src,{mode:s?"cors":"no-cors"}).then(i=>i.blob()).then(i=>e(i,0,0,t.width,t.height,{premultiplyAlpha:this.alphaMode===null||this.alphaMode===wt.UNPACK?"premultiply":"none"})).then(i=>this.destroyed?Promise.reject():(this.bitmap=i,this.update(),this._process=null,Promise.resolve(this))),this._process}upload(t,e,s){if(typeof this.alphaMode=="number"&&(e.alphaMode=this.alphaMode),!this.createBitmap)return super.upload(t,e,s);if(!this.bitmap&&(this.process(),!this.bitmap))return!1;if(super.upload(t,e,s,this.bitmap),!this.preserveBitmap){let i=!0;const n=e._glTextures;for(const a in n){const o=n[a];if(o!==s&&o.dirtyId!==e.dirtyId){i=!1;break}}i&&(this.bitmap.close&&this.bitmap.close(),this.bitmap=null)}return!0}dispose(){this.source.onload=null,this.source.onerror=null,super.dispose(),this.bitmap&&(this.bitmap.close(),this.bitmap=null),this._process=null,this._load=null}static test(t){return typeof HTMLImageElement!="undefined"&&(typeof t=="string"||t instanceof HTMLImageElement)}}class fn{constructor(){this.x0=0,this.y0=0,this.x1=1,this.y1=0,this.x2=1,this.y2=1,this.x3=0,this.y3=1,this.uvsFloat32=new Float32Array(8)}set(t,e,s){const i=e.width,n=e.height;if(s){const a=t.width/2/i,o=t.height/2/n,h=t.x/i+a,l=t.y/n+o;s=at.add(s,at.NW),this.x0=h+a*at.uX(s),this.y0=l+o*at.uY(s),s=at.add(s,2),this.x1=h+a*at.uX(s),this.y1=l+o*at.uY(s),s=at.add(s,2),this.x2=h+a*at.uX(s),this.y2=l+o*at.uY(s),s=at.add(s,2),this.x3=h+a*at.uX(s),this.y3=l+o*at.uY(s)}else this.x0=t.x/i,this.y0=t.y/n,this.x1=(t.x+t.width)/i,this.y1=t.y/n,this.x2=(t.x+t.width)/i,this.y2=(t.y+t.height)/n,this.x3=t.x/i,this.y3=(t.y+t.height)/n;this.uvsFloat32[0]=this.x0,this.uvsFloat32[1]=this.y0,this.uvsFloat32[2]=this.x1,this.uvsFloat32[3]=this.y1,this.uvsFloat32[4]=this.x2,this.uvsFloat32[5]=this.y2,this.uvsFloat32[6]=this.x3,this.uvsFloat32[7]=this.y3}}const sh=new fn;function kr(r){r.destroy=function(){},r.on=function(){},r.once=function(){},r.emit=function(){}}class L extends Ye{constructor(t,e,s,i,n,a,o){if(super(),this.noFrame=!1,e||(this.noFrame=!0,e=new z(0,0,1,1)),t instanceof L&&(t=t.baseTexture),this.baseTexture=t,this._frame=e,this.trim=i,this.valid=!1,this.destroyed=!1,this._uvs=sh,this.uvMatrix=null,this.orig=s||e,this._rotate=Number(n||0),n===!0)this._rotate=2;else if(this._rotate%2!==0)throw new Error("attempt to use diamond-shaped UVs. If you are sure, set rotation manually");this.defaultAnchor=a?new K(a.x,a.y):new K(0,0),this.defaultBorders=o,this._updateID=0,this.textureCacheIds=[],t.valid?this.noFrame?t.valid&&this.onBaseTextureUpdated(t):this.frame=e:t.once("loaded",this.onBaseTextureUpdated,this),this.noFrame&&t.on("update",this.onBaseTextureUpdated,this)}update(){this.baseTexture.resource&&this.baseTexture.resource.update()}onBaseTextureUpdated(t){if(this.noFrame){if(!this.baseTexture.valid)return;this._frame.width=t.width,this._frame.height=t.height,this.valid=!0,this.updateUvs()}else this.frame=this._frame;this.emit("update",this)}destroy(t){if(this.baseTexture){if(t){const{resource:e}=this.baseTexture;e!=null&&e.url&&St[e.url]&&L.removeFromCache(e.url),this.baseTexture.destroy()}this.baseTexture.off("loaded",this.onBaseTextureUpdated,this),this.baseTexture.off("update",this.onBaseTextureUpdated,this),this.baseTexture=null}this._frame=null,this._uvs=null,this.trim=null,this.orig=null,this.valid=!1,L.removeFromCache(this),this.textureCacheIds=null,this.destroyed=!0,this.emit("destroyed",this),this.removeAllListeners()}clone(){var t;const e=this._frame.clone(),s=this._frame===this.orig?e:this.orig.clone(),i=new L(this.baseTexture,!this.noFrame&&e,s,(t=this.trim)==null?void 0:t.clone(),this.rotate,this.defaultAnchor,this.defaultBorders);return this.noFrame&&(i._frame=e),i}updateUvs(){this._uvs===sh&&(this._uvs=new fn),this._uvs.set(this._frame,this.baseTexture,this.rotate),this._updateID++}static from(t,e={},s=N.STRICT_TEXTURE_CACHE){const i=typeof t=="string";let n=null;if(i)n=t;else if(t instanceof X){if(!t.cacheId){const o=(e==null?void 0:e.pixiIdPrefix)||"pixiid";t.cacheId=`${o}-${Te()}`,X.addToCache(t,t.cacheId)}n=t.cacheId}else{if(!t._pixiId){const o=(e==null?void 0:e.pixiIdPrefix)||"pixiid";t._pixiId=`${o}_${Te()}`}n=t._pixiId}let a=St[n];if(i&&s&&!a)throw new Error(`The cacheId "${n}" does not exist in TextureCache.`);return!a&&!(t instanceof X)?(e.resolution||(e.resolution=te(t)),a=new L(new X(t,e)),a.baseTexture.cacheId=n,X.addToCache(a.baseTexture,n),L.addToCache(a,n)):!a&&t instanceof X&&(a=new L(t),L.addToCache(a,n)),a}static fromURL(t,e){const s=Object.assign({autoLoad:!1},e==null?void 0:e.resourceOptions),i=L.from(t,Object.assign({resourceOptions:s},e),!1),n=i.baseTexture.resource;return i.baseTexture.valid?Promise.resolve(i):n.load().then(()=>Promise.resolve(i))}static fromBuffer(t,e,s,i){return new L(X.fromBuffer(t,e,s,i))}static fromLoader(t,e,s,i){const n=new X(t,Object.assign({scaleMode:X.defaultOptions.scaleMode,resolution:te(e)},i)),{resource:a}=n;a instanceof dn&&(a.url=e);const o=new L(n);return s||(s=e),X.addToCache(o.baseTexture,s),L.addToCache(o,s),s!==e&&(X.addToCache(o.baseTexture,e),L.addToCache(o,e)),o.baseTexture.valid?Promise.resolve(o):new Promise(h=>{o.baseTexture.once("loaded",()=>h(o))})}static addToCache(t,e){e&&(t.textureCacheIds.includes(e)||t.textureCacheIds.push(e),St[e]&&St[e]!==t&&console.warn(`Texture added to the cache with an id [${e}] that already had an entry`),St[e]=t)}static removeFromCache(t){if(typeof t=="string"){const e=St[t];if(e){const s=e.textureCacheIds.indexOf(t);return s>-1&&e.textureCacheIds.splice(s,1),delete St[t],e}}else if(t!=null&&t.textureCacheIds){for(let e=0;e<t.textureCacheIds.length;++e)St[t.textureCacheIds[e]]===t&&delete St[t.textureCacheIds[e]];return t.textureCacheIds.length=0,t}return null}get resolution(){return this.baseTexture.resolution}get frame(){return this._frame}set frame(t){this._frame=t,this.noFrame=!1;const{x:e,y:s,width:i,height:n}=t,a=e+i>this.baseTexture.width,o=s+n>this.baseTexture.height;if(a||o){const h=a&&o?"and":"or",l=`X: ${e} + ${i} = ${e+i} > ${this.baseTexture.width}`,u=`Y: ${s} + ${n} = ${s+n} > ${this.baseTexture.height}`;throw new Error(`Texture Error: frame does not fit inside the base Texture dimensions: ${l} ${h} ${u}`)}this.valid=i&&n&&this.baseTexture.valid,!this.trim&&!this.rotate&&(this.orig=t),this.valid&&this.updateUvs()}get rotate(){return this._rotate}set rotate(t){this._rotate=t,this.valid&&this.updateUvs()}get width(){return this.orig.width}get height(){return this.orig.height}castToBaseTexture(){return this.baseTexture}static get EMPTY(){return L._EMPTY||(L._EMPTY=new L(new X),kr(L._EMPTY),kr(L._EMPTY.baseTexture)),L._EMPTY}static get WHITE(){if(!L._WHITE){const t=N.ADAPTER.createCanvas(16,16),e=t.getContext("2d");t.width=16,t.height=16,e.fillStyle="white",e.fillRect(0,0,16,16),L._WHITE=new L(X.from(t)),kr(L._WHITE),kr(L._WHITE.baseTexture)}return L._WHITE}}class Yt extends L{constructor(t,e){super(t,e),this.valid=!0,this.filterFrame=null,this.filterPoolKey=null,this.updateUvs()}get framebuffer(){return this.baseTexture.framebuffer}get multisample(){return this.framebuffer.multisample}set multisample(t){this.framebuffer.multisample=t}resize(t,e,s=!0){const i=this.baseTexture.resolution,n=Math.round(t*i)/i,a=Math.round(e*i)/i;this.valid=n>0&&a>0,this._frame.width=this.orig.width=n,this._frame.height=this.orig.height=a,s&&this.baseTexture.resize(n,a),this.updateUvs()}setResolution(t){const{baseTexture:e}=this;e.resolution!==t&&(e.setResolution(t),this.resize(e.width,e.height,!1))}static create(t){return new Yt(new Ur(t))}}class pn{constructor(t){this.texturePool={},this.textureOptions=t||{},this.enableFullScreen=!1,this._pixelsWidth=0,this._pixelsHeight=0}createTexture(t,e,s=ft.NONE){const i=new Ur(Object.assign({width:t,height:e,resolution:1,multisample:s},this.textureOptions));return new Yt(i)}getOptimalTexture(t,e,s=1,i=ft.NONE){let n;t=Math.max(Math.ceil(t*s-1e-6),1),e=Math.max(Math.ceil(e*s-1e-6),1),!this.enableFullScreen||t!==this._pixelsWidth||e!==this._pixelsHeight?(t=xs(t),e=xs(e),n=((t&65535)<<16|e&65535)>>>0,i>1&&(n+=i*4294967296)):n=i>1?-i:-1,this.texturePool[n]||(this.texturePool[n]=[]);let a=this.texturePool[n].pop();return a||(a=this.createTexture(t,e,i)),a.filterPoolKey=n,a.setResolution(s),a}getFilterTexture(t,e,s){const i=this.getOptimalTexture(t.width,t.height,e||t.resolution,s||ft.NONE);return i.filterFrame=t.filterFrame,i}returnTexture(t){const e=t.filterPoolKey;t.filterFrame=null,this.texturePool[e].push(t)}returnFilterTexture(t){this.returnTexture(t)}clear(t){if(t=t!==!1,t)for(const e in this.texturePool){const s=this.texturePool[e];if(s)for(let i=0;i<s.length;i++)s[i].destroy(!0)}this.texturePool={}}setScreenSize(t){if(!(t.width===this._pixelsWidth&&t.height===this._pixelsHeight)){this.enableFullScreen=t.width>0&&t.height>0;for(const e in this.texturePool){if(!(Number(e)<0))continue;const s=this.texturePool[e];if(s)for(let i=0;i<s.length;i++)s[i].destroy(!0);this.texturePool[e]=[]}this._pixelsWidth=t.width,this._pixelsHeight=t.height}}}pn.SCREEN_KEY=-1;class rh extends fe{constructor(){super(),this.addAttribute("aVertexPosition",new Float32Array([0,0,1,0,1,1,0,1])).addIndex([0,1,3,2])}}class mn extends fe{constructor(){super(),this.vertices=new Float32Array([-1,-1,1,-1,1,1,-1,1]),this.uvs=new Float32Array([0,0,1,0,1,1,0,1]),this.vertexBuffer=new dt(this.vertices),this.uvBuffer=new dt(this.uvs),this.addAttribute("aVertexPosition",this.vertexBuffer).addAttribute("aTextureCoord",this.uvBuffer).addIndex([0,1,2,0,2,3])}map(t,e){let s=0,i=0;return this.uvs[0]=s,this.uvs[1]=i,this.uvs[2]=s+e.width/t.width,this.uvs[3]=i,this.uvs[4]=s+e.width/t.width,this.uvs[5]=i+e.height/t.height,this.uvs[6]=s,this.uvs[7]=i+e.height/t.height,s=e.x,i=e.y,this.vertices[0]=s,this.vertices[1]=i,this.vertices[2]=s+e.width,this.vertices[3]=i,this.vertices[4]=s+e.width,this.vertices[5]=i+e.height,this.vertices[6]=s,this.vertices[7]=i+e.height,this.invalidate(),this}invalidate(){return this.vertexBuffer._updateID++,this.uvBuffer._updateID++,this}}class ih{constructor(){this.renderTexture=null,this.target=null,this.legacy=!1,this.resolution=1,this.multisample=ft.NONE,this.sourceFrame=new z,this.destinationFrame=new z,this.bindingSourceFrame=new z,this.bindingDestinationFrame=new z,this.filters=[],this.transform=null}clear(){this.target=null,this.filters=null,this.renderTexture=null}}const Gr=[new K,new K,new K,new K],gn=new tt;class _n{constructor(t){this.renderer=t,this.defaultFilterStack=[{}],this.texturePool=new pn,this.statePool=[],this.quad=new rh,this.quadUv=new mn,this.tempRect=new z,this.activeState={},this.globalUniforms=new Lt({outputFrame:new z,inputSize:new Float32Array(4),inputPixel:new Float32Array(4),inputClamp:new Float32Array(4),resolution:1,filterArea:new Float32Array(4),filterClamp:new Float32Array(4)},!0),this.forceClear=!1,this.useMaxPadding=!1}init(){this.texturePool.setScreenSize(this.renderer.view)}push(t,e){var s,i,n,a;const o=this.renderer,h=this.defaultFilterStack,l=this.statePool.pop()||new ih,u=o.renderTexture;let c,d;if(u.current){const b=u.current;c=b.resolution,d=b.multisample}else c=o.resolution,d=o.multisample;let f=e[0].resolution||c,p=(s=e[0].multisample)!=null?s:d,m=e[0].padding,g=e[0].autoFit,_=(i=e[0].legacy)!=null?i:!0;for(let b=1;b<e.length;b++){const T=e[b];f=Math.min(f,T.resolution||c),p=Math.min(p,(n=T.multisample)!=null?n:d),m=this.useMaxPadding?Math.max(m,T.padding):m+T.padding,g=g&&T.autoFit,_=_||((a=T.legacy)!=null?a:!0)}h.length===1&&(this.defaultFilterStack[0].renderTexture=u.current),h.push(l),l.resolution=f,l.multisample=p,l.legacy=_,l.target=t,l.sourceFrame.copyFrom(t.filterArea||t.getBounds(!0)),l.sourceFrame.pad(m);const x=this.tempRect.copyFrom(u.sourceFrame);o.projection.transform&&this.transformAABB(gn.copyFrom(o.projection.transform).invert(),x),g?(l.sourceFrame.fit(x),(l.sourceFrame.width<=0||l.sourceFrame.height<=0)&&(l.sourceFrame.width=0,l.sourceFrame.height=0)):l.sourceFrame.intersects(x)||(l.sourceFrame.width=0,l.sourceFrame.height=0),this.roundFrame(l.sourceFrame,u.current?u.current.resolution:o.resolution,u.sourceFrame,u.destinationFrame,o.projection.transform),l.renderTexture=this.getOptimalFilterTexture(l.sourceFrame.width,l.sourceFrame.height,f,p),l.filters=e,l.destinationFrame.width=l.renderTexture.width,l.destinationFrame.height=l.renderTexture.height;const y=this.tempRect;y.x=0,y.y=0,y.width=l.sourceFrame.width,y.height=l.sourceFrame.height,l.renderTexture.filterFrame=l.sourceFrame,l.bindingSourceFrame.copyFrom(u.sourceFrame),l.bindingDestinationFrame.copyFrom(u.destinationFrame),l.transform=o.projection.transform,o.projection.transform=null,u.bind(l.renderTexture,l.sourceFrame,y),o.framebuffer.clear(0,0,0,0)}pop(){const t=this.defaultFilterStack,e=t.pop(),s=e.filters;this.activeState=e;const i=this.globalUniforms.uniforms;i.outputFrame=e.sourceFrame,i.resolution=e.resolution;const n=i.inputSize,a=i.inputPixel,o=i.inputClamp;if(n[0]=e.destinationFrame.width,n[1]=e.destinationFrame.height,n[2]=1/n[0],n[3]=1/n[1],a[0]=Math.round(n[0]*e.resolution),a[1]=Math.round(n[1]*e.resolution),a[2]=1/a[0],a[3]=1/a[1],o[0]=.5*a[2],o[1]=.5*a[3],o[2]=e.sourceFrame.width*n[2]-.5*a[2],o[3]=e.sourceFrame.height*n[3]-.5*a[3],e.legacy){const l=i.filterArea;l[0]=e.destinationFrame.width,l[1]=e.destinationFrame.height,l[2]=e.sourceFrame.x,l[3]=e.sourceFrame.y,i.filterClamp=i.inputClamp}this.globalUniforms.update();const h=t[t.length-1];if(this.renderer.framebuffer.blit(),s.length===1)s[0].apply(this,e.renderTexture,h.renderTexture,Vt.BLEND,e),this.returnFilterTexture(e.renderTexture);else{let l=e.renderTexture,u=this.getOptimalFilterTexture(l.width,l.height,e.resolution);u.filterFrame=l.filterFrame;let c=0;for(c=0;c<s.length-1;++c){c===1&&e.multisample>1&&(u=this.getOptimalFilterTexture(l.width,l.height,e.resolution),u.filterFrame=l.filterFrame),s[c].apply(this,l,u,Vt.CLEAR,e);const d=l;l=u,u=d}s[c].apply(this,l,h.renderTexture,Vt.BLEND,e),c>1&&e.multisample>1&&this.returnFilterTexture(e.renderTexture),this.returnFilterTexture(l),this.returnFilterTexture(u)}e.clear(),this.statePool.push(e)}bindAndClear(t,e=Vt.CLEAR){const{renderTexture:s,state:i}=this.renderer;if(t===this.defaultFilterStack[this.defaultFilterStack.length-1].renderTexture?this.renderer.projection.transform=this.activeState.transform:this.renderer.projection.transform=null,t!=null&&t.filterFrame){const a=this.tempRect;a.x=0,a.y=0,a.width=t.filterFrame.width,a.height=t.filterFrame.height,s.bind(t,t.filterFrame,a)}else t!==this.defaultFilterStack[this.defaultFilterStack.length-1].renderTexture?s.bind(t):this.renderer.renderTexture.bind(t,this.activeState.bindingSourceFrame,this.activeState.bindingDestinationFrame);const n=i.stateId&1||this.forceClear;(e===Vt.CLEAR||e===Vt.BLIT&&n)&&this.renderer.framebuffer.clear(0,0,0,0)}applyFilter(t,e,s,i){const n=this.renderer;n.state.set(t.state),this.bindAndClear(s,i),t.uniforms.uSampler=e,t.uniforms.filterGlobals=this.globalUniforms,n.shader.bind(t),t.legacy=!!t.program.attributeData.aTextureCoord,t.legacy?(this.quadUv.map(e._frame,e.filterFrame),n.geometry.bind(this.quadUv),n.geometry.draw(Ot.TRIANGLES)):(n.geometry.bind(this.quad),n.geometry.draw(Ot.TRIANGLE_STRIP))}calculateSpriteMatrix(t,e){const{sourceFrame:s,destinationFrame:i}=this.activeState,{orig:n}=e._texture,a=t.set(i.width,0,0,i.height,s.x,s.y),o=e.worldTransform.copyTo(tt.TEMP_MATRIX);return o.invert(),a.prepend(o),a.scale(1/n.width,1/n.height),a.translate(e.anchor.x,e.anchor.y),a}destroy(){this.renderer=null,this.texturePool.clear(!1)}getOptimalFilterTexture(t,e,s=1,i=ft.NONE){return this.texturePool.getOptimalTexture(t,e,s,i)}getFilterTexture(t,e,s){if(typeof t=="number"){const n=t;t=e,e=n}t=t||this.activeState.renderTexture;const i=this.texturePool.getOptimalTexture(t.width,t.height,e||t.resolution,s||ft.NONE);return i.filterFrame=t.filterFrame,i}returnFilterTexture(t){this.texturePool.returnTexture(t)}emptyPool(){this.texturePool.clear(!0)}resize(){this.texturePool.setScreenSize(this.renderer.view)}transformAABB(t,e){const s=Gr[0],i=Gr[1],n=Gr[2],a=Gr[3];s.set(e.left,e.top),i.set(e.left,e.bottom),n.set(e.right,e.top),a.set(e.right,e.bottom),t.apply(s,s),t.apply(i,i),t.apply(n,n),t.apply(a,a);const o=Math.min(s.x,i.x,n.x,a.x),h=Math.min(s.y,i.y,n.y,a.y),l=Math.max(s.x,i.x,n.x,a.x),u=Math.max(s.y,i.y,n.y,a.y);e.x=o,e.y=h,e.width=l-o,e.height=u-h}roundFrame(t,e,s,i,n){if(!(t.width<=0||t.height<=0||s.width<=0||s.height<=0)){if(n){const{a,b:o,c:h,d:l}=n;if((Math.abs(o)>1e-4||Math.abs(h)>1e-4)&&(Math.abs(a)>1e-4||Math.abs(l)>1e-4))return}n=n?gn.copyFrom(n):gn.identity(),n.translate(-s.x,-s.y).scale(i.width/s.width,i.height/s.height).translate(i.x,i.y),this.transformAABB(n,t),t.ceil(e),this.transformAABB(n.invert(),t)}}}_n.extension={type:D.RendererSystem,name:"filter"},U.add(_n);class nh{constructor(t){this.framebuffer=t,this.stencil=null,this.dirtyId=-1,this.dirtyFormat=-1,this.dirtySize=-1,this.multisample=ft.NONE,this.msaaBuffer=null,this.blitFramebuffer=null,this.mipLevel=0}}const kd=new z;class vn{constructor(t){this.renderer=t,this.managedFramebuffers=[],this.unknownFramebuffer=new Lr(10,10),this.msaaSamples=null}contextChange(){this.disposeAll(!0);const t=this.gl=this.renderer.gl;if(this.CONTEXT_UID=this.renderer.CONTEXT_UID,this.current=this.unknownFramebuffer,this.viewport=new z,this.hasMRT=!0,this.writeDepthTexture=!0,this.renderer.context.webGLVersion===1){let e=this.renderer.context.extensions.drawBuffers,s=this.renderer.context.extensions.depthTexture;N.PREFER_ENV===be.WEBGL_LEGACY&&(e=null,s=null),e?t.drawBuffers=i=>e.drawBuffersWEBGL(i):(this.hasMRT=!1,t.drawBuffers=()=>{}),s||(this.writeDepthTexture=!1)}else this.msaaSamples=t.getInternalformatParameter(t.RENDERBUFFER,t.RGBA8,t.SAMPLES)}bind(t,e,s=0){const{gl:i}=this;if(t){const n=t.glFramebuffers[this.CONTEXT_UID]||this.initFramebuffer(t);this.current!==t&&(this.current=t,i.bindFramebuffer(i.FRAMEBUFFER,n.framebuffer)),n.mipLevel!==s&&(t.dirtyId++,t.dirtyFormat++,n.mipLevel=s),n.dirtyId!==t.dirtyId&&(n.dirtyId=t.dirtyId,n.dirtyFormat!==t.dirtyFormat?(n.dirtyFormat=t.dirtyFormat,n.dirtySize=t.dirtySize,this.updateFramebuffer(t,s)):n.dirtySize!==t.dirtySize&&(n.dirtySize=t.dirtySize,this.resizeFramebuffer(t)));for(let a=0;a<t.colorTextures.length;a++){const o=t.colorTextures[a];this.renderer.texture.unbind(o.parentTextureArray||o)}if(t.depthTexture&&this.renderer.texture.unbind(t.depthTexture),e){const a=e.width>>s,o=e.height>>s,h=a/e.width;this.setViewport(e.x*h,e.y*h,a,o)}else{const a=t.width>>s,o=t.height>>s;this.setViewport(0,0,a,o)}}else this.current&&(this.current=null,i.bindFramebuffer(i.FRAMEBUFFER,null)),e?this.setViewport(e.x,e.y,e.width,e.height):this.setViewport(0,0,this.renderer.width,this.renderer.height)}setViewport(t,e,s,i){const n=this.viewport;t=Math.round(t),e=Math.round(e),s=Math.round(s),i=Math.round(i),(n.width!==s||n.height!==i||n.x!==t||n.y!==e)&&(n.x=t,n.y=e,n.width=s,n.height=i,this.gl.viewport(t,e,s,i))}get size(){return this.current?{x:0,y:0,width:this.current.width,height:this.current.height}:{x:0,y:0,width:this.renderer.width,height:this.renderer.height}}clear(t,e,s,i,n=dr.COLOR|dr.DEPTH){const{gl:a}=this;a.clearColor(t,e,s,i),a.clear(n)}initFramebuffer(t){const{gl:e}=this,s=new nh(e.createFramebuffer());return s.multisample=this.detectSamples(t.multisample),t.glFramebuffers[this.CONTEXT_UID]=s,this.managedFramebuffers.push(t),t.disposeRunner.add(this),s}resizeFramebuffer(t){const{gl:e}=this,s=t.glFramebuffers[this.CONTEXT_UID];if(s.stencil){e.bindRenderbuffer(e.RENDERBUFFER,s.stencil);let a;this.renderer.context.webGLVersion===1?a=e.DEPTH_STENCIL:t.depth&&t.stencil?a=e.DEPTH24_STENCIL8:t.depth?a=e.DEPTH_COMPONENT24:a=e.STENCIL_INDEX8,s.msaaBuffer?e.renderbufferStorageMultisample(e.RENDERBUFFER,s.multisample,a,t.width,t.height):e.renderbufferStorage(e.RENDERBUFFER,a,t.width,t.height)}const i=t.colorTextures;let n=i.length;e.drawBuffers||(n=Math.min(n,1));for(let a=0;a<n;a++){const o=i[a],h=o.parentTextureArray||o;this.renderer.texture.bind(h,0),a===0&&s.msaaBuffer&&(e.bindRenderbuffer(e.RENDERBUFFER,s.msaaBuffer),e.renderbufferStorageMultisample(e.RENDERBUFFER,s.multisample,h._glTextures[this.CONTEXT_UID].internalFormat,t.width,t.height))}t.depthTexture&&this.writeDepthTexture&&this.renderer.texture.bind(t.depthTexture,0)}updateFramebuffer(t,e){const{gl:s}=this,i=t.glFramebuffers[this.CONTEXT_UID],n=t.colorTextures;let a=n.length;s.drawBuffers||(a=Math.min(a,1)),i.multisample>1&&this.canMultisampleFramebuffer(t)?i.msaaBuffer=i.msaaBuffer||s.createRenderbuffer():i.msaaBuffer&&(s.deleteRenderbuffer(i.msaaBuffer),i.msaaBuffer=null,i.blitFramebuffer&&(i.blitFramebuffer.dispose(),i.blitFramebuffer=null));const o=[];for(let h=0;h<a;h++){const l=n[h],u=l.parentTextureArray||l;this.renderer.texture.bind(u,0),h===0&&i.msaaBuffer?(s.bindRenderbuffer(s.RENDERBUFFER,i.msaaBuffer),s.renderbufferStorageMultisample(s.RENDERBUFFER,i.multisample,u._glTextures[this.CONTEXT_UID].internalFormat,t.width,t.height),s.framebufferRenderbuffer(s.FRAMEBUFFER,s.COLOR_ATTACHMENT0,s.RENDERBUFFER,i.msaaBuffer)):(s.framebufferTexture2D(s.FRAMEBUFFER,s.COLOR_ATTACHMENT0+h,l.target,u._glTextures[this.CONTEXT_UID].texture,e),o.push(s.COLOR_ATTACHMENT0+h))}if(o.length>1&&s.drawBuffers(o),t.depthTexture&&this.writeDepthTexture){const h=t.depthTexture;this.renderer.texture.bind(h,0),s.framebufferTexture2D(s.FRAMEBUFFER,s.DEPTH_ATTACHMENT,s.TEXTURE_2D,h._glTextures[this.CONTEXT_UID].texture,e)}if((t.stencil||t.depth)&&!(t.depthTexture&&this.writeDepthTexture)){i.stencil=i.stencil||s.createRenderbuffer();let h,l;this.renderer.context.webGLVersion===1?(h=s.DEPTH_STENCIL_ATTACHMENT,l=s.DEPTH_STENCIL):t.depth&&t.stencil?(h=s.DEPTH_STENCIL_ATTACHMENT,l=s.DEPTH24_STENCIL8):t.depth?(h=s.DEPTH_ATTACHMENT,l=s.DEPTH_COMPONENT24):(h=s.STENCIL_ATTACHMENT,l=s.STENCIL_INDEX8),s.bindRenderbuffer(s.RENDERBUFFER,i.stencil),i.msaaBuffer?s.renderbufferStorageMultisample(s.RENDERBUFFER,i.multisample,l,t.width,t.height):s.renderbufferStorage(s.RENDERBUFFER,l,t.width,t.height),s.framebufferRenderbuffer(s.FRAMEBUFFER,h,s.RENDERBUFFER,i.stencil)}else i.stencil&&(s.deleteRenderbuffer(i.stencil),i.stencil=null)}canMultisampleFramebuffer(t){return this.renderer.context.webGLVersion!==1&&t.colorTextures.length<=1&&!t.depthTexture}detectSamples(t){const{msaaSamples:e}=this;let s=ft.NONE;if(t<=1||e===null)return s;for(let i=0;i<e.length;i++)if(e[i]<=t){s=e[i];break}return s===1&&(s=ft.NONE),s}blit(t,e,s){const{current:i,renderer:n,gl:a,CONTEXT_UID:o}=this;if(n.context.webGLVersion!==2||!i)return;const h=i.glFramebuffers[o];if(!h)return;if(!t){if(!h.msaaBuffer)return;const u=i.colorTextures[0];if(!u)return;h.blitFramebuffer||(h.blitFramebuffer=new Lr(i.width,i.height),h.blitFramebuffer.addColorTexture(0,u)),t=h.blitFramebuffer,t.colorTextures[0]!==u&&(t.colorTextures[0]=u,t.dirtyId++,t.dirtyFormat++),(t.width!==i.width||t.height!==i.height)&&(t.width=i.width,t.height=i.height,t.dirtyId++,t.dirtySize++)}e||(e=kd,e.width=i.width,e.height=i.height),s||(s=e);const l=e.width===s.width&&e.height===s.height;this.bind(t),a.bindFramebuffer(a.READ_FRAMEBUFFER,h.framebuffer),a.blitFramebuffer(e.left,e.top,e.right,e.bottom,s.left,s.top,s.right,s.bottom,a.COLOR_BUFFER_BIT,l?a.NEAREST:a.LINEAR),a.bindFramebuffer(a.READ_FRAMEBUFFER,t.glFramebuffers[this.CONTEXT_UID].framebuffer)}disposeFramebuffer(t,e){const s=t.glFramebuffers[this.CONTEXT_UID],i=this.gl;if(!s)return;delete t.glFramebuffers[this.CONTEXT_UID];const n=this.managedFramebuffers.indexOf(t);n>=0&&this.managedFramebuffers.splice(n,1),t.disposeRunner.remove(this),e||(i.deleteFramebuffer(s.framebuffer),s.msaaBuffer&&i.deleteRenderbuffer(s.msaaBuffer),s.stencil&&i.deleteRenderbuffer(s.stencil)),s.blitFramebuffer&&this.disposeFramebuffer(s.blitFramebuffer,e)}disposeAll(t){const e=this.managedFramebuffers;this.managedFramebuffers=[];for(let s=0;s<e.length;s++)this.disposeFramebuffer(e[s],t)}forceStencil(){const t=this.current;if(!t)return;const e=t.glFramebuffers[this.CONTEXT_UID];if(!e||e.stencil&&t.stencil)return;t.stencil=!0;const s=t.width,i=t.height,n=this.gl,a=e.stencil=n.createRenderbuffer();n.bindRenderbuffer(n.RENDERBUFFER,a);let o,h;this.renderer.context.webGLVersion===1?(o=n.DEPTH_STENCIL_ATTACHMENT,h=n.DEPTH_STENCIL):t.depth?(o=n.DEPTH_STENCIL_ATTACHMENT,h=n.DEPTH24_STENCIL8):(o=n.STENCIL_ATTACHMENT,h=n.STENCIL_INDEX8),e.msaaBuffer?n.renderbufferStorageMultisample(n.RENDERBUFFER,e.multisample,h,s,i):n.renderbufferStorage(n.RENDERBUFFER,h,s,i),n.framebufferRenderbuffer(n.FRAMEBUFFER,o,n.RENDERBUFFER,a)}reset(){this.current=this.unknownFramebuffer,this.viewport=new z}destroy(){this.renderer=null}}vn.extension={type:D.RendererSystem,name:"framebuffer"},U.add(vn);const yn={5126:4,5123:2,5121:1};class xn{constructor(t){this.renderer=t,this._activeGeometry=null,this._activeVao=null,this.hasVao=!0,this.hasInstance=!0,this.canUseUInt32ElementIndex=!1,this.managedGeometries={}}contextChange(){this.disposeAll(!0);const t=this.gl=this.renderer.gl,e=this.renderer.context;if(this.CONTEXT_UID=this.renderer.CONTEXT_UID,e.webGLVersion!==2){let s=this.renderer.context.extensions.vertexArrayObject;N.PREFER_ENV===be.WEBGL_LEGACY&&(s=null),s?(t.createVertexArray=()=>s.createVertexArrayOES(),t.bindVertexArray=i=>s.bindVertexArrayOES(i),t.deleteVertexArray=i=>s.deleteVertexArrayOES(i)):(this.hasVao=!1,t.createVertexArray=()=>null,t.bindVertexArray=()=>null,t.deleteVertexArray=()=>null)}if(e.webGLVersion!==2){const s=t.getExtension("ANGLE_instanced_arrays");s?(t.vertexAttribDivisor=(i,n)=>s.vertexAttribDivisorANGLE(i,n),t.drawElementsInstanced=(i,n,a,o,h)=>s.drawElementsInstancedANGLE(i,n,a,o,h),t.drawArraysInstanced=(i,n,a,o)=>s.drawArraysInstancedANGLE(i,n,a,o)):this.hasInstance=!1}this.canUseUInt32ElementIndex=e.webGLVersion===2||!!e.extensions.uint32ElementIndex}bind(t,e){e=e||this.renderer.shader.shader;const{gl:s}=this;let i=t.glVertexArrayObjects[this.CONTEXT_UID],n=!1;i||(this.managedGeometries[t.id]=t,t.disposeRunner.add(this),t.glVertexArrayObjects[this.CONTEXT_UID]=i={},n=!0);const a=i[e.program.id]||this.initGeometryVao(t,e,n);this._activeGeometry=t,this._activeVao!==a&&(this._activeVao=a,this.hasVao?s.bindVertexArray(a):this.activateVao(t,e.program)),this.updateBuffers()}reset(){this.unbind()}updateBuffers(){const t=this._activeGeometry,e=this.renderer.buffer;for(let s=0;s<t.buffers.length;s++){const i=t.buffers[s];e.update(i)}}checkCompatibility(t,e){const s=t.attributes,i=e.attributeData;for(const n in i)if(!s[n])throw new Error(`shader and geometry incompatible, geometry missing the "${n}" attribute`)}getSignature(t,e){const s=t.attributes,i=e.attributeData,n=["g",t.id];for(const a in s)i[a]&&n.push(a,i[a].location);return n.join("-")}initGeometryVao(t,e,s=!0){const i=this.gl,n=this.CONTEXT_UID,a=this.renderer.buffer,o=e.program;o.glPrograms[n]||this.renderer.shader.generateProgram(e),this.checkCompatibility(t,o);const h=this.getSignature(t,o),l=t.glVertexArrayObjects[this.CONTEXT_UID];let u=l[h];if(u)return l[o.id]=u,u;const c=t.buffers,d=t.attributes,f={},p={};for(const m in c)f[m]=0,p[m]=0;for(const m in d)!d[m].size&&o.attributeData[m]?d[m].size=o.attributeData[m].size:d[m].size||console.warn(`PIXI Geometry attribute '${m}' size cannot be determined (likely the bound shader does not have the attribute)`),f[d[m].buffer]+=d[m].size*yn[d[m].type];for(const m in d){const g=d[m],_=g.size;g.stride===void 0&&(f[g.buffer]===_*yn[g.type]?g.stride=0:g.stride=f[g.buffer]),g.start===void 0&&(g.start=p[g.buffer],p[g.buffer]+=_*yn[g.type])}u=i.createVertexArray(),i.bindVertexArray(u);for(let m=0;m<c.length;m++){const g=c[m];a.bind(g),s&&g._glBuffers[n].refCount++}return this.activateVao(t,o),l[o.id]=u,l[h]=u,i.bindVertexArray(null),a.unbind(jt.ARRAY_BUFFER),u}disposeGeometry(t,e){var s;if(!this.managedGeometries[t.id])return;delete this.managedGeometries[t.id];const i=t.glVertexArrayObjects[this.CONTEXT_UID],n=this.gl,a=t.buffers,o=(s=this.renderer)==null?void 0:s.buffer;if(t.disposeRunner.remove(this),!!i){if(o)for(let h=0;h<a.length;h++){const l=a[h]._glBuffers[this.CONTEXT_UID];l&&(l.refCount--,l.refCount===0&&!e&&o.dispose(a[h],e))}if(!e){for(const h in i)if(h[0]==="g"){const l=i[h];this._activeVao===l&&this.unbind(),n.deleteVertexArray(l)}}delete t.glVertexArrayObjects[this.CONTEXT_UID]}}disposeAll(t){const e=Object.keys(this.managedGeometries);for(let s=0;s<e.length;s++)this.disposeGeometry(this.managedGeometries[e[s]],t)}activateVao(t,e){const s=this.gl,i=this.CONTEXT_UID,n=this.renderer.buffer,a=t.buffers,o=t.attributes;t.indexBuffer&&n.bind(t.indexBuffer);let h=null;for(const l in o){const u=o[l],c=a[u.buffer],d=c._glBuffers[i];if(e.attributeData[l]){h!==d&&(n.bind(c),h=d);const f=e.attributeData[l].location;if(s.enableVertexAttribArray(f),s.vertexAttribPointer(f,u.size,u.type||s.FLOAT,u.normalized,u.stride,u.start),u.instance)if(this.hasInstance)s.vertexAttribDivisor(f,u.divisor);else throw new Error("geometry error, GPU Instancing is not supported on this device")}}}draw(t,e,s,i){const{gl:n}=this,a=this._activeGeometry;if(a.indexBuffer){const o=a.indexBuffer.data.BYTES_PER_ELEMENT,h=o===2?n.UNSIGNED_SHORT:n.UNSIGNED_INT;o===2||o===4&&this.canUseUInt32ElementIndex?a.instanced?n.drawElementsInstanced(t,e||a.indexBuffer.data.length,h,(s||0)*o,i||1):n.drawElements(t,e||a.indexBuffer.data.length,h,(s||0)*o):console.warn("unsupported index buffer type: uint32")}else a.instanced?n.drawArraysInstanced(t,s,e||a.getSize(),i||1):n.drawArrays(t,s,e||a.getSize());return this}unbind(){this.gl.bindVertexArray(null),this._activeVao=null,this._activeGeometry=null}destroy(){this.renderer=null}}xn.extension={type:D.RendererSystem,name:"geometry"},U.add(xn);const ah=new tt;class $r{constructor(t,e){this._texture=t,this.mapCoord=new tt,this.uClampFrame=new Float32Array(4),this.uClampOffset=new Float32Array(2),this._textureID=-1,this._updateID=0,this.clampOffset=0,this.clampMargin=typeof e=="undefined"?.5:e,this.isSimple=!1}get texture(){return this._texture}set texture(t){this._texture=t,this._textureID=-1}multiplyUvs(t,e){e===void 0&&(e=t);const s=this.mapCoord;for(let i=0;i<t.length;i+=2){const n=t[i],a=t[i+1];e[i]=n*s.a+a*s.c+s.tx,e[i+1]=n*s.b+a*s.d+s.ty}return e}update(t){const e=this._texture;if(!e||!e.valid||!t&&this._textureID===e._updateID)return!1;this._textureID=e._updateID,this._updateID++;const s=e._uvs;this.mapCoord.set(s.x1-s.x0,s.y1-s.y0,s.x3-s.x0,s.y3-s.y0,s.x0,s.y0);const i=e.orig,n=e.trim;n&&(ah.set(i.width/n.width,0,0,i.height/n.height,-n.x/n.width,-n.y/n.height),this.mapCoord.append(ah));const a=e.baseTexture,o=this.uClampFrame,h=this.clampMargin/a.resolution,l=this.clampOffset;return o[0]=(e._frame.x+h+l)/a.width,o[1]=(e._frame.y+h+l)/a.height,o[2]=(e._frame.x+e._frame.width-h+l)/a.width,o[3]=(e._frame.y+e._frame.height-h+l)/a.height,this.uClampOffset[0]=l/a.realWidth,this.uClampOffset[1]=l/a.realHeight,this.isSimple=e._frame.width===a.width&&e._frame.height===a.height&&e.rotate===0,!0}}var Gd=`varying vec2 vMaskCoord;
varying vec2 vTextureCoord;

uniform sampler2D uSampler;
uniform sampler2D mask;
uniform float alpha;
uniform float npmAlpha;
uniform vec4 maskClamp;

void main(void)
{
    float clip = step(3.5,
        step(maskClamp.x, vMaskCoord.x) +
        step(maskClamp.y, vMaskCoord.y) +
        step(vMaskCoord.x, maskClamp.z) +
        step(vMaskCoord.y, maskClamp.w));

    vec4 original = texture2D(uSampler, vTextureCoord);
    vec4 masky = texture2D(mask, vMaskCoord);
    float alphaMul = 1.0 - npmAlpha * (1.0 - masky.a);

    original *= (alphaMul * masky.r * alpha * clip);

    gl_FragColor = original;
}
`,$d=`attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;

uniform mat3 projectionMatrix;
uniform mat3 otherMatrix;

varying vec2 vMaskCoord;
varying vec2 vTextureCoord;

void main(void)
{
    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = aTextureCoord;
    vMaskCoord = ( otherMatrix * vec3( aTextureCoord, 1.0)  ).xy;
}
`;class oh extends Et{constructor(t,e,s){let i=null;typeof t!="string"&&e===void 0&&s===void 0&&(i=t,t=void 0,e=void 0,s=void 0),super(t||$d,e||Gd,s),this.maskSprite=i,this.maskMatrix=new tt}get maskSprite(){return this._maskSprite}set maskSprite(t){this._maskSprite=t,this._maskSprite&&(this._maskSprite.renderable=!1)}apply(t,e,s,i){const n=this._maskSprite,a=n._texture;a.valid&&(a.uvMatrix||(a.uvMatrix=new $r(a,0)),a.uvMatrix.update(),this.uniforms.npmAlpha=a.baseTexture.alphaMode?0:1,this.uniforms.mask=a,this.uniforms.otherMatrix=t.calculateSpriteMatrix(this.maskMatrix,n).prepend(a.uvMatrix.mapCoord),this.uniforms.alpha=n.worldAlpha,this.uniforms.maskClamp=a.uvMatrix.uClampFrame,t.applyFilter(this,e,s,i))}}class hh{constructor(t=null){this.type=pt.NONE,this.autoDetect=!0,this.maskObject=t||null,this.pooled=!1,this.isMaskData=!0,this.resolution=null,this.multisample=Et.defaultMultisample,this.enabled=!0,this.colorMask=15,this._filters=null,this._stencilCounter=0,this._scissorCounter=0,this._scissorRect=null,this._scissorRectLocal=null,this._colorMask=15,this._target=null}get filter(){return this._filters?this._filters[0]:null}set filter(t){t?this._filters?this._filters[0]=t:this._filters=[t]:this._filters=null}reset(){this.pooled&&(this.maskObject=null,this.type=pt.NONE,this.autoDetect=!0),this._target=null,this._scissorRectLocal=null}copyCountersOrReset(t){t?(this._stencilCounter=t._stencilCounter,this._scissorCounter=t._scissorCounter,this._scissorRect=t._scissorRect):(this._stencilCounter=0,this._scissorCounter=0,this._scissorRect=null)}}class bn{constructor(t){this.renderer=t,this.enableScissor=!0,this.alphaMaskPool=[],this.maskDataPool=[],this.maskStack=[],this.alphaMaskIndex=0}setMaskStack(t){this.maskStack=t,this.renderer.scissor.setMaskStack(t),this.renderer.stencil.setMaskStack(t)}push(t,e){let s=e;if(!s.isMaskData){const n=this.maskDataPool.pop()||new hh;n.pooled=!0,n.maskObject=e,s=n}const i=this.maskStack.length!==0?this.maskStack[this.maskStack.length-1]:null;if(s.copyCountersOrReset(i),s._colorMask=i?i._colorMask:15,s.autoDetect&&this.detect(s),s._target=t,s.type!==pt.SPRITE&&this.maskStack.push(s),s.enabled)switch(s.type){case pt.SCISSOR:this.renderer.scissor.push(s);break;case pt.STENCIL:this.renderer.stencil.push(s);break;case pt.SPRITE:s.copyCountersOrReset(null),this.pushSpriteMask(s);break;case pt.COLOR:this.pushColorMask(s);break;default:break}s.type===pt.SPRITE&&this.maskStack.push(s)}pop(t){const e=this.maskStack.pop();if(!(!e||e._target!==t)){if(e.enabled)switch(e.type){case pt.SCISSOR:this.renderer.scissor.pop(e);break;case pt.STENCIL:this.renderer.stencil.pop(e.maskObject);break;case pt.SPRITE:this.popSpriteMask(e);break;case pt.COLOR:this.popColorMask(e);break;default:break}if(e.reset(),e.pooled&&this.maskDataPool.push(e),this.maskStack.length!==0){const s=this.maskStack[this.maskStack.length-1];s.type===pt.SPRITE&&s._filters&&(s._filters[0].maskSprite=s.maskObject)}}}detect(t){const e=t.maskObject;e?e.isSprite?t.type=pt.SPRITE:this.enableScissor&&this.renderer.scissor.testScissor(t)?t.type=pt.SCISSOR:t.type=pt.STENCIL:t.type=pt.COLOR}pushSpriteMask(t){const{maskObject:e}=t,s=t._target;let i=t._filters;i||(i=this.alphaMaskPool[this.alphaMaskIndex],i||(i=this.alphaMaskPool[this.alphaMaskIndex]=[new oh])),i[0].resolution=t.resolution,i[0].multisample=t.multisample,i[0].maskSprite=e;const n=s.filterArea;s.filterArea=e.getBounds(!0),this.renderer.filter.push(s,i),s.filterArea=n,t._filters||this.alphaMaskIndex++}popSpriteMask(t){this.renderer.filter.pop(),t._filters?t._filters[0].maskSprite=null:(this.alphaMaskIndex--,this.alphaMaskPool[this.alphaMaskIndex][0].maskSprite=null)}pushColorMask(t){const e=t._colorMask,s=t._colorMask=e&t.colorMask;s!==e&&this.renderer.gl.colorMask((s&1)!==0,(s&2)!==0,(s&4)!==0,(s&8)!==0)}popColorMask(t){const e=t._colorMask,s=this.maskStack.length>0?this.maskStack[this.maskStack.length-1]._colorMask:15;s!==e&&this.renderer.gl.colorMask((s&1)!==0,(s&2)!==0,(s&4)!==0,(s&8)!==0)}destroy(){this.renderer=null}}bn.extension={type:D.RendererSystem,name:"mask"},U.add(bn);class lh{constructor(t){this.renderer=t,this.maskStack=[],this.glConst=0}getStackLength(){return this.maskStack.length}setMaskStack(t){const{gl:e}=this.renderer,s=this.getStackLength();this.maskStack=t;const i=this.getStackLength();i!==s&&(i===0?e.disable(this.glConst):(e.enable(this.glConst),this._useCurrent()))}_useCurrent(){}destroy(){this.renderer=null,this.maskStack=null}}const uh=new tt,ch=[],dh=class Ti extends lh{constructor(t){super(t),this.glConst=N.ADAPTER.getWebGLRenderingContext().SCISSOR_TEST}getStackLength(){const t=this.maskStack[this.maskStack.length-1];return t?t._scissorCounter:0}calcScissorRect(t){var e;if(t._scissorRectLocal)return;const s=t._scissorRect,{maskObject:i}=t,{renderer:n}=this,a=n.renderTexture,o=i.getBounds(!0,(e=ch.pop())!=null?e:new z);this.roundFrameToPixels(o,a.current?a.current.resolution:n.resolution,a.sourceFrame,a.destinationFrame,n.projection.transform),s&&o.fit(s),t._scissorRectLocal=o}static isMatrixRotated(t){if(!t)return!1;const{a:e,b:s,c:i,d:n}=t;return(Math.abs(s)>1e-4||Math.abs(i)>1e-4)&&(Math.abs(e)>1e-4||Math.abs(n)>1e-4)}testScissor(t){const{maskObject:e}=t;if(!e.isFastRect||!e.isFastRect()||Ti.isMatrixRotated(e.worldTransform)||Ti.isMatrixRotated(this.renderer.projection.transform))return!1;this.calcScissorRect(t);const s=t._scissorRectLocal;return s.width>0&&s.height>0}roundFrameToPixels(t,e,s,i,n){Ti.isMatrixRotated(n)||(n=n?uh.copyFrom(n):uh.identity(),n.translate(-s.x,-s.y).scale(i.width/s.width,i.height/s.height).translate(i.x,i.y),this.renderer.filter.transformAABB(n,t),t.fit(i),t.x=Math.round(t.x*e),t.y=Math.round(t.y*e),t.width=Math.round(t.width*e),t.height=Math.round(t.height*e))}push(t){t._scissorRectLocal||this.calcScissorRect(t);const{gl:e}=this.renderer;t._scissorRect||e.enable(e.SCISSOR_TEST),t._scissorCounter++,t._scissorRect=t._scissorRectLocal,this._useCurrent()}pop(t){const{gl:e}=this.renderer;t&&ch.push(t._scissorRectLocal),this.getStackLength()>0?this._useCurrent():e.disable(e.SCISSOR_TEST)}_useCurrent(){const t=this.maskStack[this.maskStack.length-1]._scissorRect;let e;this.renderer.renderTexture.current?e=t.y:e=this.renderer.height-t.height-t.y,this.renderer.gl.scissor(t.x,e,t.width,t.height)}};dh.extension={type:D.RendererSystem,name:"scissor"};let fh=dh;U.add(fh);class Tn extends lh{constructor(t){super(t),this.glConst=N.ADAPTER.getWebGLRenderingContext().STENCIL_TEST}getStackLength(){const t=this.maskStack[this.maskStack.length-1];return t?t._stencilCounter:0}push(t){const e=t.maskObject,{gl:s}=this.renderer,i=t._stencilCounter;i===0&&(this.renderer.framebuffer.forceStencil(),s.clearStencil(0),s.clear(s.STENCIL_BUFFER_BIT),s.enable(s.STENCIL_TEST)),t._stencilCounter++;const n=t._colorMask;n!==0&&(t._colorMask=0,s.colorMask(!1,!1,!1,!1)),s.stencilFunc(s.EQUAL,i,4294967295),s.stencilOp(s.KEEP,s.KEEP,s.INCR),e.renderable=!0,e.render(this.renderer),this.renderer.batch.flush(),e.renderable=!1,n!==0&&(t._colorMask=n,s.colorMask((n&1)!==0,(n&2)!==0,(n&4)!==0,(n&8)!==0)),this._useCurrent()}pop(t){const e=this.renderer.gl;if(this.getStackLength()===0)e.disable(e.STENCIL_TEST);else{const s=this.maskStack.length!==0?this.maskStack[this.maskStack.length-1]:null,i=s?s._colorMask:15;i!==0&&(s._colorMask=0,e.colorMask(!1,!1,!1,!1)),e.stencilOp(e.KEEP,e.KEEP,e.DECR),t.renderable=!0,t.render(this.renderer),this.renderer.batch.flush(),t.renderable=!1,i!==0&&(s._colorMask=i,e.colorMask((i&1)!==0,(i&2)!==0,(i&4)!==0,(i&8)!==0)),this._useCurrent()}}_useCurrent(){const t=this.renderer.gl;t.stencilFunc(t.EQUAL,this.getStackLength(),4294967295),t.stencilOp(t.KEEP,t.KEEP,t.KEEP)}}Tn.extension={type:D.RendererSystem,name:"stencil"},U.add(Tn);class En{constructor(t){this.renderer=t,this.plugins={}}init(){const t=this.rendererPlugins;for(const e in t)this.plugins[e]=new t[e](this.renderer)}destroy(){for(const t in this.plugins)this.plugins[t].destroy(),this.plugins[t]=null}}En.extension={type:[D.RendererSystem,D.CanvasRendererSystem],name:"_plugin"},U.add(En);class An{constructor(t){this.renderer=t,this.destinationFrame=null,this.sourceFrame=null,this.defaultFrame=null,this.projectionMatrix=new tt,this.transform=null}update(t,e,s,i){this.destinationFrame=t||this.destinationFrame||this.defaultFrame,this.sourceFrame=e||this.sourceFrame||t,this.calculateProjection(this.destinationFrame,this.sourceFrame,s,i),this.transform&&this.projectionMatrix.append(this.transform);const n=this.renderer;n.globalUniforms.uniforms.projectionMatrix=this.projectionMatrix,n.globalUniforms.update(),n.shader.shader&&n.shader.syncUniformGroup(n.shader.shader.uniforms.globals)}calculateProjection(t,e,s,i){const n=this.projectionMatrix,a=i?-1:1;n.identity(),n.a=1/e.width*2,n.d=a*(1/e.height*2),n.tx=-1-e.x*n.a,n.ty=-a-e.y*n.d}setTransform(t){}destroy(){this.renderer=null}}An.extension={type:D.RendererSystem,name:"projection"},U.add(An);var ph=Object.getOwnPropertySymbols,Hd=Object.prototype.hasOwnProperty,Vd=Object.prototype.propertyIsEnumerable,jd=(r,t)=>{var e={};for(var s in r)Hd.call(r,s)&&t.indexOf(s)<0&&(e[s]=r[s]);if(r!=null&&ph)for(var s of ph(r))t.indexOf(s)<0&&Vd.call(r,s)&&(e[s]=r[s]);return e};const Xd=new Dr,mh=new z;class wn{constructor(t){this.renderer=t,this._tempMatrix=new tt}generateTexture(t,e){var s;const i=e||{},{region:n}=i,a=jd(i,["region"]),o=(n==null?void 0:n.copyTo(mh))||t.getLocalBounds(mh,!0),h=a.resolution||this.renderer.resolution;o.width=Math.max(o.width,1/h),o.height=Math.max(o.height,1/h),a.width=o.width,a.height=o.height,a.resolution=h,(s=a.multisample)!=null||(a.multisample=this.renderer.multisample);const l=Yt.create(a);this._tempMatrix.tx=-o.x,this._tempMatrix.ty=-o.y;const u=t.transform;return t.transform=Xd,this.renderer.render(t,{renderTexture:l,transform:this._tempMatrix,skipUpdateTransform:!!t.parent,blit:!0}),t.transform=u,l}destroy(){}}wn.extension={type:[D.RendererSystem,D.CanvasRendererSystem],name:"textureGenerator"},U.add(wn);const Ge=new z,Ms=new z;class Sn{constructor(t){this.renderer=t,this.defaultMaskStack=[],this.current=null,this.sourceFrame=new z,this.destinationFrame=new z,this.viewportFrame=new z}contextChange(){var t;const e=(t=this.renderer)==null?void 0:t.gl.getContextAttributes();this._rendererPremultipliedAlpha=!!(e&&e.alpha&&e.premultipliedAlpha)}bind(t=null,e,s){const i=this.renderer;this.current=t;let n,a,o;t?(n=t.baseTexture,o=n.resolution,e||(Ge.width=t.frame.width,Ge.height=t.frame.height,e=Ge),s||(Ms.x=t.frame.x,Ms.y=t.frame.y,Ms.width=e.width,Ms.height=e.height,s=Ms),a=n.framebuffer):(o=i.resolution,e||(Ge.width=i._view.screen.width,Ge.height=i._view.screen.height,e=Ge),s||(s=Ge,s.width=e.width,s.height=e.height));const h=this.viewportFrame;h.x=s.x*o,h.y=s.y*o,h.width=s.width*o,h.height=s.height*o,t||(h.y=i.view.height-(h.y+h.height)),h.ceil(),this.renderer.framebuffer.bind(a,h),this.renderer.projection.update(s,e,o,!a),t?this.renderer.mask.setMaskStack(n.maskStack):this.renderer.mask.setMaskStack(this.defaultMaskStack),this.sourceFrame.copyFrom(e),this.destinationFrame.copyFrom(s)}clear(t,e){const s=this.current?this.current.baseTexture.clear:this.renderer.background.backgroundColor,i=Y.shared.setValue(t||s);(this.current&&this.current.baseTexture.alphaMode>0||!this.current&&this._rendererPremultipliedAlpha)&&i.premultiply(i.alpha);const n=this.destinationFrame,a=this.current?this.current.baseTexture:this.renderer._view.screen,o=n.width!==a.width||n.height!==a.height;if(o){let{x:h,y:l,width:u,height:c}=this.viewportFrame;h=Math.round(h),l=Math.round(l),u=Math.round(u),c=Math.round(c),this.renderer.gl.enable(this.renderer.gl.SCISSOR_TEST),this.renderer.gl.scissor(h,l,u,c)}this.renderer.framebuffer.clear(i.red,i.green,i.blue,i.alpha,e),o&&this.renderer.scissor.pop()}resize(){this.bind(null)}reset(){this.bind(null)}destroy(){this.renderer=null}}Sn.extension={type:D.RendererSystem,name:"renderTexture"},U.add(Sn);class zd{}class gh{constructor(t,e){this.program=t,this.uniformData=e,this.uniformGroups={},this.uniformDirtyGroups={},this.uniformBufferBindings={}}destroy(){this.uniformData=null,this.uniformGroups=null,this.uniformDirtyGroups=null,this.uniformBufferBindings=null,this.program=null}}function Wd(r,t){const e={},s=t.getProgramParameter(r,t.ACTIVE_ATTRIBUTES);for(let i=0;i<s;i++){const n=t.getActiveAttrib(r,i);if(n.name.startsWith("gl_"))continue;const a=Zo(t,n.type),o={type:a,name:n.name,size:qo(a),location:t.getAttribLocation(r,n.name)};e[n.name]=o}return e}function Yd(r,t){const e={},s=t.getProgramParameter(r,t.ACTIVE_UNIFORMS);for(let i=0;i<s;i++){const n=t.getActiveUniform(r,i),a=n.name.replace(/\[.*?\]$/,""),o=!!n.name.match(/\[.*?\]$/),h=Zo(t,n.type);e[a]={name:a,index:i,type:h,size:n.size,isArray:o,value:Xo(h,n.size)}}return e}function _h(r,t){var e;const s=jo(r,r.VERTEX_SHADER,t.vertexSrc),i=jo(r,r.FRAGMENT_SHADER,t.fragmentSrc),n=r.createProgram();r.attachShader(n,s),r.attachShader(n,i);const a=(e=t.extra)==null?void 0:e.transformFeedbackVaryings;if(a&&(typeof r.transformFeedbackVaryings!="function"||r.transformFeedbackVaryings(n,a.names,a.bufferMode==="separate"?r.SEPARATE_ATTRIBS:r.INTERLEAVED_ATTRIBS)),r.linkProgram(n),r.getProgramParameter(n,r.LINK_STATUS)||Id(r,n,s,i),t.attributeData=Wd(n,r),t.uniformData=Yd(n,r),!/^[ \t]*#[ \t]*version[ \t]+300[ \t]+es[ \t]*$/m.test(t.vertexSrc)){const h=Object.keys(t.attributeData);h.sort((l,u)=>l>u?1:-1);for(let l=0;l<h.length;l++)t.attributeData[h[l]].location=l,r.bindAttribLocation(n,l,h[l]);r.linkProgram(n)}r.deleteShader(s),r.deleteShader(i);const o={};for(const h in t.uniformData){const l=t.uniformData[h];o[h]={location:r.getUniformLocation(n,h),value:Xo(l.type,l.size)}}return new gh(n,o)}function qd(r,t,e,s,i){e.buffer.update(i)}const Kd={float:`
        data[offset] = v;
    `,vec2:`
        data[offset] = v[0];
        data[offset+1] = v[1];
    `,vec3:`
        data[offset] = v[0];
        data[offset+1] = v[1];
        data[offset+2] = v[2];

    `,vec4:`
        data[offset] = v[0];
        data[offset+1] = v[1];
        data[offset+2] = v[2];
        data[offset+3] = v[3];
    `,mat2:`
        data[offset] = v[0];
        data[offset+1] = v[1];

        data[offset+4] = v[2];
        data[offset+5] = v[3];
    `,mat3:`
        data[offset] = v[0];
        data[offset+1] = v[1];
        data[offset+2] = v[2];

        data[offset + 4] = v[3];
        data[offset + 5] = v[4];
        data[offset + 6] = v[5];

        data[offset + 8] = v[6];
        data[offset + 9] = v[7];
        data[offset + 10] = v[8];
    `,mat4:`
        for(var i = 0; i < 16; i++)
        {
            data[offset + i] = v[i];
        }
    `},vh={float:4,vec2:8,vec3:12,vec4:16,int:4,ivec2:8,ivec3:12,ivec4:16,uint:4,uvec2:8,uvec3:12,uvec4:16,bool:4,bvec2:8,bvec3:12,bvec4:16,mat2:16*2,mat3:16*3,mat4:16*4};function yh(r){const t=r.map(n=>({data:n,offset:0,dataLen:0,dirty:0}));let e=0,s=0,i=0;for(let n=0;n<t.length;n++){const a=t[n];if(e=vh[a.data.type],a.data.size>1&&(e=Math.max(e,16)*a.data.size),a.dataLen=e,s%e!==0&&s<16){const o=s%e%16;s+=o,i+=o}s+e>16?(i=Math.ceil(i/16)*16,a.offset=i,i+=e,s=e):(a.offset=i,s+=e,i+=e)}return i=Math.ceil(i/16)*16,{uboElements:t,size:i}}function xh(r,t){const e=[];for(const s in r)t[s]&&e.push(t[s]);return e.sort((s,i)=>s.index-i.index),e}function bh(r,t){if(!r.autoManage)return{size:0,syncFunc:qd};const e=xh(r.uniforms,t),{uboElements:s,size:i}=yh(e),n=[`
    var v = null;
    var v2 = null;
    var cv = null;
    var t = 0;
    var gl = renderer.gl
    var index = 0;
    var data = buffer.data;
    `];for(let a=0;a<s.length;a++){const o=s[a],h=r.uniforms[o.data.name],l=o.data.name;let u=!1;for(let c=0;c<ke.length;c++){const d=ke[c];if(d.codeUbo&&d.test(o.data,h)){n.push(`offset = ${o.offset/4};`,ke[c].codeUbo(o.data.name,h)),u=!0;break}}if(!u)if(o.data.size>1){const c=qo(o.data.type),d=Math.max(vh[o.data.type]/16,1),f=c/d,p=(4-f%4)%4;n.push(`
                cv = ud.${l}.value;
                v = uv.${l};
                offset = ${o.offset/4};

                t = 0;

                for(var i=0; i < ${o.data.size*d}; i++)
                {
                    for(var j = 0; j < ${f}; j++)
                    {
                        data[offset++] = v[t++];
                    }
                    offset += ${p};
                }

                `)}else{const c=Kd[o.data.type];n.push(`
                cv = ud.${l}.value;
                v = uv.${l};
                offset = ${o.offset/4};
                ${c};
                `)}}return n.push(`
       renderer.buffer.update(buffer);
    `),{size:i,syncFunc:new Function("ud","uv","renderer","syncData","buffer",n.join(`
`))}}let Zd=0;const Hr={textureCount:0,uboCount:0};class Cn{constructor(t){this.destroyed=!1,this.renderer=t,this.systemCheck(),this.gl=null,this.shader=null,this.program=null,this.cache={},this._uboCache={},this.id=Zd++}systemCheck(){if(!Jo())throw new Error("Current environment does not allow unsafe-eval, please use @pixi/unsafe-eval module to enable support.")}contextChange(t){this.gl=t,this.reset()}bind(t,e){t.disposeRunner.add(this),t.uniforms.globals=this.renderer.globalUniforms;const s=t.program,i=s.glPrograms[this.renderer.CONTEXT_UID]||this.generateProgram(t);return this.shader=t,this.program!==s&&(this.program=s,this.gl.useProgram(i.program)),e||(Hr.textureCount=0,Hr.uboCount=0,this.syncUniformGroup(t.uniformGroup,Hr)),i}setUniforms(t){const e=this.shader.program,s=e.glPrograms[this.renderer.CONTEXT_UID];e.syncUniforms(s.uniformData,t,this.renderer)}syncUniformGroup(t,e){const s=this.getGlProgram();(!t.static||t.dirtyId!==s.uniformDirtyGroups[t.id])&&(s.uniformDirtyGroups[t.id]=t.dirtyId,this.syncUniforms(t,s,e))}syncUniforms(t,e,s){(t.syncUniforms[this.shader.program.id]||this.createSyncGroups(t))(e.uniformData,t.uniforms,this.renderer,s)}createSyncGroups(t){const e=this.getSignature(t,this.shader.program.uniformData,"u");return this.cache[e]||(this.cache[e]=Cd(t,this.shader.program.uniformData)),t.syncUniforms[this.shader.program.id]=this.cache[e],t.syncUniforms[this.shader.program.id]}syncUniformBufferGroup(t,e){const s=this.getGlProgram();if(!t.static||t.dirtyId!==0||!s.uniformGroups[t.id]){t.dirtyId=0;const i=s.uniformGroups[t.id]||this.createSyncBufferGroup(t,s,e);t.buffer.update(),i(s.uniformData,t.uniforms,this.renderer,Hr,t.buffer)}this.renderer.buffer.bindBufferBase(t.buffer,s.uniformBufferBindings[e])}createSyncBufferGroup(t,e,s){const{gl:i}=this.renderer;this.renderer.buffer.bind(t.buffer);const n=this.gl.getUniformBlockIndex(e.program,s);e.uniformBufferBindings[s]=this.shader.uniformBindCount,i.uniformBlockBinding(e.program,n,this.shader.uniformBindCount),this.shader.uniformBindCount++;const a=this.getSignature(t,this.shader.program.uniformData,"ubo");let o=this._uboCache[a];if(o||(o=this._uboCache[a]=bh(t,this.shader.program.uniformData)),t.autoManage){const h=new Float32Array(o.size/4);t.buffer.update(h)}return e.uniformGroups[t.id]=o.syncFunc,e.uniformGroups[t.id]}getSignature(t,e,s){const i=t.uniforms,n=[`${s}-`];for(const a in i)n.push(a),e[a]&&n.push(e[a].type);return n.join("-")}getGlProgram(){return this.shader?this.shader.program.glPrograms[this.renderer.CONTEXT_UID]:null}generateProgram(t){const e=this.gl,s=t.program,i=_h(e,s);return s.glPrograms[this.renderer.CONTEXT_UID]=i,i}reset(){this.program=null,this.shader=null}disposeShader(t){this.shader===t&&(this.shader=null)}destroy(){this.renderer=null,this.destroyed=!0}}Cn.extension={type:D.RendererSystem,name:"shader"},U.add(Cn);class Ds{constructor(t){this.renderer=t}run(t){const{renderer:e}=this;e.runners.init.emit(e.options),t.hello&&console.log(`PixiJS 7.4.2 - ${e.rendererLogId} - https://pixijs.com`),e.resize(e.screen.width,e.screen.height)}destroy(){}}Ds.defaultOptions={hello:!1},Ds.extension={type:[D.RendererSystem,D.CanvasRendererSystem],name:"startup"},U.add(Ds);function Qd(r,t=[]){return t[C.NORMAL]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.ADD]=[r.ONE,r.ONE],t[C.MULTIPLY]=[r.DST_COLOR,r.ONE_MINUS_SRC_ALPHA,r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.SCREEN]=[r.ONE,r.ONE_MINUS_SRC_COLOR,r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.OVERLAY]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.DARKEN]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.LIGHTEN]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.COLOR_DODGE]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.COLOR_BURN]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.HARD_LIGHT]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.SOFT_LIGHT]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.DIFFERENCE]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.EXCLUSION]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.HUE]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.SATURATION]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.COLOR]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.LUMINOSITY]=[r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.NONE]=[0,0],t[C.NORMAL_NPM]=[r.SRC_ALPHA,r.ONE_MINUS_SRC_ALPHA,r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.ADD_NPM]=[r.SRC_ALPHA,r.ONE,r.ONE,r.ONE],t[C.SCREEN_NPM]=[r.SRC_ALPHA,r.ONE_MINUS_SRC_COLOR,r.ONE,r.ONE_MINUS_SRC_ALPHA],t[C.SRC_IN]=[r.DST_ALPHA,r.ZERO],t[C.SRC_OUT]=[r.ONE_MINUS_DST_ALPHA,r.ZERO],t[C.SRC_ATOP]=[r.DST_ALPHA,r.ONE_MINUS_SRC_ALPHA],t[C.DST_OVER]=[r.ONE_MINUS_DST_ALPHA,r.ONE],t[C.DST_IN]=[r.ZERO,r.SRC_ALPHA],t[C.DST_OUT]=[r.ZERO,r.ONE_MINUS_SRC_ALPHA],t[C.DST_ATOP]=[r.ONE_MINUS_DST_ALPHA,r.SRC_ALPHA],t[C.XOR]=[r.ONE_MINUS_DST_ALPHA,r.ONE_MINUS_SRC_ALPHA],t[C.SUBTRACT]=[r.ONE,r.ONE,r.ONE,r.ONE,r.FUNC_REVERSE_SUBTRACT,r.FUNC_ADD],t}const Jd=0,tf=1,ef=2,sf=3,rf=4,nf=5,Th=class Aa{constructor(){this.gl=null,this.stateId=0,this.polygonOffset=0,this.blendMode=C.NONE,this._blendEq=!1,this.map=[],this.map[Jd]=this.setBlend,this.map[tf]=this.setOffset,this.map[ef]=this.setCullFace,this.map[sf]=this.setDepthTest,this.map[rf]=this.setFrontFace,this.map[nf]=this.setDepthMask,this.checks=[],this.defaultState=new ee,this.defaultState.blend=!0}contextChange(t){this.gl=t,this.blendModes=Qd(t),this.set(this.defaultState),this.reset()}set(t){if(t=t||this.defaultState,this.stateId!==t.data){let e=this.stateId^t.data,s=0;for(;e;)e&1&&this.map[s].call(this,!!(t.data&1<<s)),e=e>>1,s++;this.stateId=t.data}for(let e=0;e<this.checks.length;e++)this.checks[e](this,t)}forceState(t){t=t||this.defaultState;for(let e=0;e<this.map.length;e++)this.map[e].call(this,!!(t.data&1<<e));for(let e=0;e<this.checks.length;e++)this.checks[e](this,t);this.stateId=t.data}setBlend(t){this.updateCheck(Aa.checkBlendMode,t),this.gl[t?"enable":"disable"](this.gl.BLEND)}setOffset(t){this.updateCheck(Aa.checkPolygonOffset,t),this.gl[t?"enable":"disable"](this.gl.POLYGON_OFFSET_FILL)}setDepthTest(t){this.gl[t?"enable":"disable"](this.gl.DEPTH_TEST)}setDepthMask(t){this.gl.depthMask(t)}setCullFace(t){this.gl[t?"enable":"disable"](this.gl.CULL_FACE)}setFrontFace(t){this.gl.frontFace(this.gl[t?"CW":"CCW"])}setBlendMode(t){if(t===this.blendMode)return;this.blendMode=t;const e=this.blendModes[t],s=this.gl;e.length===2?s.blendFunc(e[0],e[1]):s.blendFuncSeparate(e[0],e[1],e[2],e[3]),e.length===6?(this._blendEq=!0,s.blendEquationSeparate(e[4],e[5])):this._blendEq&&(this._blendEq=!1,s.blendEquationSeparate(s.FUNC_ADD,s.FUNC_ADD))}setPolygonOffset(t,e){this.gl.polygonOffset(t,e)}reset(){this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL,!1),this.forceState(this.defaultState),this._blendEq=!0,this.blendMode=-1,this.setBlendMode(0)}updateCheck(t,e){const s=this.checks.indexOf(t);e&&s===-1?this.checks.push(t):!e&&s!==-1&&this.checks.splice(s,1)}static checkBlendMode(t,e){t.setBlendMode(e.blendMode)}static checkPolygonOffset(t,e){t.setPolygonOffset(1,e.polygonOffset)}destroy(){this.gl=null}};Th.extension={type:D.RendererSystem,name:"state"};let Eh=Th;U.add(Eh);class Rn extends Ye{constructor(){super(...arguments),this.runners={},this._systemsHash={}}setup(t){var e;this.addRunners(...t.runners);const s=((e=t.priority)!=null?e:[]).filter(n=>t.systems[n]),i=[...s,...Object.keys(t.systems).filter(n=>!s.includes(n))];for(const n of i)this.addSystem(t.systems[n],n)}addRunners(...t){t.forEach(e=>{this.runners[e]=new Pt(e)})}addSystem(t,e){const s=new t(this);if(this[e])throw new Error(`Whoops! The name "${e}" is already in use`);this[e]=s,this._systemsHash[e]=s;for(const i in this.runners)this.runners[i].add(s);return this}emitWithCustomOptions(t,e){const s=Object.keys(this._systemsHash);t.items.forEach(i=>{const n=s.find(a=>this._systemsHash[a]===i);i[t.name](e[n])})}destroy(){Object.values(this.runners).forEach(t=>{t.destroy()}),this._systemsHash={}}}const Os=class Ei{constructor(t){this.renderer=t,this.count=0,this.checkCount=0,this.maxIdle=Ei.defaultMaxIdle,this.checkCountMax=Ei.defaultCheckCountMax,this.mode=Ei.defaultMode}postrender(){this.renderer.objectRenderer.renderingToScreen&&(this.count++,this.mode!==fr.MANUAL&&(this.checkCount++,this.checkCount>this.checkCountMax&&(this.checkCount=0,this.run())))}run(){const t=this.renderer.texture,e=t.managedTextures;let s=!1;for(let i=0;i<e.length;i++){const n=e[i];n.resource&&this.count-n.touched>this.maxIdle&&(t.destroyTexture(n,!0),e[i]=null,s=!0)}if(s){let i=0;for(let n=0;n<e.length;n++)e[n]!==null&&(e[i++]=e[n]);e.length=i}}unload(t){const e=this.renderer.texture,s=t._texture;s&&!s.framebuffer&&e.destroyTexture(s);for(let i=t.children.length-1;i>=0;i--)this.unload(t.children[i])}destroy(){this.renderer=null}};Os.defaultMode=fr.AUTO,Os.defaultMaxIdle=3600,Os.defaultCheckCountMax=600,Os.extension={type:D.RendererSystem,name:"textureGC"};let Ae=Os;U.add(Ae);class Vr{constructor(t){this.texture=t,this.width=-1,this.height=-1,this.dirtyId=-1,this.dirtyStyleId=-1,this.mipmap=!1,this.wrapMode=33071,this.type=$.UNSIGNED_BYTE,this.internalFormat=P.RGBA,this.samplerType=0}}function af(r){let t;return"WebGL2RenderingContext"in globalThis&&r instanceof globalThis.WebGL2RenderingContext?t={[r.RGB]:k.FLOAT,[r.RGBA]:k.FLOAT,[r.ALPHA]:k.FLOAT,[r.LUMINANCE]:k.FLOAT,[r.LUMINANCE_ALPHA]:k.FLOAT,[r.R8]:k.FLOAT,[r.R8_SNORM]:k.FLOAT,[r.RG8]:k.FLOAT,[r.RG8_SNORM]:k.FLOAT,[r.RGB8]:k.FLOAT,[r.RGB8_SNORM]:k.FLOAT,[r.RGB565]:k.FLOAT,[r.RGBA4]:k.FLOAT,[r.RGB5_A1]:k.FLOAT,[r.RGBA8]:k.FLOAT,[r.RGBA8_SNORM]:k.FLOAT,[r.RGB10_A2]:k.FLOAT,[r.RGB10_A2UI]:k.FLOAT,[r.SRGB8]:k.FLOAT,[r.SRGB8_ALPHA8]:k.FLOAT,[r.R16F]:k.FLOAT,[r.RG16F]:k.FLOAT,[r.RGB16F]:k.FLOAT,[r.RGBA16F]:k.FLOAT,[r.R32F]:k.FLOAT,[r.RG32F]:k.FLOAT,[r.RGB32F]:k.FLOAT,[r.RGBA32F]:k.FLOAT,[r.R11F_G11F_B10F]:k.FLOAT,[r.RGB9_E5]:k.FLOAT,[r.R8I]:k.INT,[r.R8UI]:k.UINT,[r.R16I]:k.INT,[r.R16UI]:k.UINT,[r.R32I]:k.INT,[r.R32UI]:k.UINT,[r.RG8I]:k.INT,[r.RG8UI]:k.UINT,[r.RG16I]:k.INT,[r.RG16UI]:k.UINT,[r.RG32I]:k.INT,[r.RG32UI]:k.UINT,[r.RGB8I]:k.INT,[r.RGB8UI]:k.UINT,[r.RGB16I]:k.INT,[r.RGB16UI]:k.UINT,[r.RGB32I]:k.INT,[r.RGB32UI]:k.UINT,[r.RGBA8I]:k.INT,[r.RGBA8UI]:k.UINT,[r.RGBA16I]:k.INT,[r.RGBA16UI]:k.UINT,[r.RGBA32I]:k.INT,[r.RGBA32UI]:k.UINT,[r.DEPTH_COMPONENT16]:k.FLOAT,[r.DEPTH_COMPONENT24]:k.FLOAT,[r.DEPTH_COMPONENT32F]:k.FLOAT,[r.DEPTH_STENCIL]:k.FLOAT,[r.DEPTH24_STENCIL8]:k.FLOAT,[r.DEPTH32F_STENCIL8]:k.FLOAT}:t={[r.RGB]:k.FLOAT,[r.RGBA]:k.FLOAT,[r.ALPHA]:k.FLOAT,[r.LUMINANCE]:k.FLOAT,[r.LUMINANCE_ALPHA]:k.FLOAT,[r.DEPTH_STENCIL]:k.FLOAT},t}function of(r){let t;return"WebGL2RenderingContext"in globalThis&&r instanceof globalThis.WebGL2RenderingContext?t={[$.UNSIGNED_BYTE]:{[P.RGBA]:r.RGBA8,[P.RGB]:r.RGB8,[P.RG]:r.RG8,[P.RED]:r.R8,[P.RGBA_INTEGER]:r.RGBA8UI,[P.RGB_INTEGER]:r.RGB8UI,[P.RG_INTEGER]:r.RG8UI,[P.RED_INTEGER]:r.R8UI,[P.ALPHA]:r.ALPHA,[P.LUMINANCE]:r.LUMINANCE,[P.LUMINANCE_ALPHA]:r.LUMINANCE_ALPHA},[$.BYTE]:{[P.RGBA]:r.RGBA8_SNORM,[P.RGB]:r.RGB8_SNORM,[P.RG]:r.RG8_SNORM,[P.RED]:r.R8_SNORM,[P.RGBA_INTEGER]:r.RGBA8I,[P.RGB_INTEGER]:r.RGB8I,[P.RG_INTEGER]:r.RG8I,[P.RED_INTEGER]:r.R8I},[$.UNSIGNED_SHORT]:{[P.RGBA_INTEGER]:r.RGBA16UI,[P.RGB_INTEGER]:r.RGB16UI,[P.RG_INTEGER]:r.RG16UI,[P.RED_INTEGER]:r.R16UI,[P.DEPTH_COMPONENT]:r.DEPTH_COMPONENT16},[$.SHORT]:{[P.RGBA_INTEGER]:r.RGBA16I,[P.RGB_INTEGER]:r.RGB16I,[P.RG_INTEGER]:r.RG16I,[P.RED_INTEGER]:r.R16I},[$.UNSIGNED_INT]:{[P.RGBA_INTEGER]:r.RGBA32UI,[P.RGB_INTEGER]:r.RGB32UI,[P.RG_INTEGER]:r.RG32UI,[P.RED_INTEGER]:r.R32UI,[P.DEPTH_COMPONENT]:r.DEPTH_COMPONENT24},[$.INT]:{[P.RGBA_INTEGER]:r.RGBA32I,[P.RGB_INTEGER]:r.RGB32I,[P.RG_INTEGER]:r.RG32I,[P.RED_INTEGER]:r.R32I},[$.FLOAT]:{[P.RGBA]:r.RGBA32F,[P.RGB]:r.RGB32F,[P.RG]:r.RG32F,[P.RED]:r.R32F,[P.DEPTH_COMPONENT]:r.DEPTH_COMPONENT32F},[$.HALF_FLOAT]:{[P.RGBA]:r.RGBA16F,[P.RGB]:r.RGB16F,[P.RG]:r.RG16F,[P.RED]:r.R16F},[$.UNSIGNED_SHORT_5_6_5]:{[P.RGB]:r.RGB565},[$.UNSIGNED_SHORT_4_4_4_4]:{[P.RGBA]:r.RGBA4},[$.UNSIGNED_SHORT_5_5_5_1]:{[P.RGBA]:r.RGB5_A1},[$.UNSIGNED_INT_2_10_10_10_REV]:{[P.RGBA]:r.RGB10_A2,[P.RGBA_INTEGER]:r.RGB10_A2UI},[$.UNSIGNED_INT_10F_11F_11F_REV]:{[P.RGB]:r.R11F_G11F_B10F},[$.UNSIGNED_INT_5_9_9_9_REV]:{[P.RGB]:r.RGB9_E5},[$.UNSIGNED_INT_24_8]:{[P.DEPTH_STENCIL]:r.DEPTH24_STENCIL8},[$.FLOAT_32_UNSIGNED_INT_24_8_REV]:{[P.DEPTH_STENCIL]:r.DEPTH32F_STENCIL8}}:t={[$.UNSIGNED_BYTE]:{[P.RGBA]:r.RGBA,[P.RGB]:r.RGB,[P.ALPHA]:r.ALPHA,[P.LUMINANCE]:r.LUMINANCE,[P.LUMINANCE_ALPHA]:r.LUMINANCE_ALPHA},[$.UNSIGNED_SHORT_5_6_5]:{[P.RGB]:r.RGB},[$.UNSIGNED_SHORT_4_4_4_4]:{[P.RGBA]:r.RGBA},[$.UNSIGNED_SHORT_5_5_5_1]:{[P.RGBA]:r.RGBA}},t}class In{constructor(t){this.renderer=t,this.boundTextures=[],this.currentLocation=-1,this.managedTextures=[],this._unknownBoundTextures=!1,this.unknownTexture=new X,this.hasIntegerTextures=!1}contextChange(){const t=this.gl=this.renderer.gl;this.CONTEXT_UID=this.renderer.CONTEXT_UID,this.webGLVersion=this.renderer.context.webGLVersion,this.internalFormats=of(t),this.samplerTypes=af(t);const e=t.getParameter(t.MAX_TEXTURE_IMAGE_UNITS);this.boundTextures.length=e;for(let i=0;i<e;i++)this.boundTextures[i]=null;this.emptyTextures={};const s=new Vr(t.createTexture());t.bindTexture(t.TEXTURE_2D,s.texture),t.texImage2D(t.TEXTURE_2D,0,t.RGBA,1,1,0,t.RGBA,t.UNSIGNED_BYTE,new Uint8Array(4)),this.emptyTextures[t.TEXTURE_2D]=s,this.emptyTextures[t.TEXTURE_CUBE_MAP]=new Vr(t.createTexture()),t.bindTexture(t.TEXTURE_CUBE_MAP,this.emptyTextures[t.TEXTURE_CUBE_MAP].texture);for(let i=0;i<6;i++)t.texImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+i,0,t.RGBA,1,1,0,t.RGBA,t.UNSIGNED_BYTE,null);t.texParameteri(t.TEXTURE_CUBE_MAP,t.TEXTURE_MAG_FILTER,t.LINEAR),t.texParameteri(t.TEXTURE_CUBE_MAP,t.TEXTURE_MIN_FILTER,t.LINEAR);for(let i=0;i<this.boundTextures.length;i++)this.bind(null,i)}bind(t,e=0){const{gl:s}=this;if(t=t==null?void 0:t.castToBaseTexture(),t!=null&&t.valid&&!t.parentTextureArray){t.touched=this.renderer.textureGC.count;const i=t._glTextures[this.CONTEXT_UID]||this.initTexture(t);this.boundTextures[e]!==t&&(this.currentLocation!==e&&(this.currentLocation=e,s.activeTexture(s.TEXTURE0+e)),s.bindTexture(t.target,i.texture)),i.dirtyId!==t.dirtyId?(this.currentLocation!==e&&(this.currentLocation=e,s.activeTexture(s.TEXTURE0+e)),this.updateTexture(t)):i.dirtyStyleId!==t.dirtyStyleId&&this.updateTextureStyle(t),this.boundTextures[e]=t}else this.currentLocation!==e&&(this.currentLocation=e,s.activeTexture(s.TEXTURE0+e)),s.bindTexture(s.TEXTURE_2D,this.emptyTextures[s.TEXTURE_2D].texture),this.boundTextures[e]=null}reset(){this._unknownBoundTextures=!0,this.hasIntegerTextures=!1,this.currentLocation=-1;for(let t=0;t<this.boundTextures.length;t++)this.boundTextures[t]=this.unknownTexture}unbind(t){const{gl:e,boundTextures:s}=this;if(this._unknownBoundTextures){this._unknownBoundTextures=!1;for(let i=0;i<s.length;i++)s[i]===this.unknownTexture&&this.bind(null,i)}for(let i=0;i<s.length;i++)s[i]===t&&(this.currentLocation!==i&&(e.activeTexture(e.TEXTURE0+i),this.currentLocation=i),e.bindTexture(t.target,this.emptyTextures[t.target].texture),s[i]=null)}ensureSamplerType(t){const{boundTextures:e,hasIntegerTextures:s,CONTEXT_UID:i}=this;if(s)for(let n=t-1;n>=0;--n){const a=e[n];a&&a._glTextures[i].samplerType!==k.FLOAT&&this.renderer.texture.unbind(a)}}initTexture(t){const e=new Vr(this.gl.createTexture());return e.dirtyId=-1,t._glTextures[this.CONTEXT_UID]=e,this.managedTextures.push(t),t.on("dispose",this.destroyTexture,this),e}initTextureType(t,e){var s,i,n;e.internalFormat=(i=(s=this.internalFormats[t.type])==null?void 0:s[t.format])!=null?i:t.format,e.samplerType=(n=this.samplerTypes[e.internalFormat])!=null?n:k.FLOAT,this.webGLVersion===2&&t.type===$.HALF_FLOAT?e.type=this.gl.HALF_FLOAT:e.type=t.type}updateTexture(t){var e;const s=t._glTextures[this.CONTEXT_UID];if(!s)return;const i=this.renderer;if(this.initTextureType(t,s),(e=t.resource)!=null&&e.upload(i,t,s))s.samplerType!==k.FLOAT&&(this.hasIntegerTextures=!0);else{const n=t.realWidth,a=t.realHeight,o=i.gl;(s.width!==n||s.height!==a||s.dirtyId<0)&&(s.width=n,s.height=a,o.texImage2D(t.target,0,s.internalFormat,n,a,0,t.format,s.type,null))}t.dirtyStyleId!==s.dirtyStyleId&&this.updateTextureStyle(t),s.dirtyId=t.dirtyId}destroyTexture(t,e){const{gl:s}=this;if(t=t.castToBaseTexture(),t._glTextures[this.CONTEXT_UID]&&(this.unbind(t),s.deleteTexture(t._glTextures[this.CONTEXT_UID].texture),t.off("dispose",this.destroyTexture,this),delete t._glTextures[this.CONTEXT_UID],!e)){const i=this.managedTextures.indexOf(t);i!==-1&&Oe(this.managedTextures,i,1)}}updateTextureStyle(t){var e;const s=t._glTextures[this.CONTEXT_UID];s&&((t.mipmap===Ht.POW2||this.webGLVersion!==2)&&!t.isPowerOfTwo?s.mipmap=!1:s.mipmap=t.mipmap>=1,this.webGLVersion!==2&&!t.isPowerOfTwo?s.wrapMode=Zt.CLAMP:s.wrapMode=t.wrapMode,(e=t.resource)!=null&&e.style(this.renderer,t,s)||this.setStyle(t,s),s.dirtyStyleId=t.dirtyStyleId)}setStyle(t,e){const s=this.gl;if(e.mipmap&&t.mipmap!==Ht.ON_MANUAL&&s.generateMipmap(t.target),s.texParameteri(t.target,s.TEXTURE_WRAP_S,e.wrapMode),s.texParameteri(t.target,s.TEXTURE_WRAP_T,e.wrapMode),e.mipmap){s.texParameteri(t.target,s.TEXTURE_MIN_FILTER,t.scaleMode===Bt.LINEAR?s.LINEAR_MIPMAP_LINEAR:s.NEAREST_MIPMAP_NEAREST);const i=this.renderer.context.extensions.anisotropicFiltering;if(i&&t.anisotropicLevel>0&&t.scaleMode===Bt.LINEAR){const n=Math.min(t.anisotropicLevel,s.getParameter(i.MAX_TEXTURE_MAX_ANISOTROPY_EXT));s.texParameterf(t.target,i.TEXTURE_MAX_ANISOTROPY_EXT,n)}}else s.texParameteri(t.target,s.TEXTURE_MIN_FILTER,t.scaleMode===Bt.LINEAR?s.LINEAR:s.NEAREST);s.texParameteri(t.target,s.TEXTURE_MAG_FILTER,t.scaleMode===Bt.LINEAR?s.LINEAR:s.NEAREST)}destroy(){this.renderer=null}}In.extension={type:D.RendererSystem,name:"texture"},U.add(In);class Pn{constructor(t){this.renderer=t}contextChange(){this.gl=this.renderer.gl,this.CONTEXT_UID=this.renderer.CONTEXT_UID}bind(t){const{gl:e,CONTEXT_UID:s}=this,i=t._glTransformFeedbacks[s]||this.createGLTransformFeedback(t);e.bindTransformFeedback(e.TRANSFORM_FEEDBACK,i)}unbind(){const{gl:t}=this;t.bindTransformFeedback(t.TRANSFORM_FEEDBACK,null)}beginTransformFeedback(t,e){const{gl:s,renderer:i}=this;e&&i.shader.bind(e),s.beginTransformFeedback(t)}endTransformFeedback(){const{gl:t}=this;t.endTransformFeedback()}createGLTransformFeedback(t){const{gl:e,renderer:s,CONTEXT_UID:i}=this,n=e.createTransformFeedback();t._glTransformFeedbacks[i]=n,e.bindTransformFeedback(e.TRANSFORM_FEEDBACK,n);for(let a=0;a<t.buffers.length;a++){const o=t.buffers[a];o&&(s.buffer.update(o),o._glBuffers[i].refCount++,e.bindBufferBase(e.TRANSFORM_FEEDBACK_BUFFER,a,o._glBuffers[i].buffer||null))}return e.bindTransformFeedback(e.TRANSFORM_FEEDBACK,null),t.disposeRunner.add(this),n}disposeTransformFeedback(t,e){const s=t._glTransformFeedbacks[this.CONTEXT_UID],i=this.gl;t.disposeRunner.remove(this);const n=this.renderer.buffer;if(n)for(let a=0;a<t.buffers.length;a++){const o=t.buffers[a];if(!o)continue;const h=o._glBuffers[this.CONTEXT_UID];h&&(h.refCount--,h.refCount===0&&!e&&n.dispose(o,e))}s&&(e||i.deleteTransformFeedback(s),delete t._glTransformFeedbacks[this.CONTEXT_UID])}destroy(){this.renderer=null}}Pn.extension={type:D.RendererSystem,name:"transformFeedback"},U.add(Pn);class Bs{constructor(t){this.renderer=t}init(t){this.screen=new z(0,0,t.width,t.height),this.element=t.view||N.ADAPTER.createCanvas(),this.resolution=t.resolution||N.RESOLUTION,this.autoDensity=!!t.autoDensity}resizeView(t,e){this.element.width=Math.round(t*this.resolution),this.element.height=Math.round(e*this.resolution);const s=this.element.width/this.resolution,i=this.element.height/this.resolution;this.screen.width=s,this.screen.height=i,this.autoDensity&&(this.element.style.width=`${s}px`,this.element.style.height=`${i}px`),this.renderer.emit("resize",s,i),this.renderer.runners.resize.emit(this.screen.width,this.screen.height)}destroy(t){var e;t&&((e=this.element.parentNode)==null||e.removeChild(this.element)),this.renderer=null,this.element=null,this.screen=null}}Bs.defaultOptions={width:800,height:600,resolution:void 0,autoDensity:!1},Bs.extension={type:[D.RendererSystem,D.CanvasRendererSystem],name:"_view"},U.add(Bs);var hf=Object.defineProperty,Ah=Object.getOwnPropertySymbols,lf=Object.prototype.hasOwnProperty,uf=Object.prototype.propertyIsEnumerable,wh=(r,t,e)=>t in r?hf(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,jr=(r,t)=>{for(var e in t||(t={}))lf.call(t,e)&&wh(r,e,t[e]);if(Ah)for(var e of Ah(t))uf.call(t,e)&&wh(r,e,t[e]);return r};N.PREFER_ENV=be.WEBGL2,N.STRICT_TEXTURE_CACHE=!1,N.RENDER_OPTIONS=jr(jr(jr(jr({},Ps.defaultOptions),Is.defaultOptions),Bs.defaultOptions),Ds.defaultOptions),Object.defineProperties(N,{WRAP_MODE:{get(){return X.defaultOptions.wrapMode},set(r){X.defaultOptions.wrapMode=r}},SCALE_MODE:{get(){return X.defaultOptions.scaleMode},set(r){X.defaultOptions.scaleMode=r}},MIPMAP_TEXTURES:{get(){return X.defaultOptions.mipmap},set(r){X.defaultOptions.mipmap=r}},ANISOTROPIC_LEVEL:{get(){return X.defaultOptions.anisotropicLevel},set(r){X.defaultOptions.anisotropicLevel=r}},FILTER_RESOLUTION:{get(){return Et.defaultResolution},set(r){Et.defaultResolution=r}},FILTER_MULTISAMPLE:{get(){return Et.defaultMultisample},set(r){Et.defaultMultisample=r}},SPRITE_MAX_TEXTURES:{get(){return Ee.defaultMaxTextures},set(r){Ee.defaultMaxTextures=r}},SPRITE_BATCH_SIZE:{get(){return Ee.defaultBatchSize},set(r){Ee.defaultBatchSize=r}},CAN_UPLOAD_SAME_BUFFER:{get(){return Ee.canUploadSameBuffer},set(r){Ee.canUploadSameBuffer=r}},GC_MODE:{get(){return Ae.defaultMode},set(r){Ae.defaultMode=r}},GC_MAX_IDLE:{get(){return Ae.defaultMaxIdle},set(r){Ae.defaultMaxIdle=r}},GC_MAX_CHECK_COUNT:{get(){return Ae.defaultCheckCountMax},set(r){Ae.defaultCheckCountMax=r}},PRECISION_VERTEX:{get(){return se.defaultVertexPrecision},set(r){se.defaultVertexPrecision=r}},PRECISION_FRAGMENT:{get(){return se.defaultFragmentPrecision},set(r){se.defaultFragmentPrecision=r}}});var me=(r=>(r[r.INTERACTION=50]="INTERACTION",r[r.HIGH=25]="HIGH",r[r.NORMAL=0]="NORMAL",r[r.LOW=-25]="LOW",r[r.UTILITY=-50]="UTILITY",r))(me||{});class Mn{constructor(t,e=null,s=0,i=!1){this.next=null,this.previous=null,this._destroyed=!1,this.fn=t,this.context=e,this.priority=s,this.once=i}match(t,e=null){return this.fn===t&&this.context===e}emit(t){this.fn&&(this.context?this.fn.call(this.context,t):this.fn(t));const e=this.next;return this.once&&this.destroy(!0),this._destroyed&&(this.next=null),e}connect(t){this.previous=t,t.next&&(t.next.previous=this),this.next=t.next,t.next=this}destroy(t=!1){this._destroyed=!0,this.fn=null,this.context=null,this.previous&&(this.previous.next=this.next),this.next&&(this.next.previous=this.previous);const e=this.next;return this.next=t?null:e,this.previous=null,e}}const Sh=class Dt{constructor(){this.autoStart=!1,this.deltaTime=1,this.lastTime=-1,this.speed=1,this.started=!1,this._requestId=null,this._maxElapsedMS=100,this._minElapsedMS=0,this._protected=!1,this._lastFrame=-1,this._head=new Mn(null,null,1/0),this.deltaMS=1/Dt.targetFPMS,this.elapsedMS=1/Dt.targetFPMS,this._tick=t=>{this._requestId=null,this.started&&(this.update(t),this.started&&this._requestId===null&&this._head.next&&(this._requestId=requestAnimationFrame(this._tick)))}}_requestIfNeeded(){this._requestId===null&&this._head.next&&(this.lastTime=performance.now(),this._lastFrame=this.lastTime,this._requestId=requestAnimationFrame(this._tick))}_cancelIfNeeded(){this._requestId!==null&&(cancelAnimationFrame(this._requestId),this._requestId=null)}_startIfPossible(){this.started?this._requestIfNeeded():this.autoStart&&this.start()}add(t,e,s=me.NORMAL){return this._addListener(new Mn(t,e,s))}addOnce(t,e,s=me.NORMAL){return this._addListener(new Mn(t,e,s,!0))}_addListener(t){let e=this._head.next,s=this._head;if(!e)t.connect(s);else{for(;e;){if(t.priority>e.priority){t.connect(s);break}s=e,e=e.next}t.previous||t.connect(s)}return this._startIfPossible(),this}remove(t,e){let s=this._head.next;for(;s;)s.match(t,e)?s=s.destroy():s=s.next;return this._head.next||this._cancelIfNeeded(),this}get count(){if(!this._head)return 0;let t=0,e=this._head;for(;e=e.next;)t++;return t}start(){this.started||(this.started=!0,this._requestIfNeeded())}stop(){this.started&&(this.started=!1,this._cancelIfNeeded())}destroy(){if(!this._protected){this.stop();let t=this._head.next;for(;t;)t=t.destroy(!0);this._head.destroy(),this._head=null}}update(t=performance.now()){let e;if(t>this.lastTime){if(e=this.elapsedMS=t-this.lastTime,e>this._maxElapsedMS&&(e=this._maxElapsedMS),e*=this.speed,this._minElapsedMS){const n=t-this._lastFrame|0;if(n<this._minElapsedMS)return;this._lastFrame=t-n%this._minElapsedMS}this.deltaMS=e,this.deltaTime=this.deltaMS*Dt.targetFPMS;const s=this._head;let i=s.next;for(;i;)i=i.emit(this.deltaTime);s.next||this._cancelIfNeeded()}else this.deltaTime=this.deltaMS=this.elapsedMS=0;this.lastTime=t}get FPS(){return 1e3/this.elapsedMS}get minFPS(){return 1e3/this._maxElapsedMS}set minFPS(t){const e=Math.min(this.maxFPS,t),s=Math.min(Math.max(0,e)/1e3,Dt.targetFPMS);this._maxElapsedMS=1/s}get maxFPS(){return this._minElapsedMS?Math.round(1e3/this._minElapsedMS):0}set maxFPS(t){if(t===0)this._minElapsedMS=0;else{const e=Math.max(this.minFPS,t);this._minElapsedMS=1/(e/1e3)}}static get shared(){if(!Dt._shared){const t=Dt._shared=new Dt;t.autoStart=!0,t._protected=!0}return Dt._shared}static get system(){if(!Dt._system){const t=Dt._system=new Dt;t.autoStart=!0,t._protected=!0}return Dt._system}};Sh.targetFPMS=.06;let bt=Sh;Object.defineProperties(N,{TARGET_FPMS:{get(){return bt.targetFPMS},set(r){bt.targetFPMS=r}}});class Dn{static init(t){t=Object.assign({autoStart:!0,sharedTicker:!1},t),Object.defineProperty(this,"ticker",{set(e){this._ticker&&this._ticker.remove(this.render,this),this._ticker=e,e&&e.add(this.render,this,me.LOW)},get(){return this._ticker}}),this.stop=()=>{this._ticker.stop()},this.start=()=>{this._ticker.start()},this._ticker=null,this.ticker=t.sharedTicker?bt.shared:new bt,t.autoStart&&this.start()}static destroy(){if(this._ticker){const t=this._ticker;this.ticker=null,t.destroy()}}}Dn.extension=D.Application,U.add(Dn);const Ch=[];U.handleByList(D.Renderer,Ch);function Rh(r){for(const t of Ch)if(t.test(r))return new t(r);throw new Error("Unable to auto-detect a suitable renderer.")}var cf=`attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;

uniform mat3 projectionMatrix;

varying vec2 vTextureCoord;

void main(void)
{
    gl_Position = vec4((projectionMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);
    vTextureCoord = aTextureCoord;
}`,df=`attribute vec2 aVertexPosition;

uniform mat3 projectionMatrix;

varying vec2 vTextureCoord;

uniform vec4 inputSize;
uniform vec4 outputFrame;

vec4 filterVertexPosition( void )
{
    vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;

    return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
}

vec2 filterTextureCoord( void )
{
    return aVertexPosition * (outputFrame.zw * inputSize.zw);
}

void main(void)
{
    gl_Position = filterVertexPosition();
    vTextureCoord = filterTextureCoord();
}
`;const Ih=cf,On=df;class Bn{constructor(t){this.renderer=t}contextChange(t){let e;if(this.renderer.context.webGLVersion===1){const s=t.getParameter(t.FRAMEBUFFER_BINDING);t.bindFramebuffer(t.FRAMEBUFFER,null),e=t.getParameter(t.SAMPLES),t.bindFramebuffer(t.FRAMEBUFFER,s)}else{const s=t.getParameter(t.DRAW_FRAMEBUFFER_BINDING);t.bindFramebuffer(t.DRAW_FRAMEBUFFER,null),e=t.getParameter(t.SAMPLES),t.bindFramebuffer(t.DRAW_FRAMEBUFFER,s)}e>=ft.HIGH?this.multisample=ft.HIGH:e>=ft.MEDIUM?this.multisample=ft.MEDIUM:e>=ft.LOW?this.multisample=ft.LOW:this.multisample=ft.NONE}destroy(){}}Bn.extension={type:D.RendererSystem,name:"_multisample"},U.add(Bn);class ff{constructor(t){this.buffer=t||null,this.updateID=-1,this.byteLength=-1,this.refCount=0}}class Fn{constructor(t){this.renderer=t,this.managedBuffers={},this.boundBufferBases={}}destroy(){this.renderer=null}contextChange(){this.disposeAll(!0),this.gl=this.renderer.gl,this.CONTEXT_UID=this.renderer.CONTEXT_UID}bind(t){const{gl:e,CONTEXT_UID:s}=this,i=t._glBuffers[s]||this.createGLBuffer(t);e.bindBuffer(t.type,i.buffer)}unbind(t){const{gl:e}=this;e.bindBuffer(t,null)}bindBufferBase(t,e){const{gl:s,CONTEXT_UID:i}=this;if(this.boundBufferBases[e]!==t){const n=t._glBuffers[i]||this.createGLBuffer(t);this.boundBufferBases[e]=t,s.bindBufferBase(s.UNIFORM_BUFFER,e,n.buffer)}}bindBufferRange(t,e,s){const{gl:i,CONTEXT_UID:n}=this;s=s||0;const a=t._glBuffers[n]||this.createGLBuffer(t);i.bindBufferRange(i.UNIFORM_BUFFER,e||0,a.buffer,s*256,256)}update(t){const{gl:e,CONTEXT_UID:s}=this,i=t._glBuffers[s]||this.createGLBuffer(t);if(t._updateID!==i.updateID)if(i.updateID=t._updateID,e.bindBuffer(t.type,i.buffer),i.byteLength>=t.data.byteLength)e.bufferSubData(t.type,0,t.data);else{const n=t.static?e.STATIC_DRAW:e.DYNAMIC_DRAW;i.byteLength=t.data.byteLength,e.bufferData(t.type,t.data,n)}}dispose(t,e){if(!this.managedBuffers[t.id])return;delete this.managedBuffers[t.id];const s=t._glBuffers[this.CONTEXT_UID],i=this.gl;t.disposeRunner.remove(this),s&&(e||i.deleteBuffer(s.buffer),delete t._glBuffers[this.CONTEXT_UID])}disposeAll(t){const e=Object.keys(this.managedBuffers);for(let s=0;s<e.length;s++)this.dispose(this.managedBuffers[e[s]],t)}createGLBuffer(t){const{CONTEXT_UID:e,gl:s}=this;return t._glBuffers[e]=new ff(s.createBuffer()),this.managedBuffers[t.id]=t,t.disposeRunner.add(this),t._glBuffers[e]}}Fn.extension={type:D.RendererSystem,name:"buffer"},U.add(Fn);class Nn{constructor(t){this.renderer=t}render(t,e){const s=this.renderer;let i,n,a,o;if(e&&(i=e.renderTexture,n=e.clear,a=e.transform,o=e.skipUpdateTransform),this.renderingToScreen=!i,s.runners.prerender.emit(),s.emit("prerender"),s.projection.transform=a,!s.context.isLost){if(i||(this.lastObjectRendered=t),!o){const h=t.enableTempParent();t.updateTransform(),t.disableTempParent(h)}s.renderTexture.bind(i),s.batch.currentRenderer.start(),(n!=null?n:s.background.clearBeforeRender)&&s.renderTexture.clear(),t.render(s),s.batch.currentRenderer.flush(),i&&(e.blit&&s.framebuffer.blit(),i.baseTexture.update()),s.runners.postrender.emit(),s.projection.transform=null,s.emit("postrender")}}destroy(){this.renderer=null,this.lastObjectRendered=null}}Nn.extension={type:D.RendererSystem,name:"objectRenderer"},U.add(Nn);const Xr=class wa extends Rn{constructor(t){super(),this.type=cr.WEBGL,t=Object.assign({},N.RENDER_OPTIONS,t),this.gl=null,this.CONTEXT_UID=0,this.globalUniforms=new Lt({projectionMatrix:new tt},!0);const e={runners:["init","destroy","contextChange","resolutionChange","reset","update","postrender","prerender","resize"],systems:wa.__systems,priority:["_view","textureGenerator","background","_plugin","startup","context","state","texture","buffer","geometry","framebuffer","transformFeedback","mask","scissor","stencil","projection","textureGC","filter","renderTexture","batch","objectRenderer","_multisample"]};this.setup(e),"useContextAlpha"in t&&(t.premultipliedAlpha=t.useContextAlpha&&t.useContextAlpha!=="notMultiplied",t.backgroundAlpha=t.useContextAlpha===!1?1:t.backgroundAlpha),this._plugin.rendererPlugins=wa.__plugins,this.options=t,this.startup.run(this.options)}static test(t){return t!=null&&t.forceCanvas?!1:ho()}render(t,e){this.objectRenderer.render(t,e)}resize(t,e){this._view.resizeView(t,e)}reset(){return this.runners.reset.emit(),this}clear(){this.renderTexture.bind(),this.renderTexture.clear()}destroy(t=!1){this.runners.destroy.items.reverse(),this.emitWithCustomOptions(this.runners.destroy,{_view:t}),super.destroy()}get plugins(){return this._plugin.plugins}get multisample(){return this._multisample.multisample}get width(){return this._view.element.width}get height(){return this._view.element.height}get resolution(){return this._view.resolution}set resolution(t){this._view.resolution=t,this.runners.resolutionChange.emit(t)}get autoDensity(){return this._view.autoDensity}get view(){return this._view.element}get screen(){return this._view.screen}get lastObjectRendered(){return this.objectRenderer.lastObjectRendered}get renderingToScreen(){return this.objectRenderer.renderingToScreen}get rendererLogId(){return`WebGL ${this.context.webGLVersion}`}get clearBeforeRender(){return this.background.clearBeforeRender}get useContextAlpha(){return this.context.useContextAlpha}get preserveDrawingBuffer(){return this.context.preserveDrawingBuffer}get backgroundColor(){return this.background.color}set backgroundColor(t){this.background.color=t}get backgroundAlpha(){return this.background.alpha}set backgroundAlpha(t){this.background.alpha=t}get powerPreference(){return this.context.powerPreference}generateTexture(t,e){return this.textureGenerator.generateTexture(t,e)}};Xr.extension={type:D.Renderer,priority:1},Xr.__plugins={},Xr.__systems={};let zr=Xr;U.handleByMap(D.RendererPlugin,zr.__plugins),U.handleByMap(D.RendererSystem,zr.__systems),U.add(zr);class Ln extends Qe{constructor(t,e){const{width:s,height:i}=e||{};super(s,i),this.items=[],this.itemDirtyIds=[];for(let n=0;n<t;n++){const a=new X;this.items.push(a),this.itemDirtyIds.push(-2)}this.length=t,this._load=null,this.baseTexture=null}initFromArray(t,e){for(let s=0;s<this.length;s++)t[s]&&(t[s].castToBaseTexture?this.addBaseTextureAt(t[s].castToBaseTexture(),s):t[s]instanceof Qe?this.addResourceAt(t[s],s):this.addResourceAt(sn(t[s],e),s))}dispose(){for(let t=0,e=this.length;t<e;t++)this.items[t].destroy();this.items=null,this.itemDirtyIds=null,this._load=null}addResourceAt(t,e){if(!this.items[e])throw new Error(`Index ${e} is out of bounds`);return t.valid&&!this.valid&&this.resize(t.width,t.height),this.items[e].setResource(t),this}bind(t){if(this.baseTexture!==null)throw new Error("Only one base texture per TextureArray is allowed");super.bind(t);for(let e=0;e<this.length;e++)this.items[e].parentTextureArray=t,this.items[e].on("update",t.update,t)}unbind(t){super.unbind(t);for(let e=0;e<this.length;e++)this.items[e].parentTextureArray=null,this.items[e].off("update",t.update,t)}load(){if(this._load)return this._load;const t=this.items.map(e=>e.resource).filter(e=>e).map(e=>e.load());return this._load=Promise.all(t).then(()=>{const{realWidth:e,realHeight:s}=this.items[0];return this.resize(e,s),this.update(),Promise.resolve(this)}),this._load}}class Ph extends Ln{constructor(t,e){const{width:s,height:i}=e||{};let n,a;Array.isArray(t)?(n=t,a=t.length):a=t,super(a,{width:s,height:i}),n&&this.initFromArray(n,e)}addBaseTextureAt(t,e){if(t.resource)this.addResourceAt(t.resource,e);else throw new Error("ArrayResource does not support RenderTexture");return this}bind(t){super.bind(t),t.target=Me.TEXTURE_2D_ARRAY}upload(t,e,s){const{length:i,itemDirtyIds:n,items:a}=this,{gl:o}=t;s.dirtyId<0&&o.texImage3D(o.TEXTURE_2D_ARRAY,0,s.internalFormat,this._width,this._height,i,0,e.format,s.type,null);for(let h=0;h<i;h++){const l=a[h];n[h]<l.dirtyId&&(n[h]=l.dirtyId,l.valid&&o.texSubImage3D(o.TEXTURE_2D_ARRAY,0,0,0,h,l.resource.width,l.resource.height,1,e.format,s.type,l.resource.source))}return!0}}class Un extends re{constructor(t){super(t)}static test(t){const{OffscreenCanvas:e}=globalThis;return e&&t instanceof e?!0:globalThis.HTMLCanvasElement&&t instanceof HTMLCanvasElement}}const Mh=class or extends Ln{constructor(t,e){const{width:s,height:i,autoLoad:n,linkBaseTexture:a}=e||{};if(t&&t.length!==or.SIDES)throw new Error(`Invalid length. Got ${t.length}, expected 6`);super(6,{width:s,height:i});for(let o=0;o<or.SIDES;o++)this.items[o].target=Me.TEXTURE_CUBE_MAP_POSITIVE_X+o;this.linkBaseTexture=a!==!1,t&&this.initFromArray(t,e),n!==!1&&this.load()}bind(t){super.bind(t),t.target=Me.TEXTURE_CUBE_MAP}addBaseTextureAt(t,e,s){if(s===void 0&&(s=this.linkBaseTexture),!this.items[e])throw new Error(`Index ${e} is out of bounds`);if(!this.linkBaseTexture||t.parentTextureArray||Object.keys(t._glTextures).length>0)if(t.resource)this.addResourceAt(t.resource,e);else throw new Error("CubeResource does not support copying of renderTexture.");else t.target=Me.TEXTURE_CUBE_MAP_POSITIVE_X+e,t.parentTextureArray=this.baseTexture,this.items[e]=t;return t.valid&&!this.valid&&this.resize(t.realWidth,t.realHeight),this.items[e]=t,this}upload(t,e,s){const i=this.itemDirtyIds;for(let n=0;n<or.SIDES;n++){const a=this.items[n];(i[n]<a.dirtyId||s.dirtyId<e.dirtyId)&&(a.valid&&a.resource?(a.resource.upload(t,a,s),i[n]=a.dirtyId):i[n]<-1&&(t.gl.texImage2D(a.target,0,s.internalFormat,e.realWidth,e.realHeight,0,e.format,s.type,null),i[n]=-1))}return!0}static test(t){return Array.isArray(t)&&t.length===or.SIDES}};Mh.SIDES=6;let Dh=Mh;class $e extends re{constructor(t,e){var s,i;e=e||{};let n,a,o;typeof t=="string"?(n=$e.EMPTY,a=t,o=!0):(n=t,a=null,o=!1),super(n),this.url=a,this.crossOrigin=(s=e.crossOrigin)!=null?s:!0,this.alphaMode=typeof e.alphaMode=="number"?e.alphaMode:null,this.ownsImageBitmap=(i=e.ownsImageBitmap)!=null?i:o,this._load=null,e.autoLoad!==!1&&this.load()}load(){return this._load?this._load:(this._load=new Promise(async(t,e)=>{if(this.url===null){t(this);return}try{const s=await N.ADAPTER.fetch(this.url,{mode:this.crossOrigin?"cors":"no-cors"});if(this.destroyed)return;const i=await s.blob();if(this.destroyed)return;const n=await createImageBitmap(i,{premultiplyAlpha:this.alphaMode===null||this.alphaMode===wt.UNPACK?"premultiply":"none"});if(this.destroyed){n.close();return}this.source=n,this.update(),t(this)}catch(s){if(this.destroyed)return;e(s),this.onError.emit(s)}}),this._load)}upload(t,e,s){return this.source instanceof ImageBitmap?(typeof this.alphaMode=="number"&&(e.alphaMode=this.alphaMode),super.upload(t,e,s)):(this.load(),!1)}dispose(){this.ownsImageBitmap&&this.source instanceof ImageBitmap&&this.source.close(),super.dispose(),this._load=null}static test(t){return!!globalThis.createImageBitmap&&typeof ImageBitmap!="undefined"&&(typeof t=="string"||t instanceof ImageBitmap)}static get EMPTY(){var t;return $e._EMPTY=(t=$e._EMPTY)!=null?t:N.ADAPTER.createCanvas(0,0),$e._EMPTY}}const kn=class Ai extends re{constructor(t,e){e=e||{},super(N.ADAPTER.createCanvas()),this._width=0,this._height=0,this.svg=t,this.scale=e.scale||1,this._overrideWidth=e.width,this._overrideHeight=e.height,this._resolve=null,this._crossorigin=e.crossorigin,this._load=null,e.autoLoad!==!1&&this.load()}load(){return this._load?this._load:(this._load=new Promise(t=>{if(this._resolve=()=>{this.update(),t(this)},Ai.SVG_XML.test(this.svg.trim())){if(!btoa)throw new Error("Your browser doesn't support base64 conversions.");this.svg=`data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(this.svg)))}`}this._loadSvg()}),this._load)}_loadSvg(){const t=new Image;re.crossOrigin(t,this.svg,this._crossorigin),t.src=this.svg,t.onerror=e=>{this._resolve&&(t.onerror=null,this.onError.emit(e))},t.onload=()=>{if(!this._resolve)return;const e=t.width,s=t.height;if(!e||!s)throw new Error("The SVG image must have width and height defined (in pixels), canvas API needs them.");let i=e*this.scale,n=s*this.scale;(this._overrideWidth||this._overrideHeight)&&(i=this._overrideWidth||this._overrideHeight/s*e,n=this._overrideHeight||this._overrideWidth/e*s),i=Math.round(i),n=Math.round(n);const a=this.source;a.width=i,a.height=n,a._pixiId=`canvas_${Te()}`,a.getContext("2d").drawImage(t,0,0,e,s,0,0,i,n),this._resolve(),this._resolve=null}}static getSize(t){const e=Ai.SVG_SIZE.exec(t),s={};return e&&(s[e[1]]=Math.round(parseFloat(e[3])),s[e[5]]=Math.round(parseFloat(e[7]))),s}dispose(){super.dispose(),this._resolve=null,this._crossorigin=null}static test(t,e){return e==="svg"||typeof t=="string"&&t.startsWith("data:image/svg+xml")||typeof t=="string"&&Ai.SVG_XML.test(t)}};kn.SVG_XML=/^(<\?xml[^?]+\?>)?\s*(<!--[^(-->)]*-->)?\s*\<svg/m,kn.SVG_SIZE=/<svg[^>]*(?:\s(width|height)=('|")(\d*(?:\.\d+)?)(?:px)?('|"))[^>]*(?:\s(width|height)=('|")(\d*(?:\.\d+)?)(?:px)?('|"))[^>]*>/i;let Wr=kn;class pf extends re{constructor(t){super(t)}static test(t){return!!globalThis.VideoFrame&&t instanceof globalThis.VideoFrame}}const Gn=class Sa extends re{constructor(t,e){if(e=e||{},!(t instanceof HTMLVideoElement)){const s=document.createElement("video");e.autoLoad!==!1&&s.setAttribute("preload","auto"),e.playsinline!==!1&&(s.setAttribute("webkit-playsinline",""),s.setAttribute("playsinline","")),e.muted===!0&&(s.setAttribute("muted",""),s.muted=!0),e.loop===!0&&s.setAttribute("loop",""),e.autoPlay!==!1&&s.setAttribute("autoplay",""),typeof t=="string"&&(t=[t]);const i=t[0].src||t[0];re.crossOrigin(s,i,e.crossorigin);for(let n=0;n<t.length;++n){const a=document.createElement("source");let{src:o,mime:h}=t[n];if(o=o||t[n],o.startsWith("data:"))h=o.slice(5,o.indexOf(";"));else if(!o.startsWith("blob:")){const l=o.split("?").shift().toLowerCase(),u=l.slice(l.lastIndexOf(".")+1);h=h||Sa.MIME_TYPES[u]||`video/${u}`}a.src=o,h&&(a.type=h),s.appendChild(a)}t=s}super(t),this.noSubImage=!0,this._autoUpdate=!0,this._isConnectedToTicker=!1,this._updateFPS=e.updateFPS||0,this._msToNextUpdate=0,this.autoPlay=e.autoPlay!==!1,this._videoFrameRequestCallback=this._videoFrameRequestCallback.bind(this),this._videoFrameRequestCallbackHandle=null,this._load=null,this._resolve=null,this._reject=null,this._onCanPlay=this._onCanPlay.bind(this),this._onError=this._onError.bind(this),this._onPlayStart=this._onPlayStart.bind(this),this._onPlayStop=this._onPlayStop.bind(this),this._onSeeked=this._onSeeked.bind(this),e.autoLoad!==!1&&this.load()}update(t=0){if(!this.destroyed){if(this._updateFPS){const e=bt.shared.elapsedMS*this.source.playbackRate;this._msToNextUpdate=Math.floor(this._msToNextUpdate-e)}(!this._updateFPS||this._msToNextUpdate<=0)&&(super.update(),this._msToNextUpdate=this._updateFPS?Math.floor(1e3/this._updateFPS):0)}}_videoFrameRequestCallback(){this.update(),this.destroyed?this._videoFrameRequestCallbackHandle=null:this._videoFrameRequestCallbackHandle=this.source.requestVideoFrameCallback(this._videoFrameRequestCallback)}load(){if(this._load)return this._load;const t=this.source;return(t.readyState===t.HAVE_ENOUGH_DATA||t.readyState===t.HAVE_FUTURE_DATA)&&t.width&&t.height&&(t.complete=!0),t.addEventListener("play",this._onPlayStart),t.addEventListener("pause",this._onPlayStop),t.addEventListener("seeked",this._onSeeked),this._isSourceReady()?this._onCanPlay():(t.addEventListener("canplay",this._onCanPlay),t.addEventListener("canplaythrough",this._onCanPlay),t.addEventListener("error",this._onError,!0)),this._load=new Promise((e,s)=>{this.valid?e(this):(this._resolve=e,this._reject=s,t.load())}),this._load}_onError(t){this.source.removeEventListener("error",this._onError,!0),this.onError.emit(t),this._reject&&(this._reject(t),this._reject=null,this._resolve=null)}_isSourcePlaying(){const t=this.source;return!t.paused&&!t.ended}_isSourceReady(){return this.source.readyState>2}_onPlayStart(){this.valid||this._onCanPlay(),this._configureAutoUpdate()}_onPlayStop(){this._configureAutoUpdate()}_onSeeked(){this._autoUpdate&&!this._isSourcePlaying()&&(this._msToNextUpdate=0,this.update(),this._msToNextUpdate=0)}_onCanPlay(){const t=this.source;t.removeEventListener("canplay",this._onCanPlay),t.removeEventListener("canplaythrough",this._onCanPlay);const e=this.valid;this._msToNextUpdate=0,this.update(),this._msToNextUpdate=0,!e&&this._resolve&&(this._resolve(this),this._resolve=null,this._reject=null),this._isSourcePlaying()?this._onPlayStart():this.autoPlay&&t.play()}dispose(){this._configureAutoUpdate();const t=this.source;t&&(t.removeEventListener("play",this._onPlayStart),t.removeEventListener("pause",this._onPlayStop),t.removeEventListener("seeked",this._onSeeked),t.removeEventListener("canplay",this._onCanPlay),t.removeEventListener("canplaythrough",this._onCanPlay),t.removeEventListener("error",this._onError,!0),t.pause(),t.src="",t.load()),super.dispose()}get autoUpdate(){return this._autoUpdate}set autoUpdate(t){t!==this._autoUpdate&&(this._autoUpdate=t,this._configureAutoUpdate())}get updateFPS(){return this._updateFPS}set updateFPS(t){t!==this._updateFPS&&(this._updateFPS=t,this._configureAutoUpdate())}_configureAutoUpdate(){this._autoUpdate&&this._isSourcePlaying()?!this._updateFPS&&this.source.requestVideoFrameCallback?(this._isConnectedToTicker&&(bt.shared.remove(this.update,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0),this._videoFrameRequestCallbackHandle===null&&(this._videoFrameRequestCallbackHandle=this.source.requestVideoFrameCallback(this._videoFrameRequestCallback))):(this._videoFrameRequestCallbackHandle!==null&&(this.source.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker||(bt.shared.add(this.update,this),this._isConnectedToTicker=!0,this._msToNextUpdate=0)):(this._videoFrameRequestCallbackHandle!==null&&(this.source.cancelVideoFrameCallback(this._videoFrameRequestCallbackHandle),this._videoFrameRequestCallbackHandle=null),this._isConnectedToTicker&&(bt.shared.remove(this.update,this),this._isConnectedToTicker=!1,this._msToNextUpdate=0))}static test(t,e){return globalThis.HTMLVideoElement&&t instanceof HTMLVideoElement||Sa.TYPES.includes(e)}};Gn.TYPES=["mp4","m4v","webm","ogg","ogv","h264","avi","mov"],Gn.MIME_TYPES={ogv:"video/ogg",mov:"video/quicktime",m4v:"video/mp4"};let $n=Gn;wr.push($e,dn,Un,$n,pf,Wr,Ts,Dh,Ph);class mf{constructor(){this._glTransformFeedbacks={},this.buffers=[],this.disposeRunner=new Pt("disposeTransformFeedback")}bindBuffer(t,e){this.buffers[t]=e}destroy(){this.disposeRunner.emit(this,!1)}}const gf="7.4.2";X.prototype.getDrawableSource=function(){const r=this.resource;return r?r.bitmap||r.source:null},Ur.prototype._canvasRenderTarget=null,L.prototype.patternCache=null,L.prototype.tintCache=null;let Fs;function Oh(r){const t=N.ADAPTER.createCanvas(6,1),e=t.getContext("2d");return e.fillStyle=r,e.fillRect(0,0,6,1),t}function Hn(){if(typeof document=="undefined")return!1;if(Fs!==void 0)return Fs;const r=Oh("#ff00ff"),t=Oh("#ffff00"),e=N.ADAPTER.createCanvas(6,1).getContext("2d");e.globalCompositeOperation="multiply",e.drawImage(r,0,0),e.drawImage(t,2,0);const s=e.getImageData(2,0,1,1);if(!s)Fs=!1;else{const i=s.data;Fs=i[0]===255&&i[1]===0&&i[2]===0}return Fs}function _f(r=[]){return Hn()?(r[C.NORMAL]="source-over",r[C.ADD]="lighter",r[C.MULTIPLY]="multiply",r[C.SCREEN]="screen",r[C.OVERLAY]="overlay",r[C.DARKEN]="darken",r[C.LIGHTEN]="lighten",r[C.COLOR_DODGE]="color-dodge",r[C.COLOR_BURN]="color-burn",r[C.HARD_LIGHT]="hard-light",r[C.SOFT_LIGHT]="soft-light",r[C.DIFFERENCE]="difference",r[C.EXCLUSION]="exclusion",r[C.HUE]="hue",r[C.SATURATION]="saturation",r[C.COLOR]="color",r[C.LUMINOSITY]="luminosity"):(r[C.NORMAL]="source-over",r[C.ADD]="lighter",r[C.MULTIPLY]="source-over",r[C.SCREEN]="source-over",r[C.OVERLAY]="source-over",r[C.DARKEN]="source-over",r[C.LIGHTEN]="source-over",r[C.COLOR_DODGE]="source-over",r[C.COLOR_BURN]="source-over",r[C.HARD_LIGHT]="source-over",r[C.SOFT_LIGHT]="source-over",r[C.DIFFERENCE]="source-over",r[C.EXCLUSION]="source-over",r[C.HUE]="source-over",r[C.SATURATION]="source-over",r[C.COLOR]="source-over",r[C.LUMINOSITY]="source-over"),r[C.NORMAL_NPM]=r[C.NORMAL],r[C.ADD_NPM]=r[C.ADD],r[C.SCREEN_NPM]=r[C.SCREEN],r[C.SRC_IN]="source-in",r[C.SRC_OUT]="source-out",r[C.SRC_ATOP]="source-atop",r[C.DST_OVER]="destination-over",r[C.DST_IN]="destination-in",r[C.DST_OUT]="destination-out",r[C.DST_ATOP]="destination-atop",r[C.XOR]="xor",r[C.SUBTRACT]="source-over",r}const vf=new tt;class Vn{constructor(t){this.activeResolution=1,this.smoothProperty="imageSmoothingEnabled",this.blendModes=_f(),this._activeBlendMode=null,this._projTransform=null,this._outerBlend=!1,this.renderer=t}init(){const t=this.renderer.background.alpha<1;if(this.rootContext=this.renderer.view.getContext("2d",{alpha:t}),this.activeContext=this.rootContext,!this.rootContext.imageSmoothingEnabled){const e=this.rootContext;e.webkitImageSmoothingEnabled?this.smoothProperty="webkitImageSmoothingEnabled":e.mozImageSmoothingEnabled?this.smoothProperty="mozImageSmoothingEnabled":e.oImageSmoothingEnabled?this.smoothProperty="oImageSmoothingEnabled":e.msImageSmoothingEnabled&&(this.smoothProperty="msImageSmoothingEnabled")}}setContextTransform(t,e,s){let i=t;const n=this._projTransform,a=this.activeResolution;s=s||a,n&&(i=vf,i.copyFrom(t),i.prepend(n)),e?this.activeContext.setTransform(i.a*s,i.b*s,i.c*s,i.d*s,i.tx*a|0,i.ty*a|0):this.activeContext.setTransform(i.a*s,i.b*s,i.c*s,i.d*s,i.tx*a,i.ty*a)}clear(t,e){const{activeContext:s,renderer:i}=this,n=t?Y.shared.setValue(t):this.renderer.background.backgroundColor;s.clearRect(0,0,i.width,i.height),t&&(s.globalAlpha=e!=null?e:this.renderer.background.alpha,s.fillStyle=n.toHex(),s.fillRect(0,0,i.width,i.height),s.globalAlpha=1)}setBlendMode(t,e){const s=t===C.SRC_IN||t===C.SRC_OUT||t===C.DST_IN||t===C.DST_ATOP;!e&&s&&(t=C.NORMAL),this._activeBlendMode!==t&&(this._activeBlendMode=t,this._outerBlend=s,this.activeContext.globalCompositeOperation=this.blendModes[t])}resize(){this.smoothProperty&&(this.rootContext[this.smoothProperty]=X.defaultOptions.scaleMode===Bt.LINEAR)}invalidateBlendMode(){this._activeBlendMode=this.blendModes.indexOf(this.activeContext.globalCompositeOperation)}destroy(){this.renderer=null,this.rootContext=null,this.activeContext=null,this.smoothProperty=null}}Vn.extension={type:D.CanvasRendererSystem,name:"canvasContext"},U.add(Vn);class jn{constructor(t){this._foundShapes=[],this.renderer=t}pushMask(t){const e=this.renderer,s=t.maskObject||t;e.canvasContext.activeContext.save();const i=this._foundShapes;if(this.recursiveFindShapes(s,i),i.length>0){const n=e.canvasContext.activeContext;n.beginPath();for(let a=0;a<i.length;a++){const o=i[a],h=o.transform.worldTransform;this.renderer.canvasContext.setContextTransform(h),this.renderGraphicsShape(o)}i.length=0,n.clip()}}recursiveFindShapes(t,e){t.geometry&&t.geometry.graphicsData&&e.push(t);const{children:s}=t;if(s)for(let i=0;i<s.length;i++)this.recursiveFindShapes(s[i],e)}renderGraphicsShape(t){t.finishPoly();const e=this.renderer.canvasContext.activeContext,s=t.geometry.graphicsData,i=s.length;if(i!==0)for(let n=0;n<i;n++){const a=s[n],o=a.shape;if(o.type===rt.POLY){let h=o.points;const l=a.holes;let u,c,d,f;e.moveTo(h[0],h[1]);for(let p=1;p<h.length/2;p++)e.lineTo(h[p*2],h[p*2+1]);if(l.length>0){u=0,d=h[0],f=h[1];for(let p=2;p+2<h.length;p+=2)u+=(h[p]-d)*(h[p+3]-f)-(h[p+2]-d)*(h[p+1]-f);for(let p=0;p<l.length;p++)if(h=l[p].shape.points,!!h){c=0,d=h[0],f=h[1];for(let m=2;m+2<h.length;m+=2)c+=(h[m]-d)*(h[m+3]-f)-(h[m+2]-d)*(h[m+1]-f);if(c*u<0){e.moveTo(h[0],h[1]);for(let m=2;m<h.length;m+=2)e.lineTo(h[m],h[m+1])}else{e.moveTo(h[h.length-2],h[h.length-1]);for(let m=h.length-4;m>=0;m-=2)e.lineTo(h[m],h[m+1])}l[p].shape.closeStroke&&e.closePath()}}h[0]===h[h.length-2]&&h[1]===h[h.length-1]&&e.closePath()}else if(o.type===rt.RECT)e.rect(o.x,o.y,o.width,o.height),e.closePath();else if(o.type===rt.CIRC)e.arc(o.x,o.y,o.radius,0,2*Math.PI),e.closePath();else if(o.type===rt.ELIP){const h=o.width*2,l=o.height*2,u=o.x-h/2,c=o.y-l/2,d=.5522848,f=h/2*d,p=l/2*d,m=u+h,g=c+l,_=u+h/2,x=c+l/2;e.moveTo(u,x),e.bezierCurveTo(u,x-p,_-f,c,_,c),e.bezierCurveTo(_+f,c,m,x-p,m,x),e.bezierCurveTo(m,x+p,_+f,g,_,g),e.bezierCurveTo(_-f,g,u,x+p,u,x),e.closePath()}else if(o.type===rt.RREC){const h=o.x,l=o.y,u=o.width,c=o.height;let d=o.radius;const f=Math.min(u,c)/2;d=d>f?f:d,e.moveTo(h,l+d),e.lineTo(h,l+c-d),e.quadraticCurveTo(h,l+c,h+d,l+c),e.lineTo(h+u-d,l+c),e.quadraticCurveTo(h+u,l+c,h+u,l+c-d),e.lineTo(h+u,l+d),e.quadraticCurveTo(h+u,l,h+u-d,l),e.lineTo(h+d,l),e.quadraticCurveTo(h,l,h,l+d),e.closePath()}}}popMask(t){t.canvasContext.activeContext.restore(),t.canvasContext.invalidateBlendMode()}destroy(){}}jn.extension={type:D.CanvasRendererSystem,name:"mask"},U.add(jn);class Xn{constructor(t){this.renderer=t}render(t,e){const s=this.renderer;if(!s.view)return;const i=s.canvasContext;let n,a,o,h;e&&(n=e.renderTexture,a=e.clear,o=e.transform,h=e.skipUpdateTransform),this.renderingToScreen=!n,s.emit("prerender");const l=s.resolution;n?(n=n.castToBaseTexture(),n._canvasRenderTarget||(n._canvasRenderTarget=new bs(n.width,n.height,n.resolution),n.resource=new Un(n._canvasRenderTarget.canvas),n.valid=!0),i.activeContext=n._canvasRenderTarget.context,s.canvasContext.activeResolution=n._canvasRenderTarget.resolution):(i.activeContext=i.rootContext,i.activeResolution=l);const u=i.activeContext;if(i._projTransform=o||null,n||(this.lastObjectRendered=t),!h){const d=t.enableTempParent();t.updateTransform(),t.disableTempParent(d)}if(u.save(),u.setTransform(1,0,0,1,0,0),u.globalAlpha=1,i._activeBlendMode=C.NORMAL,i._outerBlend=!1,u.globalCompositeOperation=i.blendModes[C.NORMAL],a!=null?a:s.background.clearBeforeRender)if(this.renderingToScreen){u.clearRect(0,0,s.width,s.height);const d=s.background;d.alpha>0&&(u.globalAlpha=d.backgroundColor.alpha,u.fillStyle=d.backgroundColor.toHex(),u.fillRect(0,0,s.width,s.height),u.globalAlpha=1)}else n=n,n._canvasRenderTarget.clear(),n.clear.alpha>0&&(u.globalAlpha=n.clear.alpha,u.fillStyle=n.clear.toHex(),u.fillRect(0,0,n.realWidth,n.realHeight),u.globalAlpha=1);const c=i.activeContext;i.activeContext=u,t.renderCanvas(s),i.activeContext=c,u.restore(),i.activeResolution=l,i._projTransform=null,s.emit("postrender")}destroy(){this.lastObjectRendered=null,this.render=null}}Xn.extension={type:D.CanvasRendererSystem,name:"objectRenderer"},U.add(Xn);const{deprecation:yf}=Do,Yr=class Ca extends Rn{constructor(t){super(),this.type=cr.CANVAS,this.rendererLogId="Canvas",t=Object.assign({},N.RENDER_OPTIONS,t);const e={runners:["init","destroy","contextChange","resolutionChange","reset","update","postrender","prerender","resize"],systems:Ca.__systems,priority:["textureGenerator","background","_view","_plugin","startup","mask","canvasContext","objectRenderer"]};this.setup(e),"useContextAlpha"in t&&(t.backgroundAlpha=t.useContextAlpha===!1?1:t.backgroundAlpha),this._plugin.rendererPlugins=Ca.__plugins,this.options=t,this.startup.run(this.options)}static test(){return!0}generateTexture(t,e){return this.textureGenerator.generateTexture(t,e)}reset(){}render(t,e){this.objectRenderer.render(t,e)}clear(){this.canvasContext.clear()}destroy(t){this.runners.destroy.items.reverse(),this.emitWithCustomOptions(this.runners.destroy,{_view:t}),super.destroy()}get plugins(){return this._plugin.plugins}resize(t,e){this._view.resizeView(t,e)}get width(){return this._view.element.width}get height(){return this._view.element.height}get resolution(){return this._view.resolution}set resolution(t){this._view.resolution=t,this.runners.resolutionChange.emit(t)}get autoDensity(){return this._view.autoDensity}get view(){return this._view.element}get screen(){return this._view.screen}get lastObjectRendered(){return this.objectRenderer.lastObjectRendered}get renderingToScreen(){return this.objectRenderer.renderingToScreen}get clearBeforeRender(){return this.background.clearBeforeRender}get blendModes(){return this.canvasContext.blendModes}get maskManager(){return yf("7.0.0","renderer.maskManager has been deprecated, please use renderer.mask instead"),this.mask}get refresh(){return!0}get rootContext(){return this.canvasContext.rootContext}get context(){return this.canvasContext.activeContext}get smoothProperty(){return this.canvasContext.smoothProperty}setBlendMode(t,e){this.canvasContext.setBlendMode(t,e)}invalidateBlendMode(){this.canvasContext.invalidateBlendMode()}setContextTransform(t,e,s){this.canvasContext.setContextTransform(t,e,s)}get backgroundColor(){return this.background.color}set backgroundColor(t){this.background.color=t}get backgroundAlpha(){return this.background.alpha}set backgroundAlpha(t){this.background.alpha=t}get preserveDrawingBuffer(){return!1}get useContextAlpha(){return!1}};Yr.extension={type:D.Renderer,priority:0},Yr.__plugins={},Yr.__systems={};let Ns=Yr;U.handleByMap(D.CanvasRendererPlugin,Ns.__plugins),U.handleByMap(D.CanvasRendererSystem,Ns.__systems),U.add(Ns);const _t={canvas:null,getTintedCanvas:(r,t)=>{const e=r.texture,s=Y.shared.setValue(t).toHex();e.tintCache=e.tintCache||{};const i=e.tintCache[s];let n;if(i){if(i.tintId===e._updateID)return e.tintCache[s];n=e.tintCache[s]}else n=N.ADAPTER.createCanvas();if(_t.tintMethod(e,t,n),n.tintId=e._updateID,_t.convertTintToImage&&n.toDataURL!==void 0){const a=new Image;a.src=n.toDataURL(),e.tintCache[s]=a}else e.tintCache[s]=n;return n},getTintedPattern:(r,t)=>{const e=Y.shared.setValue(t).toHex();r.patternCache=r.patternCache||{};let s=r.patternCache[e];return(s==null?void 0:s.tintId)===r._updateID||(_t.canvas||(_t.canvas=N.ADAPTER.createCanvas()),_t.tintMethod(r,t,_t.canvas),s=_t.canvas.getContext("2d").createPattern(_t.canvas,"repeat"),s.tintId=r._updateID,r.patternCache[e]=s),s},tintWithMultiply:(r,t,e)=>{const s=e.getContext("2d"),i=r._frame.clone(),n=r.baseTexture.resolution;i.x*=n,i.y*=n,i.width*=n,i.height*=n,e.width=Math.ceil(i.width),e.height=Math.ceil(i.height),s.save(),s.fillStyle=Y.shared.setValue(t).toHex(),s.fillRect(0,0,i.width,i.height),s.globalCompositeOperation="multiply";const a=r.baseTexture.getDrawableSource();s.drawImage(a,i.x,i.y,i.width,i.height,0,0,i.width,i.height),s.globalCompositeOperation="destination-atop",s.drawImage(a,i.x,i.y,i.width,i.height,0,0,i.width,i.height),s.restore()},tintWithOverlay:(r,t,e)=>{const s=e.getContext("2d"),i=r._frame.clone(),n=r.baseTexture.resolution;i.x*=n,i.y*=n,i.width*=n,i.height*=n,e.width=Math.ceil(i.width),e.height=Math.ceil(i.height),s.save(),s.globalCompositeOperation="copy",s.fillStyle=`#${`00000${(t|0).toString(16)}`.slice(-6)}`,s.fillRect(0,0,i.width,i.height),s.globalCompositeOperation="destination-atop",s.drawImage(r.baseTexture.getDrawableSource(),i.x,i.y,i.width,i.height,0,0,i.width,i.height),s.restore()},tintWithPerPixel:(r,t,e)=>{const s=e.getContext("2d"),i=r._frame.clone(),n=r.baseTexture.resolution;i.x*=n,i.y*=n,i.width*=n,i.height*=n,e.width=Math.ceil(i.width),e.height=Math.ceil(i.height),s.save(),s.globalCompositeOperation="copy",s.drawImage(r.baseTexture.getDrawableSource(),i.x,i.y,i.width,i.height,0,0,i.width,i.height),s.restore();const[a,o,h]=Y.shared.setValue(t).toArray(),l=s.getImageData(0,0,i.width,i.height),u=l.data;for(let c=0;c<u.length;c+=4)u[c+0]*=a,u[c+1]*=o,u[c+2]*=h;s.putImageData(l,0,0)},roundColor:r=>Y.shared.setValue(r).round(_t.cacheStepsPerColorChannel).toNumber(),cacheStepsPerColorChannel:8,convertTintToImage:!1,canUseMultiply:Hn(),tintMethod:null};_t.tintMethod=_t.canUseMultiply?_t.tintWithMultiply:_t.tintWithPerPixel;class Ls{constructor(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0,this.rect=null,this.updateID=-1}isEmpty(){return this.minX>this.maxX||this.minY>this.maxY}clear(){this.minX=1/0,this.minY=1/0,this.maxX=-1/0,this.maxY=-1/0}getRectangle(t){return this.minX>this.maxX||this.minY>this.maxY?z.EMPTY:(t=t||new z(0,0,1,1),t.x=this.minX,t.y=this.minY,t.width=this.maxX-this.minX,t.height=this.maxY-this.minY,t)}addPoint(t){this.minX=Math.min(this.minX,t.x),this.maxX=Math.max(this.maxX,t.x),this.minY=Math.min(this.minY,t.y),this.maxY=Math.max(this.maxY,t.y)}addPointMatrix(t,e){const{a:s,b:i,c:n,d:a,tx:o,ty:h}=t,l=s*e.x+n*e.y+o,u=i*e.x+a*e.y+h;this.minX=Math.min(this.minX,l),this.maxX=Math.max(this.maxX,l),this.minY=Math.min(this.minY,u),this.maxY=Math.max(this.maxY,u)}addQuad(t){let e=this.minX,s=this.minY,i=this.maxX,n=this.maxY,a=t[0],o=t[1];e=a<e?a:e,s=o<s?o:s,i=a>i?a:i,n=o>n?o:n,a=t[2],o=t[3],e=a<e?a:e,s=o<s?o:s,i=a>i?a:i,n=o>n?o:n,a=t[4],o=t[5],e=a<e?a:e,s=o<s?o:s,i=a>i?a:i,n=o>n?o:n,a=t[6],o=t[7],e=a<e?a:e,s=o<s?o:s,i=a>i?a:i,n=o>n?o:n,this.minX=e,this.minY=s,this.maxX=i,this.maxY=n}addFrame(t,e,s,i,n){this.addFrameMatrix(t.worldTransform,e,s,i,n)}addFrameMatrix(t,e,s,i,n){const a=t.a,o=t.b,h=t.c,l=t.d,u=t.tx,c=t.ty;let d=this.minX,f=this.minY,p=this.maxX,m=this.maxY,g=a*e+h*s+u,_=o*e+l*s+c;d=g<d?g:d,f=_<f?_:f,p=g>p?g:p,m=_>m?_:m,g=a*i+h*s+u,_=o*i+l*s+c,d=g<d?g:d,f=_<f?_:f,p=g>p?g:p,m=_>m?_:m,g=a*e+h*n+u,_=o*e+l*n+c,d=g<d?g:d,f=_<f?_:f,p=g>p?g:p,m=_>m?_:m,g=a*i+h*n+u,_=o*i+l*n+c,d=g<d?g:d,f=_<f?_:f,p=g>p?g:p,m=_>m?_:m,this.minX=d,this.minY=f,this.maxX=p,this.maxY=m}addVertexData(t,e,s){let i=this.minX,n=this.minY,a=this.maxX,o=this.maxY;for(let h=e;h<s;h+=2){const l=t[h],u=t[h+1];i=l<i?l:i,n=u<n?u:n,a=l>a?l:a,o=u>o?u:o}this.minX=i,this.minY=n,this.maxX=a,this.maxY=o}addVertices(t,e,s,i){this.addVerticesMatrix(t.worldTransform,e,s,i)}addVerticesMatrix(t,e,s,i,n=0,a=n){const o=t.a,h=t.b,l=t.c,u=t.d,c=t.tx,d=t.ty;let f=this.minX,p=this.minY,m=this.maxX,g=this.maxY;for(let _=s;_<i;_+=2){const x=e[_],y=e[_+1],b=o*x+l*y+c,T=u*y+h*x+d;f=Math.min(f,b-n),m=Math.max(m,b+n),p=Math.min(p,T-a),g=Math.max(g,T+a)}this.minX=f,this.minY=p,this.maxX=m,this.maxY=g}addBounds(t){const e=this.minX,s=this.minY,i=this.maxX,n=this.maxY;this.minX=t.minX<e?t.minX:e,this.minY=t.minY<s?t.minY:s,this.maxX=t.maxX>i?t.maxX:i,this.maxY=t.maxY>n?t.maxY:n}addBoundsMask(t,e){const s=t.minX>e.minX?t.minX:e.minX,i=t.minY>e.minY?t.minY:e.minY,n=t.maxX<e.maxX?t.maxX:e.maxX,a=t.maxY<e.maxY?t.maxY:e.maxY;if(s<=n&&i<=a){const o=this.minX,h=this.minY,l=this.maxX,u=this.maxY;this.minX=s<o?s:o,this.minY=i<h?i:h,this.maxX=n>l?n:l,this.maxY=a>u?a:u}}addBoundsMatrix(t,e){this.addFrameMatrix(e,t.minX,t.minY,t.maxX,t.maxY)}addBoundsArea(t,e){const s=t.minX>e.x?t.minX:e.x,i=t.minY>e.y?t.minY:e.y,n=t.maxX<e.x+e.width?t.maxX:e.x+e.width,a=t.maxY<e.y+e.height?t.maxY:e.y+e.height;if(s<=n&&i<=a){const o=this.minX,h=this.minY,l=this.maxX,u=this.maxY;this.minX=s<o?s:o,this.minY=i<h?i:h,this.maxX=n>l?n:l,this.maxY=a>u?a:u}}pad(t=0,e=t){this.isEmpty()||(this.minX-=t,this.maxX+=t,this.minY-=e,this.maxY+=e)}addFramePad(t,e,s,i,n,a){t-=n,e-=a,s+=n,i+=a,this.minX=this.minX<t?this.minX:t,this.maxX=this.maxX>s?this.maxX:s,this.minY=this.minY<e?this.minY:e,this.maxY=this.maxY>i?this.maxY:i}}class ot extends Ye{constructor(){super(),this.tempDisplayObjectParent=null,this.transform=new Dr,this.alpha=1,this.visible=!0,this.renderable=!0,this.cullable=!1,this.cullArea=null,this.parent=null,this.worldAlpha=1,this._lastSortedIndex=0,this._zIndex=0,this.filterArea=null,this.filters=null,this._enabledFilters=null,this._bounds=new Ls,this._localBounds=null,this._boundsID=0,this._boundsRect=null,this._localBoundsRect=null,this._mask=null,this._maskRefCount=0,this._destroyed=!1,this.isSprite=!1,this.isMask=!1}static mixin(t){const e=Object.keys(t);for(let s=0;s<e.length;++s){const i=e[s];Object.defineProperty(ot.prototype,i,Object.getOwnPropertyDescriptor(t,i))}}get destroyed(){return this._destroyed}_recursivePostUpdateTransform(){this.parent?(this.parent._recursivePostUpdateTransform(),this.transform.updateTransform(this.parent.transform)):this.transform.updateTransform(this._tempDisplayObjectParent.transform)}updateTransform(){this._boundsID++,this.transform.updateTransform(this.parent.transform),this.worldAlpha=this.alpha*this.parent.worldAlpha}getBounds(t,e){return t||(this.parent?(this._recursivePostUpdateTransform(),this.updateTransform()):(this.parent=this._tempDisplayObjectParent,this.updateTransform(),this.parent=null)),this._bounds.updateID!==this._boundsID&&(this.calculateBounds(),this._bounds.updateID=this._boundsID),e||(this._boundsRect||(this._boundsRect=new z),e=this._boundsRect),this._bounds.getRectangle(e)}getLocalBounds(t){var e;t||(this._localBoundsRect||(this._localBoundsRect=new z),t=this._localBoundsRect),this._localBounds||(this._localBounds=new Ls);const s=this.transform,i=this.parent;this.parent=null,this._tempDisplayObjectParent.worldAlpha=(e=i==null?void 0:i.worldAlpha)!=null?e:1,this.transform=this._tempDisplayObjectParent.transform;const n=this._bounds,a=this._boundsID;this._bounds=this._localBounds;const o=this.getBounds(!1,t);return this.parent=i,this.transform=s,this._bounds=n,this._bounds.updateID+=this._boundsID-a,o}toGlobal(t,e,s=!1){return s||(this._recursivePostUpdateTransform(),this.parent?this.displayObjectUpdateTransform():(this.parent=this._tempDisplayObjectParent,this.displayObjectUpdateTransform(),this.parent=null)),this.worldTransform.apply(t,e)}toLocal(t,e,s,i){return e&&(t=e.toGlobal(t,s,i)),i||(this._recursivePostUpdateTransform(),this.parent?this.displayObjectUpdateTransform():(this.parent=this._tempDisplayObjectParent,this.displayObjectUpdateTransform(),this.parent=null)),this.worldTransform.applyInverse(t,s)}setParent(t){if(!t||!t.addChild)throw new Error("setParent: Argument must be a Container");return t.addChild(this),t}removeFromParent(){var t;(t=this.parent)==null||t.removeChild(this)}setTransform(t=0,e=0,s=1,i=1,n=0,a=0,o=0,h=0,l=0){return this.position.x=t,this.position.y=e,this.scale.x=s||1,this.scale.y=i||1,this.rotation=n,this.skew.x=a,this.skew.y=o,this.pivot.x=h,this.pivot.y=l,this}destroy(t){this.removeFromParent(),this._destroyed=!0,this.transform=null,this.parent=null,this._bounds=null,this.mask=null,this.cullArea=null,this.filters=null,this.filterArea=null,this.hitArea=null,this.eventMode="auto",this.interactiveChildren=!1,this.emit("destroyed"),this.removeAllListeners()}get _tempDisplayObjectParent(){return this.tempDisplayObjectParent===null&&(this.tempDisplayObjectParent=new Bh),this.tempDisplayObjectParent}enableTempParent(){const t=this.parent;return this.parent=this._tempDisplayObjectParent,t}disableTempParent(t){this.parent=t}get x(){return this.position.x}set x(t){this.transform.position.x=t}get y(){return this.position.y}set y(t){this.transform.position.y=t}get worldTransform(){return this.transform.worldTransform}get localTransform(){return this.transform.localTransform}get position(){return this.transform.position}set position(t){this.transform.position.copyFrom(t)}get scale(){return this.transform.scale}set scale(t){this.transform.scale.copyFrom(t)}get pivot(){return this.transform.pivot}set pivot(t){this.transform.pivot.copyFrom(t)}get skew(){return this.transform.skew}set skew(t){this.transform.skew.copyFrom(t)}get rotation(){return this.transform.rotation}set rotation(t){this.transform.rotation=t}get angle(){return this.transform.rotation*$o}set angle(t){this.transform.rotation=t*Ho}get zIndex(){return this._zIndex}set zIndex(t){this._zIndex!==t&&(this._zIndex=t,this.parent&&(this.parent.sortDirty=!0))}get worldVisible(){let t=this;do{if(!t.visible)return!1;t=t.parent}while(t);return!0}get mask(){return this._mask}set mask(t){if(this._mask!==t){if(this._mask){const e=this._mask.isMaskData?this._mask.maskObject:this._mask;e&&(e._maskRefCount--,e._maskRefCount===0&&(e.renderable=!0,e.isMask=!1))}if(this._mask=t,this._mask){const e=this._mask.isMaskData?this._mask.maskObject:this._mask;e&&(e._maskRefCount===0&&(e.renderable=!1,e.isMask=!0),e._maskRefCount++)}}}}class Bh extends ot{constructor(){super(...arguments),this.sortDirty=null}}ot.prototype.displayObjectUpdateTransform=ot.prototype.updateTransform;const xf=new tt;function bf(r,t){return r.zIndex===t.zIndex?r._lastSortedIndex-t._lastSortedIndex:r.zIndex-t.zIndex}const Fh=class Ra extends ot{constructor(){super(),this.children=[],this.sortableChildren=Ra.defaultSortableChildren,this.sortDirty=!1}onChildrenChange(t){}addChild(...t){if(t.length>1)for(let e=0;e<t.length;e++)this.addChild(t[e]);else{const e=t[0];e.parent&&e.parent.removeChild(e),e.parent=this,this.sortDirty=!0,e.transform._parentID=-1,this.children.push(e),this._boundsID++,this.onChildrenChange(this.children.length-1),this.emit("childAdded",e,this,this.children.length-1),e.emit("added",this)}return t[0]}addChildAt(t,e){if(e<0||e>this.children.length)throw new Error(`${t}addChildAt: The index ${e} supplied is out of bounds ${this.children.length}`);return t.parent&&t.parent.removeChild(t),t.parent=this,this.sortDirty=!0,t.transform._parentID=-1,this.children.splice(e,0,t),this._boundsID++,this.onChildrenChange(e),t.emit("added",this),this.emit("childAdded",t,this,e),t}swapChildren(t,e){if(t===e)return;const s=this.getChildIndex(t),i=this.getChildIndex(e);this.children[s]=e,this.children[i]=t,this.onChildrenChange(s<i?s:i)}getChildIndex(t){const e=this.children.indexOf(t);if(e===-1)throw new Error("The supplied DisplayObject must be a child of the caller");return e}setChildIndex(t,e){if(e<0||e>=this.children.length)throw new Error(`The index ${e} supplied is out of bounds ${this.children.length}`);const s=this.getChildIndex(t);Oe(this.children,s,1),this.children.splice(e,0,t),this.onChildrenChange(e)}getChildAt(t){if(t<0||t>=this.children.length)throw new Error(`getChildAt: Index (${t}) does not exist.`);return this.children[t]}removeChild(...t){if(t.length>1)for(let e=0;e<t.length;e++)this.removeChild(t[e]);else{const e=t[0],s=this.children.indexOf(e);if(s===-1)return null;e.parent=null,e.transform._parentID=-1,Oe(this.children,s,1),this._boundsID++,this.onChildrenChange(s),e.emit("removed",this),this.emit("childRemoved",e,this,s)}return t[0]}removeChildAt(t){const e=this.getChildAt(t);return e.parent=null,e.transform._parentID=-1,Oe(this.children,t,1),this._boundsID++,this.onChildrenChange(t),e.emit("removed",this),this.emit("childRemoved",e,this,t),e}removeChildren(t=0,e=this.children.length){const s=t,i=e,n=i-s;let a;if(n>0&&n<=i){a=this.children.splice(s,n);for(let o=0;o<a.length;++o)a[o].parent=null,a[o].transform&&(a[o].transform._parentID=-1);this._boundsID++,this.onChildrenChange(t);for(let o=0;o<a.length;++o)a[o].emit("removed",this),this.emit("childRemoved",a[o],this,o);return a}else if(n===0&&this.children.length===0)return[];throw new RangeError("removeChildren: numeric values are outside the acceptable range.")}sortChildren(){let t=!1;for(let e=0,s=this.children.length;e<s;++e){const i=this.children[e];i._lastSortedIndex=e,!t&&i.zIndex!==0&&(t=!0)}t&&this.children.length>1&&this.children.sort(bf),this.sortDirty=!1}updateTransform(){this.sortableChildren&&this.sortDirty&&this.sortChildren(),this._boundsID++,this.transform.updateTransform(this.parent.transform),this.worldAlpha=this.alpha*this.parent.worldAlpha;for(let t=0,e=this.children.length;t<e;++t){const s=this.children[t];s.visible&&s.updateTransform()}}calculateBounds(){this._bounds.clear(),this._calculateBounds();for(let t=0;t<this.children.length;t++){const e=this.children[t];if(!(!e.visible||!e.renderable))if(e.calculateBounds(),e._mask){const s=e._mask.isMaskData?e._mask.maskObject:e._mask;s?(s.calculateBounds(),this._bounds.addBoundsMask(e._bounds,s._bounds)):this._bounds.addBounds(e._bounds)}else e.filterArea?this._bounds.addBoundsArea(e._bounds,e.filterArea):this._bounds.addBounds(e._bounds)}this._bounds.updateID=this._boundsID}getLocalBounds(t,e=!1){const s=super.getLocalBounds(t);if(!e)for(let i=0,n=this.children.length;i<n;++i){const a=this.children[i];a.visible&&a.updateTransform()}return s}_calculateBounds(){}_renderWithCulling(t){const e=t.renderTexture.sourceFrame;if(!(e.width>0&&e.height>0))return;let s,i;this.cullArea?(s=this.cullArea,i=this.worldTransform):this._render!==Ra.prototype._render&&(s=this.getBounds(!0));const n=t.projection.transform;if(n&&(i?(i=xf.copyFrom(i),i.prepend(n)):i=n),s&&e.intersects(s,i))this._render(t);else if(this.cullArea)return;for(let a=0,o=this.children.length;a<o;++a){const h=this.children[a],l=h.cullable;h.cullable=l||!this.cullArea,h.render(t),h.cullable=l}}render(t){var e;if(!(!this.visible||this.worldAlpha<=0||!this.renderable))if(this._mask||(e=this.filters)!=null&&e.length)this.renderAdvanced(t);else if(this.cullable)this._renderWithCulling(t);else{this._render(t);for(let s=0,i=this.children.length;s<i;++s)this.children[s].render(t)}}renderAdvanced(t){var e,s,i;const n=this.filters,a=this._mask;if(n){this._enabledFilters||(this._enabledFilters=[]),this._enabledFilters.length=0;for(let h=0;h<n.length;h++)n[h].enabled&&this._enabledFilters.push(n[h])}const o=n&&((e=this._enabledFilters)==null?void 0:e.length)||a&&(!a.isMaskData||a.enabled&&(a.autoDetect||a.type!==pt.NONE));if(o&&t.batch.flush(),n&&(s=this._enabledFilters)!=null&&s.length&&t.filter.push(this,this._enabledFilters),a&&t.mask.push(this,this._mask),this.cullable)this._renderWithCulling(t);else{this._render(t);for(let h=0,l=this.children.length;h<l;++h)this.children[h].render(t)}o&&t.batch.flush(),a&&t.mask.pop(this),n&&(i=this._enabledFilters)!=null&&i.length&&t.filter.pop()}_render(t){}destroy(t){super.destroy(),this.sortDirty=!1;const e=typeof t=="boolean"?t:t==null?void 0:t.children,s=this.removeChildren(0,this.children.length);if(e)for(let i=0;i<s.length;++i)s[i].destroy(t)}get width(){return this.scale.x*this.getLocalBounds().width}set width(t){const e=this.getLocalBounds().width;e!==0?this.scale.x=t/e:this.scale.x=1,this._width=t}get height(){return this.scale.y*this.getLocalBounds().height}set height(t){const e=this.getLocalBounds().height;e!==0?this.scale.y=t/e:this.scale.y=1,this._height=t}};Fh.defaultSortableChildren=!1;let Ct=Fh;Ct.prototype.containerUpdateTransform=Ct.prototype.updateTransform,Object.defineProperties(N,{SORTABLE_CHILDREN:{get(){return Ct.defaultSortableChildren},set(r){Ct.defaultSortableChildren=r}}});const Us=new K,Tf=new Uint16Array([0,1,2,0,2,3]);class Ut extends Ct{constructor(t){super(),this._anchor=new pe(this._onAnchorUpdate,this,t?t.defaultAnchor.x:0,t?t.defaultAnchor.y:0),this._texture=null,this._width=0,this._height=0,this._tintColor=new Y(16777215),this._tintRGB=null,this.tint=16777215,this.blendMode=C.NORMAL,this._cachedTint=16777215,this.uvs=null,this.texture=t||L.EMPTY,this.vertexData=new Float32Array(8),this.vertexTrimmedData=null,this._transformID=-1,this._textureID=-1,this._transformTrimmedID=-1,this._textureTrimmedID=-1,this.indices=Tf,this.pluginName="batch",this.isSprite=!0,this._roundPixels=N.ROUND_PIXELS}_onTextureUpdate(){this._textureID=-1,this._textureTrimmedID=-1,this._cachedTint=16777215,this._width&&(this.scale.x=de(this.scale.x)*this._width/this._texture.orig.width),this._height&&(this.scale.y=de(this.scale.y)*this._height/this._texture.orig.height)}_onAnchorUpdate(){this._transformID=-1,this._transformTrimmedID=-1}calculateVertices(){const t=this._texture;if(this._transformID===this.transform._worldID&&this._textureID===t._updateID)return;this._textureID!==t._updateID&&(this.uvs=this._texture._uvs.uvsFloat32),this._transformID=this.transform._worldID,this._textureID=t._updateID;const e=this.transform.worldTransform,s=e.a,i=e.b,n=e.c,a=e.d,o=e.tx,h=e.ty,l=this.vertexData,u=t.trim,c=t.orig,d=this._anchor;let f=0,p=0,m=0,g=0;if(u?(p=u.x-d._x*c.width,f=p+u.width,g=u.y-d._y*c.height,m=g+u.height):(p=-d._x*c.width,f=p+c.width,g=-d._y*c.height,m=g+c.height),l[0]=s*p+n*g+o,l[1]=a*g+i*p+h,l[2]=s*f+n*g+o,l[3]=a*g+i*f+h,l[4]=s*f+n*m+o,l[5]=a*m+i*f+h,l[6]=s*p+n*m+o,l[7]=a*m+i*p+h,this._roundPixels){const _=N.RESOLUTION;for(let x=0;x<l.length;++x)l[x]=Math.round(l[x]*_)/_}}calculateTrimmedVertices(){if(!this.vertexTrimmedData)this.vertexTrimmedData=new Float32Array(8);else if(this._transformTrimmedID===this.transform._worldID&&this._textureTrimmedID===this._texture._updateID)return;this._transformTrimmedID=this.transform._worldID,this._textureTrimmedID=this._texture._updateID;const t=this._texture,e=this.vertexTrimmedData,s=t.orig,i=this._anchor,n=this.transform.worldTransform,a=n.a,o=n.b,h=n.c,l=n.d,u=n.tx,c=n.ty,d=-i._x*s.width,f=d+s.width,p=-i._y*s.height,m=p+s.height;if(e[0]=a*d+h*p+u,e[1]=l*p+o*d+c,e[2]=a*f+h*p+u,e[3]=l*p+o*f+c,e[4]=a*f+h*m+u,e[5]=l*m+o*f+c,e[6]=a*d+h*m+u,e[7]=l*m+o*d+c,this._roundPixels){const g=N.RESOLUTION;for(let _=0;_<e.length;++_)e[_]=Math.round(e[_]*g)/g}}_render(t){this.calculateVertices(),t.batch.setObjectRenderer(t.plugins[this.pluginName]),t.plugins[this.pluginName].render(this)}_calculateBounds(){const t=this._texture.trim,e=this._texture.orig;!t||t.width===e.width&&t.height===e.height?(this.calculateVertices(),this._bounds.addQuad(this.vertexData)):(this.calculateTrimmedVertices(),this._bounds.addQuad(this.vertexTrimmedData))}getLocalBounds(t){return this.children.length===0?(this._localBounds||(this._localBounds=new Ls),this._localBounds.minX=this._texture.orig.width*-this._anchor._x,this._localBounds.minY=this._texture.orig.height*-this._anchor._y,this._localBounds.maxX=this._texture.orig.width*(1-this._anchor._x),this._localBounds.maxY=this._texture.orig.height*(1-this._anchor._y),t||(this._localBoundsRect||(this._localBoundsRect=new z),t=this._localBoundsRect),this._localBounds.getRectangle(t)):super.getLocalBounds.call(this,t)}containsPoint(t){this.worldTransform.applyInverse(t,Us);const e=this._texture.orig.width,s=this._texture.orig.height,i=-e*this.anchor.x;let n=0;return Us.x>=i&&Us.x<i+e&&(n=-s*this.anchor.y,Us.y>=n&&Us.y<n+s)}destroy(t){if(super.destroy(t),this._texture.off("update",this._onTextureUpdate,this),this._anchor=null,typeof t=="boolean"?t:t==null?void 0:t.texture){const e=typeof t=="boolean"?t:t==null?void 0:t.baseTexture;this._texture.destroy(!!e)}this._texture=null}static from(t,e){const s=t instanceof L?t:L.from(t,e);return new Ut(s)}set roundPixels(t){this._roundPixels!==t&&(this._transformID=-1,this._transformTrimmedID=-1),this._roundPixels=t}get roundPixels(){return this._roundPixels}get width(){return Math.abs(this.scale.x)*this._texture.orig.width}set width(t){const e=de(this.scale.x)||1;this.scale.x=e*t/this._texture.orig.width,this._width=t}get height(){return Math.abs(this.scale.y)*this._texture.orig.height}set height(t){const e=de(this.scale.y)||1;this.scale.y=e*t/this._texture.orig.height,this._height=t}get anchor(){return this._anchor}set anchor(t){this._anchor.copyFrom(t)}get tint(){return this._tintColor.value}set tint(t){this._tintColor.setValue(t),this._tintRGB=this._tintColor.toLittleEndianNumber()}get tintValue(){return this._tintColor.toNumber()}get texture(){return this._texture}set texture(t){this._texture!==t&&(this._texture&&this._texture.off("update",this._onTextureUpdate,this),this._texture=t||L.EMPTY,this._cachedTint=16777215,this._textureID=-1,this._textureTrimmedID=-1,t&&(t.baseTexture.valid?this._onTextureUpdate():t.once("update",this._onTextureUpdate,this)))}}const ks=new K;class qr extends Ut{constructor(t,e=100,s=100){super(t),this.tileTransform=new Dr,this._width=e,this._height=s,this.uvMatrix=this.texture.uvMatrix||new $r(t),this.pluginName="tilingSprite",this.uvRespectAnchor=!1}get clampMargin(){return this.uvMatrix.clampMargin}set clampMargin(t){this.uvMatrix.clampMargin=t,this.uvMatrix.update(!0)}get tileScale(){return this.tileTransform.scale}set tileScale(t){this.tileTransform.scale.copyFrom(t)}get tilePosition(){return this.tileTransform.position}set tilePosition(t){this.tileTransform.position.copyFrom(t)}_onTextureUpdate(){this.uvMatrix&&(this.uvMatrix.texture=this._texture),this._cachedTint=16777215}_render(t){const e=this._texture;!e||!e.valid||(this.tileTransform.updateLocalTransform(),this.uvMatrix.update(),t.batch.setObjectRenderer(t.plugins[this.pluginName]),t.plugins[this.pluginName].render(this))}_calculateBounds(){const t=this._width*-this._anchor._x,e=this._height*-this._anchor._y,s=this._width*(1-this._anchor._x),i=this._height*(1-this._anchor._y);this._bounds.addFrame(this.transform,t,e,s,i)}getLocalBounds(t){return this.children.length===0?(this._bounds.minX=this._width*-this._anchor._x,this._bounds.minY=this._height*-this._anchor._y,this._bounds.maxX=this._width*(1-this._anchor._x),this._bounds.maxY=this._height*(1-this._anchor._y),t||(this._localBoundsRect||(this._localBoundsRect=new z),t=this._localBoundsRect),this._bounds.getRectangle(t)):super.getLocalBounds.call(this,t)}containsPoint(t){this.worldTransform.applyInverse(t,ks);const e=this._width,s=this._height,i=-e*this.anchor._x;if(ks.x>=i&&ks.x<i+e){const n=-s*this.anchor._y;if(ks.y>=n&&ks.y<n+s)return!0}return!1}destroy(t){super.destroy(t),this.tileTransform=null,this.uvMatrix=null}static from(t,e){const s=t instanceof L?t:L.from(t,e);return new qr(s,e.width,e.height)}get width(){return this._width}set width(t){this._width=t}get height(){return this._height}set height(t){this._height=t}}var Ef=`#version 300 es
#define SHADER_NAME Tiling-Sprite-100

precision lowp float;

in vec2 vTextureCoord;

out vec4 fragmentColor;

uniform sampler2D uSampler;
uniform vec4 uColor;
uniform mat3 uMapCoord;
uniform vec4 uClampFrame;
uniform vec2 uClampOffset;

void main(void)
{
    vec2 coord = vTextureCoord + ceil(uClampOffset - vTextureCoord);
    coord = (uMapCoord * vec3(coord, 1.0)).xy;
    vec2 unclamped = coord;
    coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);

    vec4 texSample = texture(uSampler, coord, unclamped == coord ? 0.0f : -32.0f);// lod-bias very negative to force lod 0

    fragmentColor = texSample * uColor;
}
`,Af=`#version 300 es
#define SHADER_NAME Tiling-Sprite-300

precision lowp float;

in vec2 aVertexPosition;
in vec2 aTextureCoord;

uniform mat3 projectionMatrix;
uniform mat3 translationMatrix;
uniform mat3 uTransform;

out vec2 vTextureCoord;

void main(void)
{
    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = (uTransform * vec3(aTextureCoord, 1.0)).xy;
}
`,wf=`#version 100
#ifdef GL_EXT_shader_texture_lod
    #extension GL_EXT_shader_texture_lod : enable
#endif
#define SHADER_NAME Tiling-Sprite-100

precision lowp float;

varying vec2 vTextureCoord;

uniform sampler2D uSampler;
uniform vec4 uColor;
uniform mat3 uMapCoord;
uniform vec4 uClampFrame;
uniform vec2 uClampOffset;

void main(void)
{
    vec2 coord = vTextureCoord + ceil(uClampOffset - vTextureCoord);
    coord = (uMapCoord * vec3(coord, 1.0)).xy;
    vec2 unclamped = coord;
    coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);

    #ifdef GL_EXT_shader_texture_lod
        vec4 texSample = unclamped == coord
            ? texture2D(uSampler, coord) 
            : texture2DLodEXT(uSampler, coord, 0);
    #else
        vec4 texSample = texture2D(uSampler, coord);
    #endif

    gl_FragColor = texSample * uColor;
}
`,Nh=`#version 100
#define SHADER_NAME Tiling-Sprite-100

precision lowp float;

attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;

uniform mat3 projectionMatrix;
uniform mat3 translationMatrix;
uniform mat3 uTransform;

varying vec2 vTextureCoord;

void main(void)
{
    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = (uTransform * vec3(aTextureCoord, 1.0)).xy;
}
`,Sf=`#version 100
#define SHADER_NAME Tiling-Sprite-Simple-100

precision lowp float;

varying vec2 vTextureCoord;

uniform sampler2D uSampler;
uniform vec4 uColor;

void main(void)
{
    vec4 texSample = texture2D(uSampler, vTextureCoord);
    gl_FragColor = texSample * uColor;
}
`;const Kr=new tt;class zn extends Cs{constructor(t){super(t),t.runners.contextChange.add(this),this.quad=new mn,this.state=ee.for2d()}contextChange(){const t=this.renderer,e={globals:t.globalUniforms};this.simpleShader=Wt.from(Nh,Sf,e),this.shader=t.context.webGLVersion>1?Wt.from(Af,Ef,e):Wt.from(Nh,wf,e)}render(t){const e=this.renderer,s=this.quad;let i=s.vertices;i[0]=i[6]=t._width*-t.anchor.x,i[1]=i[3]=t._height*-t.anchor.y,i[2]=i[4]=t._width*(1-t.anchor.x),i[5]=i[7]=t._height*(1-t.anchor.y);const n=t.uvRespectAnchor?t.anchor.x:0,a=t.uvRespectAnchor?t.anchor.y:0;i=s.uvs,i[0]=i[6]=-n,i[1]=i[3]=-a,i[2]=i[4]=1-n,i[5]=i[7]=1-a,s.invalidate();const o=t._texture,h=o.baseTexture,l=h.alphaMode>0,u=t.tileTransform.localTransform,c=t.uvMatrix;let d=h.isPowerOfTwo&&o.frame.width===h.width&&o.frame.height===h.height;d&&(h._glTextures[e.CONTEXT_UID]?d=h.wrapMode!==Zt.CLAMP:h.wrapMode===Zt.CLAMP&&(h.wrapMode=Zt.REPEAT));const f=d?this.simpleShader:this.shader,p=o.width,m=o.height,g=t._width,_=t._height;Kr.set(u.a*p/g,u.b*p/_,u.c*m/g,u.d*m/_,u.tx/g,u.ty/_),Kr.invert(),d?Kr.prepend(c.mapCoord):(f.uniforms.uMapCoord=c.mapCoord.toArray(!0),f.uniforms.uClampFrame=c.uClampFrame,f.uniforms.uClampOffset=c.uClampOffset),f.uniforms.uTransform=Kr.toArray(!0),f.uniforms.uColor=Y.shared.setValue(t.tint).premultiply(t.worldAlpha,l).toArray(f.uniforms.uColor),f.uniforms.translationMatrix=t.transform.worldTransform.toArray(!0),f.uniforms.uSampler=o,e.shader.bind(f),e.geometry.bind(s),this.state.blendMode=Vi(t.blendMode,l),e.state.set(this.state),e.geometry.draw(this.renderer.gl.TRIANGLES,6,0)}}zn.extension={name:"tilingSprite",type:D.RendererPlugin},U.add(zn);const Zr=new tt,Gs=new tt,ie=[new K,new K,new K,new K];qr.prototype._renderCanvas=function(r){const t=this._texture;if(!t.baseTexture.valid)return;const e=r.canvasContext.activeContext,s=this.worldTransform,i=t.baseTexture,n=i.getDrawableSource(),a=i.resolution;if(this._textureID!==this._texture._updateID||this._cachedTint!==this.tintValue){this._textureID=this._texture._updateID;const d=new bs(t._frame.width,t._frame.height,a);this.tintValue!==16777215?(this._tintedCanvas=_t.getTintedCanvas(this,this.tintValue),d.context.drawImage(this._tintedCanvas,0,0)):d.context.drawImage(n,-t._frame.x*a,-t._frame.y*a),this._cachedTint=this.tintValue,this._canvasPattern=d.context.createPattern(d.canvas,"repeat")}e.globalAlpha=this.worldAlpha,r.canvasContext.setBlendMode(this.blendMode),this.tileTransform.updateLocalTransform();const o=this.tileTransform.localTransform,h=this._width,l=this._height;Zr.identity(),Gs.copyFrom(o),this.uvRespectAnchor||Gs.translate(-this.anchor.x*h,-this.anchor.y*l),Gs.scale(1/a,1/a),Zr.prepend(Gs),Zr.prepend(s),r.canvasContext.setContextTransform(Zr),e.fillStyle=this._canvasPattern;const u=this.anchor.x*-h,c=this.anchor.y*-l;ie[0].set(u,c),ie[1].set(u+h,c),ie[2].set(u+h,c+l),ie[3].set(u,c+l);for(let d=0;d<4;d++)Gs.applyInverse(ie[d],ie[d]);e.beginPath(),e.moveTo(ie[0].x,ie[0].y);for(let d=1;d<4;d++)e.lineTo(ie[d].x,ie[d].y);e.closePath(),e.fill()};class Lh extends Ct{constructor(t=1500,e,s=16384,i=!1){super();const n=16384;s>n&&(s=n),this._properties=[!1,!0,!1,!1,!1],this._maxSize=t,this._batchSize=s,this._buffers=null,this._bufferUpdateIDs=[],this._updateID=0,this.interactiveChildren=!1,this.blendMode=C.NORMAL,this.autoResize=i,this.roundPixels=!0,this.baseTexture=null,this.setProperties(e),this._tintColor=new Y(0),this.tintRgb=new Float32Array(3),this.tint=16777215}setProperties(t){t&&(this._properties[0]="vertices"in t||"scale"in t?!!t.vertices||!!t.scale:this._properties[0],this._properties[1]="position"in t?!!t.position:this._properties[1],this._properties[2]="rotation"in t?!!t.rotation:this._properties[2],this._properties[3]="uvs"in t?!!t.uvs:this._properties[3],this._properties[4]="tint"in t||"alpha"in t?!!t.tint||!!t.alpha:this._properties[4])}updateTransform(){this.displayObjectUpdateTransform()}get tint(){return this._tintColor.value}set tint(t){this._tintColor.setValue(t),this._tintColor.toRgbArray(this.tintRgb)}render(t){!this.visible||this.worldAlpha<=0||!this.children.length||!this.renderable||(this.baseTexture||(this.baseTexture=this.children[0]._texture.baseTexture,this.baseTexture.valid||this.baseTexture.once("update",()=>this.onChildrenChange(0))),t.batch.setObjectRenderer(t.plugins.particle),t.plugins.particle.render(this))}onChildrenChange(t){const e=Math.floor(t/this._batchSize);for(;this._bufferUpdateIDs.length<e;)this._bufferUpdateIDs.push(0);this._bufferUpdateIDs[e]=++this._updateID}dispose(){if(this._buffers){for(let t=0;t<this._buffers.length;++t)this._buffers[t].destroy();this._buffers=null}}destroy(t){super.destroy(t),this.dispose(),this._properties=null,this._buffers=null,this._bufferUpdateIDs=null}}class Uh{constructor(t,e,s){this.geometry=new fe,this.indexBuffer=null,this.size=s,this.dynamicProperties=[],this.staticProperties=[];for(let i=0;i<t.length;++i){let n=t[i];n={attributeName:n.attributeName,size:n.size,uploadFunction:n.uploadFunction,type:n.type||$.FLOAT,offset:n.offset},e[i]?this.dynamicProperties.push(n):this.staticProperties.push(n)}this.staticStride=0,this.staticBuffer=null,this.staticData=null,this.staticDataUint32=null,this.dynamicStride=0,this.dynamicBuffer=null,this.dynamicData=null,this.dynamicDataUint32=null,this._updateID=0,this.initBuffers()}initBuffers(){const t=this.geometry;let e=0;this.indexBuffer=new dt(So(this.size),!0,!0),t.addIndex(this.indexBuffer),this.dynamicStride=0;for(let a=0;a<this.dynamicProperties.length;++a){const o=this.dynamicProperties[a];o.offset=e,e+=o.size,this.dynamicStride+=o.size}const s=new ArrayBuffer(this.size*this.dynamicStride*4*4);this.dynamicData=new Float32Array(s),this.dynamicDataUint32=new Uint32Array(s),this.dynamicBuffer=new dt(this.dynamicData,!1,!1);let i=0;this.staticStride=0;for(let a=0;a<this.staticProperties.length;++a){const o=this.staticProperties[a];o.offset=i,i+=o.size,this.staticStride+=o.size}const n=new ArrayBuffer(this.size*this.staticStride*4*4);this.staticData=new Float32Array(n),this.staticDataUint32=new Uint32Array(n),this.staticBuffer=new dt(this.staticData,!0,!1);for(let a=0;a<this.dynamicProperties.length;++a){const o=this.dynamicProperties[a];t.addAttribute(o.attributeName,this.dynamicBuffer,0,o.type===$.UNSIGNED_BYTE,o.type,this.dynamicStride*4,o.offset*4)}for(let a=0;a<this.staticProperties.length;++a){const o=this.staticProperties[a];t.addAttribute(o.attributeName,this.staticBuffer,0,o.type===$.UNSIGNED_BYTE,o.type,this.staticStride*4,o.offset*4)}}uploadDynamic(t,e,s){for(let i=0;i<this.dynamicProperties.length;i++){const n=this.dynamicProperties[i];n.uploadFunction(t,e,s,n.type===$.UNSIGNED_BYTE?this.dynamicDataUint32:this.dynamicData,this.dynamicStride,n.offset)}this.dynamicBuffer._updateID++}uploadStatic(t,e,s){for(let i=0;i<this.staticProperties.length;i++){const n=this.staticProperties[i];n.uploadFunction(t,e,s,n.type===$.UNSIGNED_BYTE?this.staticDataUint32:this.staticData,this.staticStride,n.offset)}this.staticBuffer._updateID++}destroy(){this.indexBuffer=null,this.dynamicProperties=null,this.dynamicBuffer=null,this.dynamicData=null,this.dynamicDataUint32=null,this.staticProperties=null,this.staticBuffer=null,this.staticData=null,this.staticDataUint32=null,this.geometry.destroy()}}var Cf=`varying vec2 vTextureCoord;
varying vec4 vColor;

uniform sampler2D uSampler;

void main(void){
    vec4 color = texture2D(uSampler, vTextureCoord) * vColor;
    gl_FragColor = color;
}`,Rf=`attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;
attribute vec4 aColor;

attribute vec2 aPositionCoord;
attribute float aRotation;

uniform mat3 translationMatrix;
uniform vec4 uColor;

varying vec2 vTextureCoord;
varying vec4 vColor;

void main(void){
    float x = (aVertexPosition.x) * cos(aRotation) - (aVertexPosition.y) * sin(aRotation);
    float y = (aVertexPosition.x) * sin(aRotation) + (aVertexPosition.y) * cos(aRotation);

    vec2 v = vec2(x, y);
    v = v + aPositionCoord;

    gl_Position = vec4((translationMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = aTextureCoord;
    vColor = aColor * uColor;
}
`;class Wn extends Cs{constructor(t){super(t),this.shader=null,this.properties=null,this.tempMatrix=new tt,this.properties=[{attributeName:"aVertexPosition",size:2,uploadFunction:this.uploadVertices,offset:0},{attributeName:"aPositionCoord",size:2,uploadFunction:this.uploadPosition,offset:0},{attributeName:"aRotation",size:1,uploadFunction:this.uploadRotation,offset:0},{attributeName:"aTextureCoord",size:2,uploadFunction:this.uploadUvs,offset:0},{attributeName:"aColor",size:1,type:$.UNSIGNED_BYTE,uploadFunction:this.uploadTint,offset:0}],this.shader=Wt.from(Rf,Cf,{}),this.state=ee.for2d()}render(t){const e=t.children,s=t._maxSize,i=t._batchSize,n=this.renderer;let a=e.length;if(a===0)return;a>s&&!t.autoResize&&(a=s);let o=t._buffers;o||(o=t._buffers=this.generateBuffers(t));const h=e[0]._texture.baseTexture,l=h.alphaMode>0;this.state.blendMode=Vi(t.blendMode,l),n.state.set(this.state);const u=n.gl,c=t.worldTransform.copyTo(this.tempMatrix);c.prepend(n.globalUniforms.uniforms.projectionMatrix),this.shader.uniforms.translationMatrix=c.toArray(!0),this.shader.uniforms.uColor=Y.shared.setValue(t.tintRgb).premultiply(t.worldAlpha,l).toArray(this.shader.uniforms.uColor),this.shader.uniforms.uSampler=h,this.renderer.shader.bind(this.shader);let d=!1;for(let f=0,p=0;f<a;f+=i,p+=1){let m=a-f;m>i&&(m=i),p>=o.length&&o.push(this._generateOneMoreBuffer(t));const g=o[p];g.uploadDynamic(e,f,m);const _=t._bufferUpdateIDs[p]||0;d=d||g._updateID<_,d&&(g._updateID=t._updateID,g.uploadStatic(e,f,m)),n.geometry.bind(g.geometry),u.drawElements(u.TRIANGLES,m*6,u.UNSIGNED_SHORT,0)}}generateBuffers(t){const e=[],s=t._maxSize,i=t._batchSize,n=t._properties;for(let a=0;a<s;a+=i)e.push(new Uh(this.properties,n,i));return e}_generateOneMoreBuffer(t){const e=t._batchSize,s=t._properties;return new Uh(this.properties,s,e)}uploadVertices(t,e,s,i,n,a){let o=0,h=0,l=0,u=0;for(let c=0;c<s;++c){const d=t[e+c],f=d._texture,p=d.scale.x,m=d.scale.y,g=f.trim,_=f.orig;g?(h=g.x-d.anchor.x*_.width,o=h+g.width,u=g.y-d.anchor.y*_.height,l=u+g.height):(o=_.width*(1-d.anchor.x),h=_.width*-d.anchor.x,l=_.height*(1-d.anchor.y),u=_.height*-d.anchor.y),i[a]=h*p,i[a+1]=u*m,i[a+n]=o*p,i[a+n+1]=u*m,i[a+n*2]=o*p,i[a+n*2+1]=l*m,i[a+n*3]=h*p,i[a+n*3+1]=l*m,a+=n*4}}uploadPosition(t,e,s,i,n,a){for(let o=0;o<s;o++){const h=t[e+o].position;i[a]=h.x,i[a+1]=h.y,i[a+n]=h.x,i[a+n+1]=h.y,i[a+n*2]=h.x,i[a+n*2+1]=h.y,i[a+n*3]=h.x,i[a+n*3+1]=h.y,a+=n*4}}uploadRotation(t,e,s,i,n,a){for(let o=0;o<s;o++){const h=t[e+o].rotation;i[a]=h,i[a+n]=h,i[a+n*2]=h,i[a+n*3]=h,a+=n*4}}uploadUvs(t,e,s,i,n,a){for(let o=0;o<s;++o){const h=t[e+o]._texture._uvs;h?(i[a]=h.x0,i[a+1]=h.y0,i[a+n]=h.x1,i[a+n+1]=h.y1,i[a+n*2]=h.x2,i[a+n*2+1]=h.y2,i[a+n*3]=h.x3,i[a+n*3+1]=h.y3,a+=n*4):(i[a]=0,i[a+1]=0,i[a+n]=0,i[a+n+1]=0,i[a+n*2]=0,i[a+n*2+1]=0,i[a+n*3]=0,i[a+n*3+1]=0,a+=n*4)}}uploadTint(t,e,s,i,n,a){for(let o=0;o<s;++o){const h=t[e+o],l=Y.shared.setValue(h._tintRGB).toPremultiplied(h.alpha,h.texture.baseTexture.alphaMode>0);i[a]=l,i[a+n]=l,i[a+n*2]=l,i[a+n*3]=l,a+=n*4}}destroy(){super.destroy(),this.shader&&(this.shader.destroy(),this.shader=null),this.tempMatrix=null}}Wn.extension={name:"particle",type:D.RendererPlugin},U.add(Wn),Lh.prototype.renderCanvas=function(r){if(!this.visible||this.worldAlpha<=0||!this.children.length||!this.renderable)return;const t=r.canvasContext.activeContext,e=this.worldTransform;let s=!0,i=0,n=0,a=0,o=0;r.canvasContext.setBlendMode(this.blendMode),t.globalAlpha=this.worldAlpha,this.displayObjectUpdateTransform();for(let h=0;h<this.children.length;++h){const l=this.children[h];if(!l.visible||!l._texture.valid)continue;const u=l._texture.frame;if(t.globalAlpha=this.worldAlpha*l.alpha,l.rotation%(Math.PI*2)===0)s&&(r.canvasContext.setContextTransform(e,!1,1),s=!1),i=l.anchor.x*(-u.width*l.scale.x)+l.position.x+.5,n=l.anchor.y*(-u.height*l.scale.y)+l.position.y+.5,a=u.width*l.scale.x,o=u.height*l.scale.y;else{s||(s=!0),l.displayObjectUpdateTransform();const f=l.worldTransform;r.canvasContext.setContextTransform(f,this.roundPixels,1),i=l.anchor.x*-u.width+.5,n=l.anchor.y*-u.height+.5,a=u.width,o=u.height}const c=l._texture.baseTexture.resolution,d=r.canvasContext.activeResolution;t.drawImage(l._texture.baseTexture.getDrawableSource(),u.x*c,u.y*c,u.width*c,u.height*c,i*d,n*d,a*d,o*d)}},Ct.prototype._renderCanvas=function(r){},Ct.prototype.renderCanvas=function(r){if(!(!this.visible||this.worldAlpha<=0||!this.renderable)){this._mask&&r.mask.pushMask(this._mask),this._renderCanvas(r);for(let t=0,e=this.children.length;t<e;++t)this.children[t].renderCanvas(r);this._mask&&r.mask.popMask(r)}},ot.prototype.renderCanvas=function(r){};var $s=(r=>(r[r.LINEAR_VERTICAL=0]="LINEAR_VERTICAL",r[r.LINEAR_HORIZONTAL=1]="LINEAR_HORIZONTAL",r))($s||{});const Qr={willReadFrequently:!0},ne=class G{static get experimentalLetterSpacingSupported(){let t=G._experimentalLetterSpacingSupported;if(t!==void 0){const e=N.ADAPTER.getCanvasRenderingContext2D().prototype;t=G._experimentalLetterSpacingSupported="letterSpacing"in e||"textLetterSpacing"in e}return t}constructor(t,e,s,i,n,a,o,h,l){this.text=t,this.style=e,this.width=s,this.height=i,this.lines=n,this.lineWidths=a,this.lineHeight=o,this.maxLineWidth=h,this.fontProperties=l}static measureText(t,e,s,i=G._canvas){s=s==null?e.wordWrap:s;const n=e.toFontString(),a=G.measureFont(n);a.fontSize===0&&(a.fontSize=e.fontSize,a.ascent=e.fontSize);const o=i.getContext("2d",Qr);o.font=n;const h=(s?G.wordWrap(t,e,i):t).split(/(?:\r\n|\r|\n)/),l=new Array(h.length);let u=0;for(let p=0;p<h.length;p++){const m=G._measureText(h[p],e.letterSpacing,o);l[p]=m,u=Math.max(u,m)}let c=u+e.strokeThickness;e.dropShadow&&(c+=e.dropShadowDistance);const d=e.lineHeight||a.fontSize+e.strokeThickness;let f=Math.max(d,a.fontSize+e.strokeThickness*2)+e.leading+(h.length-1)*(d+e.leading);return e.dropShadow&&(f+=e.dropShadowDistance),new G(t,e,c,f,h,l,d+e.leading,u,a)}static _measureText(t,e,s){let i=!1;G.experimentalLetterSpacingSupported&&(G.experimentalLetterSpacing?(s.letterSpacing=`${e}px`,s.textLetterSpacing=`${e}px`,i=!0):(s.letterSpacing="0px",s.textLetterSpacing="0px"));let n=s.measureText(t).width;return n>0&&(i?n-=e:n+=(G.graphemeSegmenter(t).length-1)*e),n}static wordWrap(t,e,s=G._canvas){const i=s.getContext("2d",Qr);let n=0,a="",o="";const h=Object.create(null),{letterSpacing:l,whiteSpace:u}=e,c=G.collapseSpaces(u),d=G.collapseNewlines(u);let f=!c;const p=e.wordWrapWidth+l,m=G.tokenize(t);for(let g=0;g<m.length;g++){let _=m[g];if(G.isNewline(_)){if(!d){o+=G.addLine(a),f=!c,a="",n=0;continue}_=" "}if(c){const y=G.isBreakingSpace(_),b=G.isBreakingSpace(a[a.length-1]);if(y&&b)continue}const x=G.getFromCache(_,l,h,i);if(x>p)if(a!==""&&(o+=G.addLine(a),a="",n=0),G.canBreakWords(_,e.breakWords)){const y=G.wordWrapSplit(_);for(let b=0;b<y.length;b++){let T=y[b],S=T,A=1;for(;y[b+A];){const R=y[b+A];if(!G.canBreakChars(S,R,_,b,e.breakWords))T+=R;else break;S=R,A++}b+=A-1;const w=G.getFromCache(T,l,h,i);w+n>p&&(o+=G.addLine(a),f=!1,a="",n=0),a+=T,n+=w}}else{a.length>0&&(o+=G.addLine(a),a="",n=0);const y=g===m.length-1;o+=G.addLine(_,!y),f=!1,a="",n=0}else x+n>p&&(f=!1,o+=G.addLine(a),a="",n=0),(a.length>0||!G.isBreakingSpace(_)||f)&&(a+=_,n+=x)}return o+=G.addLine(a,!1),o}static addLine(t,e=!0){return t=G.trimRight(t),t=e?`${t}
`:t,t}static getFromCache(t,e,s,i){let n=s[t];return typeof n!="number"&&(n=G._measureText(t,e,i)+e,s[t]=n),n}static collapseSpaces(t){return t==="normal"||t==="pre-line"}static collapseNewlines(t){return t==="normal"}static trimRight(t){if(typeof t!="string")return"";for(let e=t.length-1;e>=0;e--){const s=t[e];if(!G.isBreakingSpace(s))break;t=t.slice(0,-1)}return t}static isNewline(t){return typeof t!="string"?!1:G._newlines.includes(t.charCodeAt(0))}static isBreakingSpace(t,e){return typeof t!="string"?!1:G._breakingSpaces.includes(t.charCodeAt(0))}static tokenize(t){const e=[];let s="";if(typeof t!="string")return e;for(let i=0;i<t.length;i++){const n=t[i],a=t[i+1];if(G.isBreakingSpace(n,a)||G.isNewline(n)){s!==""&&(e.push(s),s=""),e.push(n);continue}s+=n}return s!==""&&e.push(s),e}static canBreakWords(t,e){return e}static canBreakChars(t,e,s,i,n){return!0}static wordWrapSplit(t){return G.graphemeSegmenter(t)}static measureFont(t){if(G._fonts[t])return G._fonts[t];const e={ascent:0,descent:0,fontSize:0},s=G._canvas,i=G._context;i.font=t;const n=G.METRICS_STRING+G.BASELINE_SYMBOL,a=Math.ceil(i.measureText(n).width);let o=Math.ceil(i.measureText(G.BASELINE_SYMBOL).width);const h=Math.ceil(G.HEIGHT_MULTIPLIER*o);if(o=o*G.BASELINE_MULTIPLIER|0,a===0||h===0)return G._fonts[t]=e,e;s.width=a,s.height=h,i.fillStyle="#f00",i.fillRect(0,0,a,h),i.font=t,i.textBaseline="alphabetic",i.fillStyle="#000",i.fillText(n,0,o);const l=i.getImageData(0,0,a,h).data,u=l.length,c=a*4;let d=0,f=0,p=!1;for(d=0;d<o;++d){for(let m=0;m<c;m+=4)if(l[f+m]!==255){p=!0;break}if(!p)f+=c;else break}for(e.ascent=o-d,f=u-c,p=!1,d=h;d>o;--d){for(let m=0;m<c;m+=4)if(l[f+m]!==255){p=!0;break}if(!p)f-=c;else break}return e.descent=d-o,e.fontSize=e.ascent+e.descent,G._fonts[t]=e,e}static clearMetrics(t=""){t?delete G._fonts[t]:G._fonts={}}static get _canvas(){if(!G.__canvas){let t;try{const e=new OffscreenCanvas(0,0),s=e.getContext("2d",Qr);if(s!=null&&s.measureText)return G.__canvas=e,e;t=N.ADAPTER.createCanvas()}catch(e){t=N.ADAPTER.createCanvas()}t.width=t.height=10,G.__canvas=t}return G.__canvas}static get _context(){return G.__context||(G.__context=G._canvas.getContext("2d",Qr)),G.__context}};ne.METRICS_STRING="|\xC9q\xC5",ne.BASELINE_SYMBOL="M",ne.BASELINE_MULTIPLIER=1.4,ne.HEIGHT_MULTIPLIER=2,ne.graphemeSegmenter=(()=>{if(typeof(Intl==null?void 0:Intl.Segmenter)=="function"){const r=new Intl.Segmenter;return t=>[...r.segment(t)].map(e=>e.segment)}return r=>[...r]})(),ne.experimentalLetterSpacing=!1,ne._fonts={},ne._newlines=[10,13],ne._breakingSpaces=[9,32,8192,8193,8194,8195,8196,8197,8198,8200,8201,8202,8287,12288];let ge=ne;const If=["serif","sans-serif","monospace","cursive","fantasy","system-ui"],kh=class hr{constructor(t){this.styleID=0,this.reset(),qn(this,t,t)}clone(){const t={};return qn(t,this,hr.defaultStyle),new hr(t)}reset(){qn(this,hr.defaultStyle,hr.defaultStyle)}get align(){return this._align}set align(t){this._align!==t&&(this._align=t,this.styleID++)}get breakWords(){return this._breakWords}set breakWords(t){this._breakWords!==t&&(this._breakWords=t,this.styleID++)}get dropShadow(){return this._dropShadow}set dropShadow(t){this._dropShadow!==t&&(this._dropShadow=t,this.styleID++)}get dropShadowAlpha(){return this._dropShadowAlpha}set dropShadowAlpha(t){this._dropShadowAlpha!==t&&(this._dropShadowAlpha=t,this.styleID++)}get dropShadowAngle(){return this._dropShadowAngle}set dropShadowAngle(t){this._dropShadowAngle!==t&&(this._dropShadowAngle=t,this.styleID++)}get dropShadowBlur(){return this._dropShadowBlur}set dropShadowBlur(t){this._dropShadowBlur!==t&&(this._dropShadowBlur=t,this.styleID++)}get dropShadowColor(){return this._dropShadowColor}set dropShadowColor(t){const e=Yn(t);this._dropShadowColor!==e&&(this._dropShadowColor=e,this.styleID++)}get dropShadowDistance(){return this._dropShadowDistance}set dropShadowDistance(t){this._dropShadowDistance!==t&&(this._dropShadowDistance=t,this.styleID++)}get fill(){return this._fill}set fill(t){const e=Yn(t);this._fill!==e&&(this._fill=e,this.styleID++)}get fillGradientType(){return this._fillGradientType}set fillGradientType(t){this._fillGradientType!==t&&(this._fillGradientType=t,this.styleID++)}get fillGradientStops(){return this._fillGradientStops}set fillGradientStops(t){Pf(this._fillGradientStops,t)||(this._fillGradientStops=t,this.styleID++)}get fontFamily(){return this._fontFamily}set fontFamily(t){this.fontFamily!==t&&(this._fontFamily=t,this.styleID++)}get fontSize(){return this._fontSize}set fontSize(t){this._fontSize!==t&&(this._fontSize=t,this.styleID++)}get fontStyle(){return this._fontStyle}set fontStyle(t){this._fontStyle!==t&&(this._fontStyle=t,this.styleID++)}get fontVariant(){return this._fontVariant}set fontVariant(t){this._fontVariant!==t&&(this._fontVariant=t,this.styleID++)}get fontWeight(){return this._fontWeight}set fontWeight(t){this._fontWeight!==t&&(this._fontWeight=t,this.styleID++)}get letterSpacing(){return this._letterSpacing}set letterSpacing(t){this._letterSpacing!==t&&(this._letterSpacing=t,this.styleID++)}get lineHeight(){return this._lineHeight}set lineHeight(t){this._lineHeight!==t&&(this._lineHeight=t,this.styleID++)}get leading(){return this._leading}set leading(t){this._leading!==t&&(this._leading=t,this.styleID++)}get lineJoin(){return this._lineJoin}set lineJoin(t){this._lineJoin!==t&&(this._lineJoin=t,this.styleID++)}get miterLimit(){return this._miterLimit}set miterLimit(t){this._miterLimit!==t&&(this._miterLimit=t,this.styleID++)}get padding(){return this._padding}set padding(t){this._padding!==t&&(this._padding=t,this.styleID++)}get stroke(){return this._stroke}set stroke(t){const e=Yn(t);this._stroke!==e&&(this._stroke=e,this.styleID++)}get strokeThickness(){return this._strokeThickness}set strokeThickness(t){this._strokeThickness!==t&&(this._strokeThickness=t,this.styleID++)}get textBaseline(){return this._textBaseline}set textBaseline(t){this._textBaseline!==t&&(this._textBaseline=t,this.styleID++)}get trim(){return this._trim}set trim(t){this._trim!==t&&(this._trim=t,this.styleID++)}get whiteSpace(){return this._whiteSpace}set whiteSpace(t){this._whiteSpace!==t&&(this._whiteSpace=t,this.styleID++)}get wordWrap(){return this._wordWrap}set wordWrap(t){this._wordWrap!==t&&(this._wordWrap=t,this.styleID++)}get wordWrapWidth(){return this._wordWrapWidth}set wordWrapWidth(t){this._wordWrapWidth!==t&&(this._wordWrapWidth=t,this.styleID++)}toFontString(){const t=typeof this.fontSize=="number"?`${this.fontSize}px`:this.fontSize;let e=this.fontFamily;Array.isArray(this.fontFamily)||(e=this.fontFamily.split(","));for(let s=e.length-1;s>=0;s--){let i=e[s].trim();!/([\"\'])[^\'\"]+\1/.test(i)&&!If.includes(i)&&(i=`"${i}"`),e[s]=i}return`${this.fontStyle} ${this.fontVariant} ${this.fontWeight} ${t} ${e.join(",")}`}};kh.defaultStyle={align:"left",breakWords:!1,dropShadow:!1,dropShadowAlpha:1,dropShadowAngle:Math.PI/6,dropShadowBlur:0,dropShadowColor:"black",dropShadowDistance:5,fill:"black",fillGradientType:$s.LINEAR_VERTICAL,fillGradientStops:[],fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",leading:0,letterSpacing:0,lineHeight:0,lineJoin:"miter",miterLimit:10,padding:0,stroke:"black",strokeThickness:0,textBaseline:"alphabetic",trim:!1,whiteSpace:"pre",wordWrap:!1,wordWrapWidth:100};let _e=kh;function Yn(r){const t=Y.shared,e=s=>{const i=t.setValue(s);return i.alpha===1?i.toHex():i.toRgbaString()};return Array.isArray(r)?r.map(e):e(r)}function Pf(r,t){if(!Array.isArray(r)||!Array.isArray(t)||r.length!==t.length)return!1;for(let e=0;e<r.length;++e)if(r[e]!==t[e])return!1;return!0}function qn(r,t,e){for(const s in e)Array.isArray(t[s])?r[s]=t[s].slice():r[s]=t[s]}const Mf={texture:!0,children:!1,baseTexture:!0},Gh=class Ia extends Ut{constructor(t,e,s){var i;let n=!1;s||(s=N.ADAPTER.createCanvas(),n=!0),s.width=3,s.height=3;const a=L.from(s);a.orig=new z,a.trim=new z,super(a),this._ownCanvas=n,this.canvas=s,this.context=s.getContext("2d",{willReadFrequently:!0}),this._resolution=(i=Ia.defaultResolution)!=null?i:N.RESOLUTION,this._autoResolution=Ia.defaultAutoResolution,this._text=null,this._style=null,this._styleListener=null,this._font="",this.text=t,this.style=e,this.localStyleID=-1}static get experimentalLetterSpacing(){return ge.experimentalLetterSpacing}static set experimentalLetterSpacing(t){ge.experimentalLetterSpacing=t}updateText(t){const e=this._style;if(this.localStyleID!==e.styleID&&(this.dirty=!0,this.localStyleID=e.styleID),!this.dirty&&t)return;this._font=this._style.toFontString();const s=this.context,i=ge.measureText(this._text||" ",this._style,this._style.wordWrap,this.canvas),n=i.width,a=i.height,o=i.lines,h=i.lineHeight,l=i.lineWidths,u=i.maxLineWidth,c=i.fontProperties;this.canvas.width=Math.ceil(Math.ceil(Math.max(1,n)+e.padding*2)*this._resolution),this.canvas.height=Math.ceil(Math.ceil(Math.max(1,a)+e.padding*2)*this._resolution),s.scale(this._resolution,this._resolution),s.clearRect(0,0,this.canvas.width,this.canvas.height),s.font=this._font,s.lineWidth=e.strokeThickness,s.textBaseline=e.textBaseline,s.lineJoin=e.lineJoin,s.miterLimit=e.miterLimit;let d,f;const p=e.dropShadow?2:1;for(let m=0;m<p;++m){const g=e.dropShadow&&m===0,_=g?Math.ceil(Math.max(1,a)+e.padding*2):0,x=_*this._resolution;if(g){s.fillStyle="black",s.strokeStyle="black";const b=e.dropShadowColor,T=e.dropShadowBlur*this._resolution,S=e.dropShadowDistance*this._resolution;s.shadowColor=Y.shared.setValue(b).setAlpha(e.dropShadowAlpha).toRgbaString(),s.shadowBlur=T,s.shadowOffsetX=Math.cos(e.dropShadowAngle)*S,s.shadowOffsetY=Math.sin(e.dropShadowAngle)*S+x}else s.fillStyle=this._generateFillStyle(e,o,i),s.strokeStyle=e.stroke,s.shadowColor="black",s.shadowBlur=0,s.shadowOffsetX=0,s.shadowOffsetY=0;let y=(h-c.fontSize)/2;h-c.fontSize<0&&(y=0);for(let b=0;b<o.length;b++)d=e.strokeThickness/2,f=e.strokeThickness/2+b*h+c.ascent+y,e.align==="right"?d+=u-l[b]:e.align==="center"&&(d+=(u-l[b])/2),e.stroke&&e.strokeThickness&&this.drawLetterSpacing(o[b],d+e.padding,f+e.padding-_,!0),e.fill&&this.drawLetterSpacing(o[b],d+e.padding,f+e.padding-_)}this.updateTexture()}drawLetterSpacing(t,e,s,i=!1){const n=this._style.letterSpacing;let a=!1;if(ge.experimentalLetterSpacingSupported&&(ge.experimentalLetterSpacing?(this.context.letterSpacing=`${n}px`,this.context.textLetterSpacing=`${n}px`,a=!0):(this.context.letterSpacing="0px",this.context.textLetterSpacing="0px")),n===0||a){i?this.context.strokeText(t,e,s):this.context.fillText(t,e,s);return}let o=e;const h=ge.graphemeSegmenter(t);let l=this.context.measureText(t).width,u=0;for(let c=0;c<h.length;++c){const d=h[c];i?this.context.strokeText(d,o,s):this.context.fillText(d,o,s);let f="";for(let p=c+1;p<h.length;++p)f+=h[p];u=this.context.measureText(f).width,o+=l-u+n,l=u}}updateTexture(){const t=this.canvas;if(this._style.trim){const a=Po(t);a.data&&(t.width=a.width,t.height=a.height,this.context.putImageData(a.data,0,0))}const e=this._texture,s=this._style,i=s.trim?0:s.padding,n=e.baseTexture;e.trim.width=e._frame.width=t.width/this._resolution,e.trim.height=e._frame.height=t.height/this._resolution,e.trim.x=-i,e.trim.y=-i,e.orig.width=e._frame.width-i*2,e.orig.height=e._frame.height-i*2,this._onTextureUpdate(),n.setRealSize(t.width,t.height,this._resolution),e.updateUvs(),this.dirty=!1}_render(t){this._autoResolution&&this._resolution!==t.resolution&&(this._resolution=t.resolution,this.dirty=!0),this.updateText(!0),super._render(t)}updateTransform(){this.updateText(!0),super.updateTransform()}getBounds(t,e){return this.updateText(!0),this._textureID===-1&&(t=!1),super.getBounds(t,e)}getLocalBounds(t){return this.updateText(!0),super.getLocalBounds.call(this,t)}_calculateBounds(){this.calculateVertices(),this._bounds.addQuad(this.vertexData)}_generateFillStyle(t,e,s){const i=t.fill;if(Array.isArray(i)){if(i.length===1)return i[0]}else return i;let n;const a=t.dropShadow?t.dropShadowDistance:0,o=t.padding||0,h=this.canvas.width/this._resolution-a-o*2,l=this.canvas.height/this._resolution-a-o*2,u=i.slice(),c=t.fillGradientStops.slice();if(!c.length){const d=u.length+1;for(let f=1;f<d;++f)c.push(f/d)}if(u.unshift(i[0]),c.unshift(0),u.push(i[i.length-1]),c.push(1),t.fillGradientType===$s.LINEAR_VERTICAL){n=this.context.createLinearGradient(h/2,o,h/2,l+o);const d=s.fontProperties.fontSize+t.strokeThickness;for(let f=0;f<e.length;f++){const p=s.lineHeight*(f-1)+d,m=s.lineHeight*f;let g=m;f>0&&p>m&&(g=(m+p)/2);const _=m+d,x=s.lineHeight*(f+1);let y=_;f+1<e.length&&x<_&&(y=(_+x)/2);const b=(y-g)/l;for(let T=0;T<u.length;T++){let S=0;typeof c[T]=="number"?S=c[T]:S=T/u.length;let A=Math.min(1,Math.max(0,g/l+S*b));A=Number(A.toFixed(5)),n.addColorStop(A,u[T])}}}else{n=this.context.createLinearGradient(o,l/2,h+o,l/2);const d=u.length+1;let f=1;for(let p=0;p<u.length;p++){let m;typeof c[p]=="number"?m=c[p]:m=f/d,n.addColorStop(m,u[p]),f++}}return n}destroy(t){typeof t=="boolean"&&(t={children:t}),t=Object.assign({},Mf,t),super.destroy(t),this._ownCanvas&&(this.canvas.height=this.canvas.width=0),this.context=null,this.canvas=null,this._style=null}get width(){return this.updateText(!0),Math.abs(this.scale.x)*this._texture.orig.width}set width(t){this.updateText(!0);const e=de(this.scale.x)||1;this.scale.x=e*t/this._texture.orig.width,this._width=t}get height(){return this.updateText(!0),Math.abs(this.scale.y)*this._texture.orig.height}set height(t){this.updateText(!0);const e=de(this.scale.y)||1;this.scale.y=e*t/this._texture.orig.height,this._height=t}get style(){return this._style}set style(t){t=t||{},t instanceof _e?this._style=t:this._style=new _e(t),this.localStyleID=-1,this.dirty=!0}get text(){return this._text}set text(t){t=String(t==null?"":t),this._text!==t&&(this._text=t,this.dirty=!0)}get resolution(){return this._resolution}set resolution(t){this._autoResolution=!1,this._resolution!==t&&(this._resolution=t,this.dirty=!0)}};Gh.defaultAutoResolution=!0;let Jr=Gh;Jr.prototype._renderCanvas=function(r){this._autoResolution&&this._resolution!==r.resolution&&(this._resolution=r.resolution,this.dirty=!0),this.updateText(!0),Ut.prototype._renderCanvas.call(this,r)};const $h=new tt;ot.prototype._cacheAsBitmap=!1,ot.prototype._cacheData=null,ot.prototype._cacheAsBitmapResolution=null,ot.prototype._cacheAsBitmapMultisample=null;class Df{constructor(){this.textureCacheId=null,this.originalRender=null,this.originalRenderCanvas=null,this.originalCalculateBounds=null,this.originalGetLocalBounds=null,this.originalUpdateTransform=null,this.originalDestroy=null,this.originalMask=null,this.originalFilterArea=null,this.originalContainsPoint=null,this.sprite=null}}Object.defineProperties(ot.prototype,{cacheAsBitmapResolution:{get(){return this._cacheAsBitmapResolution},set(r){r!==this._cacheAsBitmapResolution&&(this._cacheAsBitmapResolution=r,this.cacheAsBitmap&&(this.cacheAsBitmap=!1,this.cacheAsBitmap=!0))}},cacheAsBitmapMultisample:{get(){return this._cacheAsBitmapMultisample},set(r){r!==this._cacheAsBitmapMultisample&&(this._cacheAsBitmapMultisample=r,this.cacheAsBitmap&&(this.cacheAsBitmap=!1,this.cacheAsBitmap=!0))}},cacheAsBitmap:{get(){return this._cacheAsBitmap},set(r){if(this._cacheAsBitmap===r)return;this._cacheAsBitmap=r;let t;r?(this._cacheData||(this._cacheData=new Df),t=this._cacheData,t.originalRender=this.render,t.originalRenderCanvas=this.renderCanvas,t.originalUpdateTransform=this.updateTransform,t.originalCalculateBounds=this.calculateBounds,t.originalGetLocalBounds=this.getLocalBounds,t.originalDestroy=this.destroy,t.originalContainsPoint=this.containsPoint,t.originalMask=this._mask,t.originalFilterArea=this.filterArea,this.render=this._renderCached,this.renderCanvas=this._renderCachedCanvas,this.destroy=this._cacheAsBitmapDestroy):(t=this._cacheData,t.sprite&&this._destroyCachedDisplayObject(),this.render=t.originalRender,this.renderCanvas=t.originalRenderCanvas,this.calculateBounds=t.originalCalculateBounds,this.getLocalBounds=t.originalGetLocalBounds,this.destroy=t.originalDestroy,this.updateTransform=t.originalUpdateTransform,this.containsPoint=t.originalContainsPoint,this._mask=t.originalMask,this.filterArea=t.originalFilterArea)}}}),ot.prototype._renderCached=function(r){!this.visible||this.worldAlpha<=0||!this.renderable||(this._initCachedDisplayObject(r),this._cacheData.sprite.transform._worldID=this.transform._worldID,this._cacheData.sprite.worldAlpha=this.worldAlpha,this._cacheData.sprite._render(r))},ot.prototype._initCachedDisplayObject=function(r){var t,e,s;if((t=this._cacheData)!=null&&t.sprite)return;const i=this.alpha;this.alpha=1,r.batch.flush();const n=this.getLocalBounds(new z,!0);if((e=this.filters)!=null&&e.length){const m=this.filters[0].padding;n.pad(m)}const a=this.cacheAsBitmapResolution||r.resolution;n.ceil(a),n.width=Math.max(n.width,1/a),n.height=Math.max(n.height,1/a);const o=r.renderTexture.current,h=r.renderTexture.sourceFrame.clone(),l=r.renderTexture.destinationFrame.clone(),u=r.projection.transform,c=Yt.create({width:n.width,height:n.height,resolution:a,multisample:(s=this.cacheAsBitmapMultisample)!=null?s:r.multisample}),d=`cacheAsBitmap_${Te()}`;this._cacheData.textureCacheId=d,X.addToCache(c.baseTexture,d),L.addToCache(c,d);const f=this.transform.localTransform.copyTo($h).invert().translate(-n.x,-n.y);this.render=this._cacheData.originalRender,r.render(this,{renderTexture:c,clear:!0,transform:f,skipUpdateTransform:!1}),r.framebuffer.blit(),r.projection.transform=u,r.renderTexture.bind(o,h,l),this.render=this._renderCached,this.updateTransform=this.displayObjectUpdateTransform,this.calculateBounds=this._calculateCachedBounds,this.getLocalBounds=this._getCachedLocalBounds,this._mask=null,this.filterArea=null,this.alpha=i;const p=new Ut(c);p.transform.worldTransform=this.transform.worldTransform,p.anchor.x=-(n.x/n.width),p.anchor.y=-(n.y/n.height),p.alpha=i,p._bounds=this._bounds,this._cacheData.sprite=p,this.transform._parentID=-1,this.parent?this.updateTransform():(this.enableTempParent(),this.updateTransform(),this.disableTempParent(null)),this.containsPoint=p.containsPoint.bind(p)},ot.prototype._renderCachedCanvas=function(r){!this.visible||this.worldAlpha<=0||!this.renderable||(this._initCachedDisplayObjectCanvas(r),this._cacheData.sprite.worldAlpha=this.worldAlpha,this._cacheData.sprite._renderCanvas(r))},ot.prototype._initCachedDisplayObjectCanvas=function(r){var t;if((t=this._cacheData)!=null&&t.sprite)return;const e=this.getLocalBounds(new z,!0),s=this.alpha;this.alpha=1;const i=r.canvasContext.activeContext,n=r._projTransform,a=this.cacheAsBitmapResolution||r.resolution;e.ceil(a),e.width=Math.max(e.width,1/a),e.height=Math.max(e.height,1/a);const o=Yt.create({width:e.width,height:e.height,resolution:a}),h=`cacheAsBitmap_${Te()}`;this._cacheData.textureCacheId=h,X.addToCache(o.baseTexture,h),L.addToCache(o,h);const l=$h;this.transform.localTransform.copyTo(l),l.invert(),l.tx-=e.x,l.ty-=e.y,this.renderCanvas=this._cacheData.originalRenderCanvas,r.render(this,{renderTexture:o,clear:!0,transform:l,skipUpdateTransform:!1}),r.canvasContext.activeContext=i,r._projTransform=n,this.renderCanvas=this._renderCachedCanvas,this.updateTransform=this.displayObjectUpdateTransform,this.calculateBounds=this._calculateCachedBounds,this.getLocalBounds=this._getCachedLocalBounds,this._mask=null,this.filterArea=null,this.alpha=s;const u=new Ut(o);u.transform.worldTransform=this.transform.worldTransform,u.anchor.x=-(e.x/e.width),u.anchor.y=-(e.y/e.height),u.alpha=s,u._bounds=this._bounds,this._cacheData.sprite=u,this.transform._parentID=-1,this.parent?this.updateTransform():(this.parent=r._tempDisplayObjectParent,this.updateTransform(),this.parent=null),this.containsPoint=u.containsPoint.bind(u)},ot.prototype._calculateCachedBounds=function(){this._bounds.clear(),this._cacheData.sprite.transform._worldID=this.transform._worldID,this._cacheData.sprite._calculateBounds(),this._bounds.updateID=this._boundsID},ot.prototype._getCachedLocalBounds=function(){return this._cacheData.sprite.getLocalBounds(null)},ot.prototype._destroyCachedDisplayObject=function(){this._cacheData.sprite._texture.destroy(!0),this._cacheData.sprite=null,X.removeFromCache(this._cacheData.textureCacheId),L.removeFromCache(this._cacheData.textureCacheId),this._cacheData.textureCacheId=null},ot.prototype._cacheAsBitmapDestroy=function(r){this.cacheAsBitmap=!1,this.destroy(r)},ot.prototype.name=null,Ct.prototype.getChildByName=function(r,t){for(let e=0,s=this.children.length;e<s;e++)if(this.children[e].name===r)return this.children[e];if(t)for(let e=0,s=this.children.length;e<s;e++){const i=this.children[e];if(!i.getChildByName)continue;const n=i.getChildByName(r,!0);if(n)return n}return null},ot.prototype.getGlobalPosition=function(r=new K,t=!1){return this.parent?this.parent.toGlobal(this.position,r,t):(r.x=this.position.x,r.y=this.position.y),r};var Of=`varying vec2 vTextureCoord;

uniform sampler2D uSampler;
uniform float uAlpha;

void main(void)
{
   gl_FragColor = texture2D(uSampler, vTextureCoord) * uAlpha;
}
`;class Hh extends Et{constructor(t=1){super(Ih,Of,{uAlpha:1}),this.alpha=t}get alpha(){return this.uniforms.uAlpha}set alpha(t){this.uniforms.uAlpha=t}}const Bf={5:[.153388,.221461,.250301],7:[.071303,.131514,.189879,.214607],9:[.028532,.067234,.124009,.179044,.20236],11:[.0093,.028002,.065984,.121703,.175713,.198596],13:[.002406,.009255,.027867,.065666,.121117,.174868,.197641],15:[489e-6,.002403,.009246,.02784,.065602,.120999,.174697,.197448]},Ff=["varying vec2 vBlurTexCoords[%size%];","uniform sampler2D uSampler;","void main(void)","{","    gl_FragColor = vec4(0.0);","    %blur%","}"].join(`
`);function Nf(r){const t=Bf[r],e=t.length;let s=Ff,i="";const n="gl_FragColor += texture2D(uSampler, vBlurTexCoords[%index%]) * %value%;";let a;for(let o=0;o<r;o++){let h=n.replace("%index%",o.toString());a=o,o>=e&&(a=r-o-1),h=h.replace("%value%",t[a].toString()),i+=h,i+=`
`}return s=s.replace("%blur%",i),s=s.replace("%size%",r.toString()),s}const Lf=`
    attribute vec2 aVertexPosition;

    uniform mat3 projectionMatrix;

    uniform float strength;

    varying vec2 vBlurTexCoords[%size%];

    uniform vec4 inputSize;
    uniform vec4 outputFrame;

    vec4 filterVertexPosition( void )
    {
        vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;

        return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
    }

    vec2 filterTextureCoord( void )
    {
        return aVertexPosition * (outputFrame.zw * inputSize.zw);
    }

    void main(void)
    {
        gl_Position = filterVertexPosition();

        vec2 textureCoord = filterTextureCoord();
        %blur%
    }`;function Uf(r,t){const e=Math.ceil(r/2);let s=Lf,i="",n;t?n="vBlurTexCoords[%index%] =  textureCoord + vec2(%sampleIndex% * strength, 0.0);":n="vBlurTexCoords[%index%] =  textureCoord + vec2(0.0, %sampleIndex% * strength);";for(let a=0;a<r;a++){let o=n.replace("%index%",a.toString());o=o.replace("%sampleIndex%",`${a-(e-1)}.0`),i+=o,i+=`
`}return s=s.replace("%blur%",i),s=s.replace("%size%",r.toString()),s}class ti extends Et{constructor(t,e=8,s=4,i=Et.defaultResolution,n=5){const a=Uf(n,t),o=Nf(n);super(a,o),this.horizontal=t,this.resolution=i,this._quality=0,this.quality=s,this.blur=e}apply(t,e,s,i){if(s?this.horizontal?this.uniforms.strength=1/s.width*(s.width/e.width):this.uniforms.strength=1/s.height*(s.height/e.height):this.horizontal?this.uniforms.strength=1/t.renderer.width*(t.renderer.width/e.width):this.uniforms.strength=1/t.renderer.height*(t.renderer.height/e.height),this.uniforms.strength*=this.strength,this.uniforms.strength/=this.passes,this.passes===1)t.applyFilter(this,e,s,i);else{const n=t.getFilterTexture(),a=t.renderer;let o=e,h=n;this.state.blend=!1,t.applyFilter(this,o,h,Vt.CLEAR);for(let l=1;l<this.passes-1;l++){t.bindAndClear(o,Vt.BLIT),this.uniforms.uSampler=h;const u=h;h=o,o=u,a.shader.bind(this),a.geometry.draw(5)}this.state.blend=!0,t.applyFilter(this,h,s,i),t.returnFilterTexture(n)}}get blur(){return this.strength}set blur(t){this.padding=1+Math.abs(t)*2,this.strength=t}get quality(){return this._quality}set quality(t){this._quality=t,this.passes=t}}class Vh extends Et{constructor(t=8,e=4,s=Et.defaultResolution,i=5){super(),this._repeatEdgePixels=!1,this.blurXFilter=new ti(!0,t,e,s,i),this.blurYFilter=new ti(!1,t,e,s,i),this.resolution=s,this.quality=e,this.blur=t,this.repeatEdgePixels=!1}apply(t,e,s,i){const n=Math.abs(this.blurXFilter.strength),a=Math.abs(this.blurYFilter.strength);if(n&&a){const o=t.getFilterTexture();this.blurXFilter.apply(t,e,o,Vt.CLEAR),this.blurYFilter.apply(t,o,s,i),t.returnFilterTexture(o)}else a?this.blurYFilter.apply(t,e,s,i):this.blurXFilter.apply(t,e,s,i)}updatePadding(){this._repeatEdgePixels?this.padding=0:this.padding=Math.max(Math.abs(this.blurXFilter.strength),Math.abs(this.blurYFilter.strength))*2}get blur(){return this.blurXFilter.blur}set blur(t){this.blurXFilter.blur=this.blurYFilter.blur=t,this.updatePadding()}get quality(){return this.blurXFilter.quality}set quality(t){this.blurXFilter.quality=this.blurYFilter.quality=t}get blurX(){return this.blurXFilter.blur}set blurX(t){this.blurXFilter.blur=t,this.updatePadding()}get blurY(){return this.blurYFilter.blur}set blurY(t){this.blurYFilter.blur=t,this.updatePadding()}get blendMode(){return this.blurYFilter.blendMode}set blendMode(t){this.blurYFilter.blendMode=t}get repeatEdgePixels(){return this._repeatEdgePixels}set repeatEdgePixels(t){this._repeatEdgePixels=t,this.updatePadding()}}var kf=`varying vec2 vTextureCoord;
uniform sampler2D uSampler;
uniform float m[20];
uniform float uAlpha;

void main(void)
{
    vec4 c = texture2D(uSampler, vTextureCoord);

    if (uAlpha == 0.0) {
        gl_FragColor = c;
        return;
    }

    // Un-premultiply alpha before applying the color matrix. See issue #3539.
    if (c.a > 0.0) {
      c.rgb /= c.a;
    }

    vec4 result;

    result.r = (m[0] * c.r);
        result.r += (m[1] * c.g);
        result.r += (m[2] * c.b);
        result.r += (m[3] * c.a);
        result.r += m[4];

    result.g = (m[5] * c.r);
        result.g += (m[6] * c.g);
        result.g += (m[7] * c.b);
        result.g += (m[8] * c.a);
        result.g += m[9];

    result.b = (m[10] * c.r);
       result.b += (m[11] * c.g);
       result.b += (m[12] * c.b);
       result.b += (m[13] * c.a);
       result.b += m[14];

    result.a = (m[15] * c.r);
       result.a += (m[16] * c.g);
       result.a += (m[17] * c.b);
       result.a += (m[18] * c.a);
       result.a += m[19];

    vec3 rgb = mix(c.rgb, result.rgb, uAlpha);

    // Premultiply alpha again.
    rgb *= result.a;

    gl_FragColor = vec4(rgb, result.a);
}
`;class ei extends Et{constructor(){const t={m:new Float32Array([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0]),uAlpha:1};super(On,kf,t),this.alpha=1}_loadMatrix(t,e=!1){let s=t;e&&(this._multiply(s,this.uniforms.m,t),s=this._colorMatrix(s)),this.uniforms.m=s}_multiply(t,e,s){return t[0]=e[0]*s[0]+e[1]*s[5]+e[2]*s[10]+e[3]*s[15],t[1]=e[0]*s[1]+e[1]*s[6]+e[2]*s[11]+e[3]*s[16],t[2]=e[0]*s[2]+e[1]*s[7]+e[2]*s[12]+e[3]*s[17],t[3]=e[0]*s[3]+e[1]*s[8]+e[2]*s[13]+e[3]*s[18],t[4]=e[0]*s[4]+e[1]*s[9]+e[2]*s[14]+e[3]*s[19]+e[4],t[5]=e[5]*s[0]+e[6]*s[5]+e[7]*s[10]+e[8]*s[15],t[6]=e[5]*s[1]+e[6]*s[6]+e[7]*s[11]+e[8]*s[16],t[7]=e[5]*s[2]+e[6]*s[7]+e[7]*s[12]+e[8]*s[17],t[8]=e[5]*s[3]+e[6]*s[8]+e[7]*s[13]+e[8]*s[18],t[9]=e[5]*s[4]+e[6]*s[9]+e[7]*s[14]+e[8]*s[19]+e[9],t[10]=e[10]*s[0]+e[11]*s[5]+e[12]*s[10]+e[13]*s[15],t[11]=e[10]*s[1]+e[11]*s[6]+e[12]*s[11]+e[13]*s[16],t[12]=e[10]*s[2]+e[11]*s[7]+e[12]*s[12]+e[13]*s[17],t[13]=e[10]*s[3]+e[11]*s[8]+e[12]*s[13]+e[13]*s[18],t[14]=e[10]*s[4]+e[11]*s[9]+e[12]*s[14]+e[13]*s[19]+e[14],t[15]=e[15]*s[0]+e[16]*s[5]+e[17]*s[10]+e[18]*s[15],t[16]=e[15]*s[1]+e[16]*s[6]+e[17]*s[11]+e[18]*s[16],t[17]=e[15]*s[2]+e[16]*s[7]+e[17]*s[12]+e[18]*s[17],t[18]=e[15]*s[3]+e[16]*s[8]+e[17]*s[13]+e[18]*s[18],t[19]=e[15]*s[4]+e[16]*s[9]+e[17]*s[14]+e[18]*s[19]+e[19],t}_colorMatrix(t){const e=new Float32Array(t);return e[4]/=255,e[9]/=255,e[14]/=255,e[19]/=255,e}brightness(t,e){const s=[t,0,0,0,0,0,t,0,0,0,0,0,t,0,0,0,0,0,1,0];this._loadMatrix(s,e)}tint(t,e){const[s,i,n]=Y.shared.setValue(t).toArray(),a=[s,0,0,0,0,0,i,0,0,0,0,0,n,0,0,0,0,0,1,0];this._loadMatrix(a,e)}greyscale(t,e){const s=[t,t,t,0,0,t,t,t,0,0,t,t,t,0,0,0,0,0,1,0];this._loadMatrix(s,e)}blackAndWhite(t){const e=[.3,.6,.1,0,0,.3,.6,.1,0,0,.3,.6,.1,0,0,0,0,0,1,0];this._loadMatrix(e,t)}hue(t,e){t=(t||0)/180*Math.PI;const s=Math.cos(t),i=Math.sin(t),n=Math.sqrt,a=1/3,o=n(a),h=s+(1-s)*a,l=a*(1-s)-o*i,u=a*(1-s)+o*i,c=a*(1-s)+o*i,d=s+a*(1-s),f=a*(1-s)-o*i,p=a*(1-s)-o*i,m=a*(1-s)+o*i,g=s+a*(1-s),_=[h,l,u,0,0,c,d,f,0,0,p,m,g,0,0,0,0,0,1,0];this._loadMatrix(_,e)}contrast(t,e){const s=(t||0)+1,i=-.5*(s-1),n=[s,0,0,0,i,0,s,0,0,i,0,0,s,0,i,0,0,0,1,0];this._loadMatrix(n,e)}saturate(t=0,e){const s=t*2/3+1,i=(s-1)*-.5,n=[s,i,i,0,0,i,s,i,0,0,i,i,s,0,0,0,0,0,1,0];this._loadMatrix(n,e)}desaturate(){this.saturate(-1)}negative(t){const e=[-1,0,0,1,0,0,-1,0,1,0,0,0,-1,1,0,0,0,0,1,0];this._loadMatrix(e,t)}sepia(t){const e=[.393,.7689999,.18899999,0,0,.349,.6859999,.16799999,0,0,.272,.5339999,.13099999,0,0,0,0,0,1,0];this._loadMatrix(e,t)}technicolor(t){const e=[1.9125277891456083,-.8545344976951645,-.09155508482755585,0,11.793603434377337,-.3087833385928097,1.7658908555458428,-.10601743074722245,0,-70.35205161461398,-.231103377548616,-.7501899197440212,1.847597816108189,0,30.950940869491138,0,0,0,1,0];this._loadMatrix(e,t)}polaroid(t){const e=[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0];this._loadMatrix(e,t)}toBGR(t){const e=[0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,1,0];this._loadMatrix(e,t)}kodachrome(t){const e=[1.1285582396593525,-.3967382283601348,-.03992559172921793,0,63.72958762196502,-.16404339962244616,1.0835251566291304,-.05498805115633132,0,24.732407896706203,-.16786010706155763,-.5603416277695248,1.6014850761964943,0,35.62982807460946,0,0,0,1,0];this._loadMatrix(e,t)}browni(t){const e=[.5997023498159715,.34553243048391263,-.2708298674538042,0,47.43192855600873,-.037703249837783157,.8609577587992641,.15059552388459913,0,-36.96841498319127,.24113635128153335,-.07441037908422492,.44972182064877153,0,-7.562075277591283,0,0,0,1,0];this._loadMatrix(e,t)}vintage(t){const e=[.6279345635605994,.3202183420819367,-.03965408211312453,0,9.651285835294123,.02578397704808868,.6441188644374771,.03259127616149294,0,7.462829176470591,.0466055556782719,-.0851232987247891,.5241648018700465,0,5.159190588235296,0,0,0,1,0];this._loadMatrix(e,t)}colorTone(t,e,s,i,n){t=t||.2,e=e||.15,s=s||16770432,i=i||3375104;const a=Y.shared,[o,h,l]=a.setValue(s).toArray(),[u,c,d]=a.setValue(i).toArray(),f=[.3,.59,.11,0,0,o,h,l,t,0,u,c,d,e,0,o-u,h-c,l-d,0,0];this._loadMatrix(f,n)}night(t,e){t=t||.1;const s=[t*-2,-t,0,0,0,-t,0,t,0,0,0,t,t*2,0,0,0,0,0,1,0];this._loadMatrix(s,e)}predator(t,e){const s=[11.224130630493164*t,-4.794486999511719*t,-2.8746118545532227*t,0*t,.40342438220977783*t,-3.6330697536468506*t,9.193157196044922*t,-2.951810836791992*t,0*t,-1.316135048866272*t,-3.2184197902679443*t,-4.2375030517578125*t,7.476448059082031*t,0*t,.8044459223747253*t,0,0,0,1,0];this._loadMatrix(s,e)}lsd(t){const e=[2,-.4,.5,0,0,-.5,2,-.4,0,0,-.4,-.5,3,0,0,0,0,0,1,0];this._loadMatrix(e,t)}reset(){const t=[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0];this._loadMatrix(t,!1)}get matrix(){return this.uniforms.m}set matrix(t){this.uniforms.m=t}get alpha(){return this.uniforms.uAlpha}set alpha(t){this.uniforms.uAlpha=t}}ei.prototype.grayscale=ei.prototype.greyscale;var Gf=`varying vec2 vFilterCoord;
varying vec2 vTextureCoord;

uniform vec2 scale;
uniform mat2 rotation;
uniform sampler2D uSampler;
uniform sampler2D mapSampler;

uniform highp vec4 inputSize;
uniform vec4 inputClamp;

void main(void)
{
  vec4 map =  texture2D(mapSampler, vFilterCoord);

  map -= 0.5;
  map.xy = scale * inputSize.zw * (rotation * map.xy);

  gl_FragColor = texture2D(uSampler, clamp(vec2(vTextureCoord.x + map.x, vTextureCoord.y + map.y), inputClamp.xy, inputClamp.zw));
}
`,$f=`attribute vec2 aVertexPosition;

uniform mat3 projectionMatrix;
uniform mat3 filterMatrix;

varying vec2 vTextureCoord;
varying vec2 vFilterCoord;

uniform vec4 inputSize;
uniform vec4 outputFrame;

vec4 filterVertexPosition( void )
{
    vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;

    return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
}

vec2 filterTextureCoord( void )
{
    return aVertexPosition * (outputFrame.zw * inputSize.zw);
}

void main(void)
{
	gl_Position = filterVertexPosition();
	vTextureCoord = filterTextureCoord();
	vFilterCoord = ( filterMatrix * vec3( vTextureCoord, 1.0)  ).xy;
}
`;class jh extends Et{constructor(t,e){const s=new tt;t.renderable=!1,super($f,Gf,{mapSampler:t._texture,filterMatrix:s,scale:{x:1,y:1},rotation:new Float32Array([1,0,0,1])}),this.maskSprite=t,this.maskMatrix=s,e==null&&(e=20),this.scale=new K(e,e)}apply(t,e,s,i){this.uniforms.filterMatrix=t.calculateSpriteMatrix(this.maskMatrix,this.maskSprite),this.uniforms.scale.x=this.scale.x,this.uniforms.scale.y=this.scale.y;const n=this.maskSprite.worldTransform,a=Math.sqrt(n.a*n.a+n.b*n.b),o=Math.sqrt(n.c*n.c+n.d*n.d);a!==0&&o!==0&&(this.uniforms.rotation[0]=n.a/a,this.uniforms.rotation[1]=n.b/a,this.uniforms.rotation[2]=n.c/o,this.uniforms.rotation[3]=n.d/o),t.applyFilter(this,e,s,i)}get map(){return this.uniforms.mapSampler}set map(t){this.uniforms.mapSampler=t}}var Hf=`varying vec2 v_rgbNW;
varying vec2 v_rgbNE;
varying vec2 v_rgbSW;
varying vec2 v_rgbSE;
varying vec2 v_rgbM;

varying vec2 vFragCoord;
uniform sampler2D uSampler;
uniform highp vec4 inputSize;


/**
 Basic FXAA implementation based on the code on geeks3d.com with the
 modification that the texture2DLod stuff was removed since it's
 unsupported by WebGL.

 --

 From:
 https://github.com/mitsuhiko/webgl-meincraft

 Copyright (c) 2011 by Armin Ronacher.

 Some rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions are
 met:

 * Redistributions of source code must retain the above copyright
 notice, this list of conditions and the following disclaimer.

 * Redistributions in binary form must reproduce the above
 copyright notice, this list of conditions and the following
 disclaimer in the documentation and/or other materials provided
 with the distribution.

 * The names of the contributors may not be used to endorse or
 promote products derived from this software without specific
 prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
 OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
 LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
 DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
 THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef FXAA_REDUCE_MIN
#define FXAA_REDUCE_MIN   (1.0/ 128.0)
#endif
#ifndef FXAA_REDUCE_MUL
#define FXAA_REDUCE_MUL   (1.0 / 8.0)
#endif
#ifndef FXAA_SPAN_MAX
#define FXAA_SPAN_MAX     8.0
#endif

//optimized version for mobile, where dependent
//texture reads can be a bottleneck
vec4 fxaa(sampler2D tex, vec2 fragCoord, vec2 inverseVP,
          vec2 v_rgbNW, vec2 v_rgbNE,
          vec2 v_rgbSW, vec2 v_rgbSE,
          vec2 v_rgbM) {
    vec4 color;
    vec3 rgbNW = texture2D(tex, v_rgbNW).xyz;
    vec3 rgbNE = texture2D(tex, v_rgbNE).xyz;
    vec3 rgbSW = texture2D(tex, v_rgbSW).xyz;
    vec3 rgbSE = texture2D(tex, v_rgbSE).xyz;
    vec4 texColor = texture2D(tex, v_rgbM);
    vec3 rgbM  = texColor.xyz;
    vec3 luma = vec3(0.299, 0.587, 0.114);
    float lumaNW = dot(rgbNW, luma);
    float lumaNE = dot(rgbNE, luma);
    float lumaSW = dot(rgbSW, luma);
    float lumaSE = dot(rgbSE, luma);
    float lumaM  = dot(rgbM,  luma);
    float lumaMin = min(lumaM, min(min(lumaNW, lumaNE), min(lumaSW, lumaSE)));
    float lumaMax = max(lumaM, max(max(lumaNW, lumaNE), max(lumaSW, lumaSE)));

    mediump vec2 dir;
    dir.x = -((lumaNW + lumaNE) - (lumaSW + lumaSE));
    dir.y =  ((lumaNW + lumaSW) - (lumaNE + lumaSE));

    float dirReduce = max((lumaNW + lumaNE + lumaSW + lumaSE) *
                          (0.25 * FXAA_REDUCE_MUL), FXAA_REDUCE_MIN);

    float rcpDirMin = 1.0 / (min(abs(dir.x), abs(dir.y)) + dirReduce);
    dir = min(vec2(FXAA_SPAN_MAX, FXAA_SPAN_MAX),
              max(vec2(-FXAA_SPAN_MAX, -FXAA_SPAN_MAX),
                  dir * rcpDirMin)) * inverseVP;

    vec3 rgbA = 0.5 * (
                       texture2D(tex, fragCoord * inverseVP + dir * (1.0 / 3.0 - 0.5)).xyz +
                       texture2D(tex, fragCoord * inverseVP + dir * (2.0 / 3.0 - 0.5)).xyz);
    vec3 rgbB = rgbA * 0.5 + 0.25 * (
                                     texture2D(tex, fragCoord * inverseVP + dir * -0.5).xyz +
                                     texture2D(tex, fragCoord * inverseVP + dir * 0.5).xyz);

    float lumaB = dot(rgbB, luma);
    if ((lumaB < lumaMin) || (lumaB > lumaMax))
        color = vec4(rgbA, texColor.a);
    else
        color = vec4(rgbB, texColor.a);
    return color;
}

void main() {

      vec4 color;

      color = fxaa(uSampler, vFragCoord, inputSize.zw, v_rgbNW, v_rgbNE, v_rgbSW, v_rgbSE, v_rgbM);

      gl_FragColor = color;
}
`,Vf=`
attribute vec2 aVertexPosition;

uniform mat3 projectionMatrix;

varying vec2 v_rgbNW;
varying vec2 v_rgbNE;
varying vec2 v_rgbSW;
varying vec2 v_rgbSE;
varying vec2 v_rgbM;

varying vec2 vFragCoord;

uniform vec4 inputSize;
uniform vec4 outputFrame;

vec4 filterVertexPosition( void )
{
    vec2 position = aVertexPosition * max(outputFrame.zw, vec2(0.)) + outputFrame.xy;

    return vec4((projectionMatrix * vec3(position, 1.0)).xy, 0.0, 1.0);
}

void texcoords(vec2 fragCoord, vec2 inverseVP,
               out vec2 v_rgbNW, out vec2 v_rgbNE,
               out vec2 v_rgbSW, out vec2 v_rgbSE,
               out vec2 v_rgbM) {
    v_rgbNW = (fragCoord + vec2(-1.0, -1.0)) * inverseVP;
    v_rgbNE = (fragCoord + vec2(1.0, -1.0)) * inverseVP;
    v_rgbSW = (fragCoord + vec2(-1.0, 1.0)) * inverseVP;
    v_rgbSE = (fragCoord + vec2(1.0, 1.0)) * inverseVP;
    v_rgbM = vec2(fragCoord * inverseVP);
}

void main(void) {

   gl_Position = filterVertexPosition();

   vFragCoord = aVertexPosition * outputFrame.zw;

   texcoords(vFragCoord, inputSize.zw, v_rgbNW, v_rgbNE, v_rgbSW, v_rgbSE, v_rgbM);
}
`;class Xh extends Et{constructor(){super(Vf,Hf)}}var jf=`precision highp float;

varying vec2 vTextureCoord;
varying vec4 vColor;

uniform float uNoise;
uniform float uSeed;
uniform sampler2D uSampler;

float rand(vec2 co)
{
    return fract(sin(dot(co.xy, vec2(12.9898, 78.233))) * 43758.5453);
}

void main()
{
    vec4 color = texture2D(uSampler, vTextureCoord);
    float randomValue = rand(gl_FragCoord.xy * uSeed);
    float diff = (randomValue - 0.5) * uNoise;

    // Un-premultiply alpha before applying the color matrix. See issue #3539.
    if (color.a > 0.0) {
        color.rgb /= color.a;
    }

    color.r += diff;
    color.g += diff;
    color.b += diff;

    // Premultiply alpha again.
    color.rgb *= color.a;

    gl_FragColor = color;
}
`;class zh extends Et{constructor(t=.5,e=Math.random()){super(On,jf,{uNoise:0,uSeed:0}),this.noise=t,this.seed=e}get noise(){return this.uniforms.uNoise}set noise(t){this.uniforms.uNoise=t}get seed(){return this.uniforms.uSeed}set seed(t){this.uniforms.uSeed=t}}const Kn={AlphaFilter:Hh,BlurFilter:Vh,BlurFilterPass:ti,ColorMatrixFilter:ei,DisplacementFilter:jh,FXAAFilter:Xh,NoiseFilter:zh};Object.entries(Kn).forEach(([r,t])=>{Object.defineProperty(Kn,r,{get(){return ao("7.1.0",`filters.${r} has moved to ${r}`),t}})});let Xf=class{constructor(){this.interactionFrequency=10,this._deltaTime=0,this._didMove=!1,this.tickerAdded=!1,this._pauseUpdate=!0}init(t){this.removeTickerListener(),this.events=t,this.interactionFrequency=10,this._deltaTime=0,this._didMove=!1,this.tickerAdded=!1,this._pauseUpdate=!0}get pauseUpdate(){return this._pauseUpdate}set pauseUpdate(t){this._pauseUpdate=t}addTickerListener(){this.tickerAdded||!this.domElement||(bt.system.add(this.tickerUpdate,this,me.INTERACTION),this.tickerAdded=!0)}removeTickerListener(){this.tickerAdded&&(bt.system.remove(this.tickerUpdate,this),this.tickerAdded=!1)}pointerMoved(){this._didMove=!0}update(){if(!this.domElement||this._pauseUpdate)return;if(this._didMove){this._didMove=!1;return}const t=this.events.rootPointerEvent;this.events.supportsTouchEvents&&t.pointerType==="touch"||globalThis.document.dispatchEvent(new PointerEvent("pointermove",{clientX:t.clientX,clientY:t.clientY}))}tickerUpdate(t){this._deltaTime+=t,!(this._deltaTime<this.interactionFrequency)&&(this._deltaTime=0,this.update())}};const we=new Xf;class Je{constructor(t){this.bubbles=!0,this.cancelBubble=!0,this.cancelable=!1,this.composed=!1,this.defaultPrevented=!1,this.eventPhase=Je.prototype.NONE,this.propagationStopped=!1,this.propagationImmediatelyStopped=!1,this.layer=new K,this.page=new K,this.NONE=0,this.CAPTURING_PHASE=1,this.AT_TARGET=2,this.BUBBLING_PHASE=3,this.manager=t}get layerX(){return this.layer.x}get layerY(){return this.layer.y}get pageX(){return this.page.x}get pageY(){return this.page.y}get data(){return this}composedPath(){return this.manager&&(!this.path||this.path[this.path.length-1]!==this.target)&&(this.path=this.target?this.manager.propagationPath(this.target):[]),this.path}initEvent(t,e,s){throw new Error("initEvent() is a legacy DOM API. It is not implemented in the Federated Events API.")}initUIEvent(t,e,s,i,n){throw new Error("initUIEvent() is a legacy DOM API. It is not implemented in the Federated Events API.")}preventDefault(){this.nativeEvent instanceof Event&&this.nativeEvent.cancelable&&this.nativeEvent.preventDefault(),this.defaultPrevented=!0}stopImmediatePropagation(){this.propagationImmediatelyStopped=!0}stopPropagation(){this.propagationStopped=!0}}class Hs extends Je{constructor(){super(...arguments),this.client=new K,this.movement=new K,this.offset=new K,this.global=new K,this.screen=new K}get clientX(){return this.client.x}get clientY(){return this.client.y}get x(){return this.clientX}get y(){return this.clientY}get movementX(){return this.movement.x}get movementY(){return this.movement.y}get offsetX(){return this.offset.x}get offsetY(){return this.offset.y}get globalX(){return this.global.x}get globalY(){return this.global.y}get screenX(){return this.screen.x}get screenY(){return this.screen.y}getLocalPosition(t,e,s){return t.worldTransform.applyInverse(s||this.global,e)}getModifierState(t){return"getModifierState"in this.nativeEvent&&this.nativeEvent.getModifierState(t)}initMouseEvent(t,e,s,i,n,a,o,h,l,u,c,d,f,p,m){throw new Error("Method not implemented.")}}class kt extends Hs{constructor(){super(...arguments),this.width=0,this.height=0,this.isPrimary=!1}getCoalescedEvents(){return this.type==="pointermove"||this.type==="mousemove"||this.type==="touchmove"?[this]:[]}getPredictedEvents(){throw new Error("getPredictedEvents is not supported!")}}class He extends Hs{constructor(){super(...arguments),this.DOM_DELTA_PIXEL=0,this.DOM_DELTA_LINE=1,this.DOM_DELTA_PAGE=2}}He.DOM_DELTA_PIXEL=0,He.DOM_DELTA_LINE=1,He.DOM_DELTA_PAGE=2;const zf=2048,Wf=new K,Zn=new K;class Wh{constructor(t){this.dispatch=new Ye,this.moveOnAll=!1,this.enableGlobalMoveEvents=!0,this.mappingState={trackingData:{}},this.eventPool=new Map,this._allInteractiveElements=[],this._hitElements=[],this._isPointerMoveEvent=!1,this.rootTarget=t,this.hitPruneFn=this.hitPruneFn.bind(this),this.hitTestFn=this.hitTestFn.bind(this),this.mapPointerDown=this.mapPointerDown.bind(this),this.mapPointerMove=this.mapPointerMove.bind(this),this.mapPointerOut=this.mapPointerOut.bind(this),this.mapPointerOver=this.mapPointerOver.bind(this),this.mapPointerUp=this.mapPointerUp.bind(this),this.mapPointerUpOutside=this.mapPointerUpOutside.bind(this),this.mapWheel=this.mapWheel.bind(this),this.mappingTable={},this.addEventMapping("pointerdown",this.mapPointerDown),this.addEventMapping("pointermove",this.mapPointerMove),this.addEventMapping("pointerout",this.mapPointerOut),this.addEventMapping("pointerleave",this.mapPointerOut),this.addEventMapping("pointerover",this.mapPointerOver),this.addEventMapping("pointerup",this.mapPointerUp),this.addEventMapping("pointerupoutside",this.mapPointerUpOutside),this.addEventMapping("wheel",this.mapWheel)}addEventMapping(t,e){this.mappingTable[t]||(this.mappingTable[t]=[]),this.mappingTable[t].push({fn:e,priority:0}),this.mappingTable[t].sort((s,i)=>s.priority-i.priority)}dispatchEvent(t,e){t.propagationStopped=!1,t.propagationImmediatelyStopped=!1,this.propagate(t,e),this.dispatch.emit(e||t.type,t)}mapEvent(t){if(!this.rootTarget)return;const e=this.mappingTable[t.type];if(e)for(let s=0,i=e.length;s<i;s++)e[s].fn(t);else console.warn(`[EventBoundary]: Event mapping not defined for ${t.type}`)}hitTest(t,e){we.pauseUpdate=!0;const s=this._isPointerMoveEvent&&this.enableGlobalMoveEvents?"hitTestMoveRecursive":"hitTestRecursive",i=this[s](this.rootTarget,this.rootTarget.eventMode,Wf.set(t,e),this.hitTestFn,this.hitPruneFn);return i&&i[0]}propagate(t,e){if(!t.target)return;const s=t.composedPath();t.eventPhase=t.CAPTURING_PHASE;for(let i=0,n=s.length-1;i<n;i++)if(t.currentTarget=s[i],this.notifyTarget(t,e),t.propagationStopped||t.propagationImmediatelyStopped)return;if(t.eventPhase=t.AT_TARGET,t.currentTarget=t.target,this.notifyTarget(t,e),!(t.propagationStopped||t.propagationImmediatelyStopped)){t.eventPhase=t.BUBBLING_PHASE;for(let i=s.length-2;i>=0;i--)if(t.currentTarget=s[i],this.notifyTarget(t,e),t.propagationStopped||t.propagationImmediatelyStopped)return}}all(t,e,s=this._allInteractiveElements){if(s.length===0)return;t.eventPhase=t.BUBBLING_PHASE;const i=Array.isArray(e)?e:[e];for(let n=s.length-1;n>=0;n--)i.forEach(a=>{t.currentTarget=s[n],this.notifyTarget(t,a)})}propagationPath(t){const e=[t];for(let s=0;s<zf&&t!==this.rootTarget;s++){if(!t.parent)throw new Error("Cannot find propagation path to disconnected target");e.push(t.parent),t=t.parent}return e.reverse(),e}hitTestMoveRecursive(t,e,s,i,n,a=!1){let o=!1;if(this._interactivePrune(t))return null;if((t.eventMode==="dynamic"||e==="dynamic")&&(we.pauseUpdate=!1),t.interactiveChildren&&t.children){const u=t.children;for(let c=u.length-1;c>=0;c--){const d=u[c],f=this.hitTestMoveRecursive(d,this._isInteractive(e)?e:d.eventMode,s,i,n,a||n(t,s));if(f){if(f.length>0&&!f[f.length-1].parent)continue;const p=t.isInteractive();(f.length>0||p)&&(p&&this._allInteractiveElements.push(t),f.push(t)),this._hitElements.length===0&&(this._hitElements=f),o=!0}}}const h=this._isInteractive(e),l=t.isInteractive();return h&&l&&this._allInteractiveElements.push(t),a||this._hitElements.length>0?null:o?this._hitElements:h&&!n(t,s)&&i(t,s)?l?[t]:[]:null}hitTestRecursive(t,e,s,i,n){if(this._interactivePrune(t)||n(t,s))return null;if((t.eventMode==="dynamic"||e==="dynamic")&&(we.pauseUpdate=!1),t.interactiveChildren&&t.children){const h=t.children;for(let l=h.length-1;l>=0;l--){const u=h[l],c=this.hitTestRecursive(u,this._isInteractive(e)?e:u.eventMode,s,i,n);if(c){if(c.length>0&&!c[c.length-1].parent)continue;const d=t.isInteractive();return(c.length>0||d)&&c.push(t),c}}}const a=this._isInteractive(e),o=t.isInteractive();return a&&i(t,s)?o?[t]:[]:null}_isInteractive(t){return t==="static"||t==="dynamic"}_interactivePrune(t){return!!(!t||t.isMask||!t.visible||!t.renderable||t.eventMode==="none"||t.eventMode==="passive"&&!t.interactiveChildren||t.isMask)}hitPruneFn(t,e){var s;if(t.hitArea&&(t.worldTransform.applyInverse(e,Zn),!t.hitArea.contains(Zn.x,Zn.y)))return!0;if(t._mask){const i=t._mask.isMaskData?t._mask.maskObject:t._mask;if(i&&!((s=i.containsPoint)!=null&&s.call(i,e)))return!0}return!1}hitTestFn(t,e){return t.eventMode==="passive"?!1:t.hitArea?!0:t.containsPoint?t.containsPoint(e):!1}notifyTarget(t,e){var s,i;e=e!=null?e:t.type;const n=`on${e}`;(i=(s=t.currentTarget)[n])==null||i.call(s,t);const a=t.eventPhase===t.CAPTURING_PHASE||t.eventPhase===t.AT_TARGET?`${e}capture`:e;this.notifyListeners(t,a),t.eventPhase===t.AT_TARGET&&this.notifyListeners(t,e)}mapPointerDown(t){if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}const e=this.createPointerEvent(t);if(this.dispatchEvent(e,"pointerdown"),e.pointerType==="touch")this.dispatchEvent(e,"touchstart");else if(e.pointerType==="mouse"||e.pointerType==="pen"){const i=e.button===2;this.dispatchEvent(e,i?"rightdown":"mousedown")}const s=this.trackingData(t.pointerId);s.pressTargetsByButton[t.button]=e.composedPath(),this.freeEvent(e)}mapPointerMove(t){var e,s,i;if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}this._allInteractiveElements.length=0,this._hitElements.length=0,this._isPointerMoveEvent=!0;const n=this.createPointerEvent(t);this._isPointerMoveEvent=!1;const a=n.pointerType==="mouse"||n.pointerType==="pen",o=this.trackingData(t.pointerId),h=this.findMountedTarget(o.overTargets);if(((e=o.overTargets)==null?void 0:e.length)>0&&h!==n.target){const c=t.type==="mousemove"?"mouseout":"pointerout",d=this.createPointerEvent(t,c,h);if(this.dispatchEvent(d,"pointerout"),a&&this.dispatchEvent(d,"mouseout"),!n.composedPath().includes(h)){const f=this.createPointerEvent(t,"pointerleave",h);for(f.eventPhase=f.AT_TARGET;f.target&&!n.composedPath().includes(f.target);)f.currentTarget=f.target,this.notifyTarget(f),a&&this.notifyTarget(f,"mouseleave"),f.target=f.target.parent;this.freeEvent(f)}this.freeEvent(d)}if(h!==n.target){const c=t.type==="mousemove"?"mouseover":"pointerover",d=this.clonePointerEvent(n,c);this.dispatchEvent(d,"pointerover"),a&&this.dispatchEvent(d,"mouseover");let f=h==null?void 0:h.parent;for(;f&&f!==this.rootTarget.parent&&f!==n.target;)f=f.parent;if(!f||f===this.rootTarget.parent){const p=this.clonePointerEvent(n,"pointerenter");for(p.eventPhase=p.AT_TARGET;p.target&&p.target!==h&&p.target!==this.rootTarget.parent;)p.currentTarget=p.target,this.notifyTarget(p),a&&this.notifyTarget(p,"mouseenter"),p.target=p.target.parent;this.freeEvent(p)}this.freeEvent(d)}const l=[],u=(s=this.enableGlobalMoveEvents)!=null?s:!0;this.moveOnAll?l.push("pointermove"):this.dispatchEvent(n,"pointermove"),u&&l.push("globalpointermove"),n.pointerType==="touch"&&(this.moveOnAll?l.splice(1,0,"touchmove"):this.dispatchEvent(n,"touchmove"),u&&l.push("globaltouchmove")),a&&(this.moveOnAll?l.splice(1,0,"mousemove"):this.dispatchEvent(n,"mousemove"),u&&l.push("globalmousemove"),this.cursor=(i=n.target)==null?void 0:i.cursor),l.length>0&&this.all(n,l),this._allInteractiveElements.length=0,this._hitElements.length=0,o.overTargets=n.composedPath(),this.freeEvent(n)}mapPointerOver(t){var e;if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}const s=this.trackingData(t.pointerId),i=this.createPointerEvent(t),n=i.pointerType==="mouse"||i.pointerType==="pen";this.dispatchEvent(i,"pointerover"),n&&this.dispatchEvent(i,"mouseover"),i.pointerType==="mouse"&&(this.cursor=(e=i.target)==null?void 0:e.cursor);const a=this.clonePointerEvent(i,"pointerenter");for(a.eventPhase=a.AT_TARGET;a.target&&a.target!==this.rootTarget.parent;)a.currentTarget=a.target,this.notifyTarget(a),n&&this.notifyTarget(a,"mouseenter"),a.target=a.target.parent;s.overTargets=i.composedPath(),this.freeEvent(i),this.freeEvent(a)}mapPointerOut(t){if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}const e=this.trackingData(t.pointerId);if(e.overTargets){const s=t.pointerType==="mouse"||t.pointerType==="pen",i=this.findMountedTarget(e.overTargets),n=this.createPointerEvent(t,"pointerout",i);this.dispatchEvent(n),s&&this.dispatchEvent(n,"mouseout");const a=this.createPointerEvent(t,"pointerleave",i);for(a.eventPhase=a.AT_TARGET;a.target&&a.target!==this.rootTarget.parent;)a.currentTarget=a.target,this.notifyTarget(a),s&&this.notifyTarget(a,"mouseleave"),a.target=a.target.parent;e.overTargets=null,this.freeEvent(n),this.freeEvent(a)}this.cursor=null}mapPointerUp(t){if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}const e=performance.now(),s=this.createPointerEvent(t);if(this.dispatchEvent(s,"pointerup"),s.pointerType==="touch")this.dispatchEvent(s,"touchend");else if(s.pointerType==="mouse"||s.pointerType==="pen"){const o=s.button===2;this.dispatchEvent(s,o?"rightup":"mouseup")}const i=this.trackingData(t.pointerId),n=this.findMountedTarget(i.pressTargetsByButton[t.button]);let a=n;if(n&&!s.composedPath().includes(n)){let o=n;for(;o&&!s.composedPath().includes(o);){if(s.currentTarget=o,this.notifyTarget(s,"pointerupoutside"),s.pointerType==="touch")this.notifyTarget(s,"touchendoutside");else if(s.pointerType==="mouse"||s.pointerType==="pen"){const h=s.button===2;this.notifyTarget(s,h?"rightupoutside":"mouseupoutside")}o=o.parent}delete i.pressTargetsByButton[t.button],a=o}if(a){const o=this.clonePointerEvent(s,"click");o.target=a,o.path=null,i.clicksByButton[t.button]||(i.clicksByButton[t.button]={clickCount:0,target:o.target,timeStamp:e});const h=i.clicksByButton[t.button];if(h.target===o.target&&e-h.timeStamp<200?++h.clickCount:h.clickCount=1,h.target=o.target,h.timeStamp=e,o.detail=h.clickCount,o.pointerType==="mouse"){const l=o.button===2;this.dispatchEvent(o,l?"rightclick":"click")}else o.pointerType==="touch"&&this.dispatchEvent(o,"tap");this.dispatchEvent(o,"pointertap"),this.freeEvent(o)}this.freeEvent(s)}mapPointerUpOutside(t){if(!(t instanceof kt)){console.warn("EventBoundary cannot map a non-pointer event as a pointer event");return}const e=this.trackingData(t.pointerId),s=this.findMountedTarget(e.pressTargetsByButton[t.button]),i=this.createPointerEvent(t);if(s){let n=s;for(;n;)i.currentTarget=n,this.notifyTarget(i,"pointerupoutside"),i.pointerType==="touch"?this.notifyTarget(i,"touchendoutside"):(i.pointerType==="mouse"||i.pointerType==="pen")&&this.notifyTarget(i,i.button===2?"rightupoutside":"mouseupoutside"),n=n.parent;delete e.pressTargetsByButton[t.button]}this.freeEvent(i)}mapWheel(t){if(!(t instanceof He)){console.warn("EventBoundary cannot map a non-wheel event as a wheel event");return}const e=this.createWheelEvent(t);this.dispatchEvent(e),this.freeEvent(e)}findMountedTarget(t){if(!t)return null;let e=t[0];for(let s=1;s<t.length&&t[s].parent===e;s++)e=t[s];return e}createPointerEvent(t,e,s){var i;const n=this.allocateEvent(kt);return this.copyPointerData(t,n),this.copyMouseData(t,n),this.copyData(t,n),n.nativeEvent=t.nativeEvent,n.originalEvent=t,n.target=(i=s!=null?s:this.hitTest(n.global.x,n.global.y))!=null?i:this._hitElements[0],typeof e=="string"&&(n.type=e),n}createWheelEvent(t){const e=this.allocateEvent(He);return this.copyWheelData(t,e),this.copyMouseData(t,e),this.copyData(t,e),e.nativeEvent=t.nativeEvent,e.originalEvent=t,e.target=this.hitTest(e.global.x,e.global.y),e}clonePointerEvent(t,e){const s=this.allocateEvent(kt);return s.nativeEvent=t.nativeEvent,s.originalEvent=t.originalEvent,this.copyPointerData(t,s),this.copyMouseData(t,s),this.copyData(t,s),s.target=t.target,s.path=t.composedPath().slice(),s.type=e!=null?e:s.type,s}copyWheelData(t,e){e.deltaMode=t.deltaMode,e.deltaX=t.deltaX,e.deltaY=t.deltaY,e.deltaZ=t.deltaZ}copyPointerData(t,e){t instanceof kt&&e instanceof kt&&(e.pointerId=t.pointerId,e.width=t.width,e.height=t.height,e.isPrimary=t.isPrimary,e.pointerType=t.pointerType,e.pressure=t.pressure,e.tangentialPressure=t.tangentialPressure,e.tiltX=t.tiltX,e.tiltY=t.tiltY,e.twist=t.twist)}copyMouseData(t,e){t instanceof Hs&&e instanceof Hs&&(e.altKey=t.altKey,e.button=t.button,e.buttons=t.buttons,e.client.copyFrom(t.client),e.ctrlKey=t.ctrlKey,e.metaKey=t.metaKey,e.movement.copyFrom(t.movement),e.screen.copyFrom(t.screen),e.shiftKey=t.shiftKey,e.global.copyFrom(t.global))}copyData(t,e){e.isTrusted=t.isTrusted,e.srcElement=t.srcElement,e.timeStamp=performance.now(),e.type=t.type,e.detail=t.detail,e.view=t.view,e.which=t.which,e.layer.copyFrom(t.layer),e.page.copyFrom(t.page)}trackingData(t){return this.mappingState.trackingData[t]||(this.mappingState.trackingData[t]={pressTargetsByButton:{},clicksByButton:{},overTarget:null}),this.mappingState.trackingData[t]}allocateEvent(t){this.eventPool.has(t)||this.eventPool.set(t,[]);const e=this.eventPool.get(t).pop()||new t(this);return e.eventPhase=e.NONE,e.currentTarget=null,e.path=null,e.target=null,e}freeEvent(t){if(t.manager!==this)throw new Error("It is illegal to free an event not managed by this EventBoundary!");const e=t.constructor;this.eventPool.has(e)||this.eventPool.set(e,[]),this.eventPool.get(e).push(t)}notifyListeners(t,e){const s=t.currentTarget._events[e];if(s&&t.currentTarget.isInteractive())if("fn"in s)s.once&&t.currentTarget.removeListener(e,s.fn,void 0,!0),s.fn.call(s.context,t);else for(let i=0,n=s.length;i<n&&!t.propagationImmediatelyStopped;i++)s[i].once&&t.currentTarget.removeListener(e,s[i].fn,void 0,!0),s[i].fn.call(s[i].context,t)}}var Yf=Object.defineProperty,Yh=Object.getOwnPropertySymbols,qf=Object.prototype.hasOwnProperty,Kf=Object.prototype.propertyIsEnumerable,qh=(r,t,e)=>t in r?Yf(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,Zf=(r,t)=>{for(var e in t||(t={}))qf.call(t,e)&&qh(r,e,t[e]);if(Yh)for(var e of Yh(t))Kf.call(t,e)&&qh(r,e,t[e]);return r};const Qf=1,Jf={touchstart:"pointerdown",touchend:"pointerup",touchendoutside:"pointerupoutside",touchmove:"pointermove",touchcancel:"pointercancel"},Qn=class Pa{constructor(t){this.supportsTouchEvents="ontouchstart"in globalThis,this.supportsPointerEvents=!!globalThis.PointerEvent,this.domElement=null,this.resolution=1,this.renderer=t,this.rootBoundary=new Wh(null),we.init(this),this.autoPreventDefault=!0,this.eventsAdded=!1,this.rootPointerEvent=new kt(null),this.rootWheelEvent=new He(null),this.cursorStyles={default:"inherit",pointer:"pointer"},this.features=new Proxy(Zf({},Pa.defaultEventFeatures),{set:(e,s,i)=>(s==="globalMove"&&(this.rootBoundary.enableGlobalMoveEvents=i),e[s]=i,!0)}),this.onPointerDown=this.onPointerDown.bind(this),this.onPointerMove=this.onPointerMove.bind(this),this.onPointerUp=this.onPointerUp.bind(this),this.onPointerOverOut=this.onPointerOverOut.bind(this),this.onWheel=this.onWheel.bind(this)}static get defaultEventMode(){return this._defaultEventMode}init(t){var e,s;const{view:i,resolution:n}=this.renderer;this.setTargetElement(i),this.resolution=n,Pa._defaultEventMode=(e=t.eventMode)!=null?e:"auto",Object.assign(this.features,(s=t.eventFeatures)!=null?s:{}),this.rootBoundary.enableGlobalMoveEvents=this.features.globalMove}resolutionChange(t){this.resolution=t}destroy(){this.setTargetElement(null),this.renderer=null}setCursor(t){t=t||"default";let e=!0;if(globalThis.OffscreenCanvas&&this.domElement instanceof OffscreenCanvas&&(e=!1),this.currentCursor===t)return;this.currentCursor=t;const s=this.cursorStyles[t];if(s)switch(typeof s){case"string":e&&(this.domElement.style.cursor=s);break;case"function":s(t);break;case"object":e&&Object.assign(this.domElement.style,s);break}else e&&typeof t=="string"&&!Object.prototype.hasOwnProperty.call(this.cursorStyles,t)&&(this.domElement.style.cursor=t)}get pointer(){return this.rootPointerEvent}onPointerDown(t){if(!this.features.click)return;this.rootBoundary.rootTarget=this.renderer.lastObjectRendered;const e=this.normalizeToPointerData(t);this.autoPreventDefault&&e[0].isNormalized&&(t.cancelable||!("cancelable"in t))&&t.preventDefault();for(let s=0,i=e.length;s<i;s++){const n=e[s],a=this.bootstrapEvent(this.rootPointerEvent,n);this.rootBoundary.mapEvent(a)}this.setCursor(this.rootBoundary.cursor)}onPointerMove(t){if(!this.features.move)return;this.rootBoundary.rootTarget=this.renderer.lastObjectRendered,we.pointerMoved();const e=this.normalizeToPointerData(t);for(let s=0,i=e.length;s<i;s++){const n=this.bootstrapEvent(this.rootPointerEvent,e[s]);this.rootBoundary.mapEvent(n)}this.setCursor(this.rootBoundary.cursor)}onPointerUp(t){if(!this.features.click)return;this.rootBoundary.rootTarget=this.renderer.lastObjectRendered;let e=t.target;t.composedPath&&t.composedPath().length>0&&(e=t.composedPath()[0]);const s=e!==this.domElement?"outside":"",i=this.normalizeToPointerData(t);for(let n=0,a=i.length;n<a;n++){const o=this.bootstrapEvent(this.rootPointerEvent,i[n]);o.type+=s,this.rootBoundary.mapEvent(o)}this.setCursor(this.rootBoundary.cursor)}onPointerOverOut(t){if(!this.features.click)return;this.rootBoundary.rootTarget=this.renderer.lastObjectRendered;const e=this.normalizeToPointerData(t);for(let s=0,i=e.length;s<i;s++){const n=this.bootstrapEvent(this.rootPointerEvent,e[s]);this.rootBoundary.mapEvent(n)}this.setCursor(this.rootBoundary.cursor)}onWheel(t){if(!this.features.wheel)return;const e=this.normalizeWheelEvent(t);this.rootBoundary.rootTarget=this.renderer.lastObjectRendered,this.rootBoundary.mapEvent(e)}setTargetElement(t){this.removeEvents(),this.domElement=t,we.domElement=t,this.addEvents()}addEvents(){if(this.eventsAdded||!this.domElement)return;we.addTickerListener();const t=this.domElement.style;t&&(globalThis.navigator.msPointerEnabled?(t.msContentZooming="none",t.msTouchAction="none"):this.supportsPointerEvents&&(t.touchAction="none")),this.supportsPointerEvents?(globalThis.document.addEventListener("pointermove",this.onPointerMove,!0),this.domElement.addEventListener("pointerdown",this.onPointerDown,!0),this.domElement.addEventListener("pointerleave",this.onPointerOverOut,!0),this.domElement.addEventListener("pointerover",this.onPointerOverOut,!0),globalThis.addEventListener("pointerup",this.onPointerUp,!0)):(globalThis.document.addEventListener("mousemove",this.onPointerMove,!0),this.domElement.addEventListener("mousedown",this.onPointerDown,!0),this.domElement.addEventListener("mouseout",this.onPointerOverOut,!0),this.domElement.addEventListener("mouseover",this.onPointerOverOut,!0),globalThis.addEventListener("mouseup",this.onPointerUp,!0),this.supportsTouchEvents&&(this.domElement.addEventListener("touchstart",this.onPointerDown,!0),this.domElement.addEventListener("touchend",this.onPointerUp,!0),this.domElement.addEventListener("touchmove",this.onPointerMove,!0))),this.domElement.addEventListener("wheel",this.onWheel,{passive:!0,capture:!0}),this.eventsAdded=!0}removeEvents(){if(!this.eventsAdded||!this.domElement)return;we.removeTickerListener();const t=this.domElement.style;globalThis.navigator.msPointerEnabled?(t.msContentZooming="",t.msTouchAction=""):this.supportsPointerEvents&&(t.touchAction=""),this.supportsPointerEvents?(globalThis.document.removeEventListener("pointermove",this.onPointerMove,!0),this.domElement.removeEventListener("pointerdown",this.onPointerDown,!0),this.domElement.removeEventListener("pointerleave",this.onPointerOverOut,!0),this.domElement.removeEventListener("pointerover",this.onPointerOverOut,!0),globalThis.removeEventListener("pointerup",this.onPointerUp,!0)):(globalThis.document.removeEventListener("mousemove",this.onPointerMove,!0),this.domElement.removeEventListener("mousedown",this.onPointerDown,!0),this.domElement.removeEventListener("mouseout",this.onPointerOverOut,!0),this.domElement.removeEventListener("mouseover",this.onPointerOverOut,!0),globalThis.removeEventListener("mouseup",this.onPointerUp,!0),this.supportsTouchEvents&&(this.domElement.removeEventListener("touchstart",this.onPointerDown,!0),this.domElement.removeEventListener("touchend",this.onPointerUp,!0),this.domElement.removeEventListener("touchmove",this.onPointerMove,!0))),this.domElement.removeEventListener("wheel",this.onWheel,!0),this.domElement=null,this.eventsAdded=!1}mapPositionToPoint(t,e,s){const i=this.domElement.isConnected?this.domElement.getBoundingClientRect():{x:0,y:0,width:this.domElement.width,height:this.domElement.height,left:0,top:0},n=1/this.resolution;t.x=(e-i.left)*(this.domElement.width/i.width)*n,t.y=(s-i.top)*(this.domElement.height/i.height)*n}normalizeToPointerData(t){const e=[];if(this.supportsTouchEvents&&t instanceof TouchEvent)for(let s=0,i=t.changedTouches.length;s<i;s++){const n=t.changedTouches[s];typeof n.button=="undefined"&&(n.button=0),typeof n.buttons=="undefined"&&(n.buttons=1),typeof n.isPrimary=="undefined"&&(n.isPrimary=t.touches.length===1&&t.type==="touchstart"),typeof n.width=="undefined"&&(n.width=n.radiusX||1),typeof n.height=="undefined"&&(n.height=n.radiusY||1),typeof n.tiltX=="undefined"&&(n.tiltX=0),typeof n.tiltY=="undefined"&&(n.tiltY=0),typeof n.pointerType=="undefined"&&(n.pointerType="touch"),typeof n.pointerId=="undefined"&&(n.pointerId=n.identifier||0),typeof n.pressure=="undefined"&&(n.pressure=n.force||.5),typeof n.twist=="undefined"&&(n.twist=0),typeof n.tangentialPressure=="undefined"&&(n.tangentialPressure=0),typeof n.layerX=="undefined"&&(n.layerX=n.offsetX=n.clientX),typeof n.layerY=="undefined"&&(n.layerY=n.offsetY=n.clientY),n.isNormalized=!0,n.type=t.type,e.push(n)}else if(!globalThis.MouseEvent||t instanceof MouseEvent&&(!this.supportsPointerEvents||!(t instanceof globalThis.PointerEvent))){const s=t;typeof s.isPrimary=="undefined"&&(s.isPrimary=!0),typeof s.width=="undefined"&&(s.width=1),typeof s.height=="undefined"&&(s.height=1),typeof s.tiltX=="undefined"&&(s.tiltX=0),typeof s.tiltY=="undefined"&&(s.tiltY=0),typeof s.pointerType=="undefined"&&(s.pointerType="mouse"),typeof s.pointerId=="undefined"&&(s.pointerId=Qf),typeof s.pressure=="undefined"&&(s.pressure=.5),typeof s.twist=="undefined"&&(s.twist=0),typeof s.tangentialPressure=="undefined"&&(s.tangentialPressure=0),s.isNormalized=!0,e.push(s)}else e.push(t);return e}normalizeWheelEvent(t){const e=this.rootWheelEvent;return this.transferMouseData(e,t),e.deltaX=t.deltaX,e.deltaY=t.deltaY,e.deltaZ=t.deltaZ,e.deltaMode=t.deltaMode,this.mapPositionToPoint(e.screen,t.clientX,t.clientY),e.global.copyFrom(e.screen),e.offset.copyFrom(e.screen),e.nativeEvent=t,e.type=t.type,e}bootstrapEvent(t,e){return t.originalEvent=null,t.nativeEvent=e,t.pointerId=e.pointerId,t.width=e.width,t.height=e.height,t.isPrimary=e.isPrimary,t.pointerType=e.pointerType,t.pressure=e.pressure,t.tangentialPressure=e.tangentialPressure,t.tiltX=e.tiltX,t.tiltY=e.tiltY,t.twist=e.twist,this.transferMouseData(t,e),this.mapPositionToPoint(t.screen,e.clientX,e.clientY),t.global.copyFrom(t.screen),t.offset.copyFrom(t.screen),t.isTrusted=e.isTrusted,t.type==="pointerleave"&&(t.type="pointerout"),t.type.startsWith("mouse")&&(t.type=t.type.replace("mouse","pointer")),t.type.startsWith("touch")&&(t.type=Jf[t.type]||t.type),t}transferMouseData(t,e){t.isTrusted=e.isTrusted,t.srcElement=e.srcElement,t.timeStamp=performance.now(),t.type=e.type,t.altKey=e.altKey,t.button=e.button,t.buttons=e.buttons,t.client.x=e.clientX,t.client.y=e.clientY,t.ctrlKey=e.ctrlKey,t.metaKey=e.metaKey,t.movement.x=e.movementX,t.movement.y=e.movementY,t.page.x=e.pageX,t.page.y=e.pageY,t.relatedTarget=null,t.shiftKey=e.shiftKey}};Qn.extension={name:"events",type:[D.RendererSystem,D.CanvasRendererSystem]},Qn.defaultEventFeatures={move:!0,globalMove:!0,click:!0,wheel:!0};let si=Qn;U.add(si);function Kh(r){return r==="dynamic"||r==="static"}const Zh={onclick:null,onmousedown:null,onmouseenter:null,onmouseleave:null,onmousemove:null,onglobalmousemove:null,onmouseout:null,onmouseover:null,onmouseup:null,onmouseupoutside:null,onpointercancel:null,onpointerdown:null,onpointerenter:null,onpointerleave:null,onpointermove:null,onglobalpointermove:null,onpointerout:null,onpointerover:null,onpointertap:null,onpointerup:null,onpointerupoutside:null,onrightclick:null,onrightdown:null,onrightup:null,onrightupoutside:null,ontap:null,ontouchcancel:null,ontouchend:null,ontouchendoutside:null,ontouchmove:null,onglobaltouchmove:null,ontouchstart:null,onwheel:null,_internalInteractive:void 0,get interactive(){var r;return(r=this._internalInteractive)!=null?r:Kh(si.defaultEventMode)},set interactive(r){this._internalInteractive=r,this.eventMode=r?"static":"auto"},_internalEventMode:void 0,get eventMode(){var r;return(r=this._internalEventMode)!=null?r:si.defaultEventMode},set eventMode(r){this._internalInteractive=Kh(r),this._internalEventMode=r},isInteractive(){return this.eventMode==="static"||this.eventMode==="dynamic"},interactiveChildren:!0,hitArea:null,addEventListener(r,t,e){const s=typeof e=="boolean"&&e||typeof e=="object"&&e.capture,i=typeof e=="object"?e.signal:void 0,n=typeof e=="object"?e.once===!0:!1,a=typeof t=="function"?void 0:t;r=s?`${r}capture`:r;const o=typeof t=="function"?t:t.handleEvent,h=this;i&&i.addEventListener("abort",()=>{h.off(r,o,a)}),n?h.once(r,o,a):h.on(r,o,a)},removeEventListener(r,t,e){const s=typeof e=="boolean"&&e||typeof e=="object"&&e.capture,i=typeof t=="function"?void 0:t;r=s?`${r}capture`:r,t=typeof t=="function"?t:t.handleEvent,this.off(r,t,i)},dispatchEvent(r){if(!(r instanceof Je))throw new Error("DisplayObject cannot propagate events outside of the Federated Events API");return r.defaultPrevented=!1,r.path=null,r.target=this,r.manager.dispatchEvent(r),!r.defaultPrevented}};ot.mixin(Zh);const Qh={accessible:!1,accessibleTitle:null,accessibleHint:null,tabIndex:0,_accessibleActive:!1,_accessibleDiv:null,accessibleType:"button",accessiblePointerEvents:"auto",accessibleChildren:!0,renderId:-1};ot.mixin(Qh);const tp=9,ri=100,ep=0,sp=0,Jh=2,tl=1,rp=-1e3,ip=-1e3,np=2;class Jn{constructor(t){this.debug=!1,this._isActive=!1,this._isMobileAccessibility=!1,this.pool=[],this.renderId=0,this.children=[],this.androidUpdateCount=0,this.androidUpdateFrequency=500,this._hookDiv=null,(Xt.tablet||Xt.phone)&&this.createTouchHook();const e=document.createElement("div");e.style.width=`${ri}px`,e.style.height=`${ri}px`,e.style.position="absolute",e.style.top=`${ep}px`,e.style.left=`${sp}px`,e.style.zIndex=Jh.toString(),this.div=e,this.renderer=t,this._onKeyDown=this._onKeyDown.bind(this),this._onMouseMove=this._onMouseMove.bind(this),globalThis.addEventListener("keydown",this._onKeyDown,!1)}get isActive(){return this._isActive}get isMobileAccessibility(){return this._isMobileAccessibility}createTouchHook(){const t=document.createElement("button");t.style.width=`${tl}px`,t.style.height=`${tl}px`,t.style.position="absolute",t.style.top=`${rp}px`,t.style.left=`${ip}px`,t.style.zIndex=np.toString(),t.style.backgroundColor="#FF0000",t.title="select to enable accessibility for this content",t.addEventListener("focus",()=>{this._isMobileAccessibility=!0,this.activate(),this.destroyTouchHook()}),document.body.appendChild(t),this._hookDiv=t}destroyTouchHook(){this._hookDiv&&(document.body.removeChild(this._hookDiv),this._hookDiv=null)}activate(){var t;this._isActive||(this._isActive=!0,globalThis.document.addEventListener("mousemove",this._onMouseMove,!0),globalThis.removeEventListener("keydown",this._onKeyDown,!1),this.renderer.on("postrender",this.update,this),(t=this.renderer.view.parentNode)==null||t.appendChild(this.div))}deactivate(){var t;!this._isActive||this._isMobileAccessibility||(this._isActive=!1,globalThis.document.removeEventListener("mousemove",this._onMouseMove,!0),globalThis.addEventListener("keydown",this._onKeyDown,!1),this.renderer.off("postrender",this.update),(t=this.div.parentNode)==null||t.removeChild(this.div))}updateAccessibleObjects(t){if(!t.visible||!t.accessibleChildren)return;t.accessible&&t.isInteractive()&&(t._accessibleActive||this.addChild(t),t.renderId=this.renderId);const e=t.children;if(e)for(let s=0;s<e.length;s++)this.updateAccessibleObjects(e[s])}update(){const t=performance.now();if(Xt.android.device&&t<this.androidUpdateCount||(this.androidUpdateCount=t+this.androidUpdateFrequency,!this.renderer.renderingToScreen))return;this.renderer.lastObjectRendered&&this.updateAccessibleObjects(this.renderer.lastObjectRendered);const{x:e,y:s,width:i,height:n}=this.renderer.view.getBoundingClientRect(),{width:a,height:o,resolution:h}=this.renderer,l=i/a*h,u=n/o*h;let c=this.div;c.style.left=`${e}px`,c.style.top=`${s}px`,c.style.width=`${a}px`,c.style.height=`${o}px`;for(let d=0;d<this.children.length;d++){const f=this.children[d];if(f.renderId!==this.renderId)f._accessibleActive=!1,Oe(this.children,d,1),this.div.removeChild(f._accessibleDiv),this.pool.push(f._accessibleDiv),f._accessibleDiv=null,d--;else{c=f._accessibleDiv;let p=f.hitArea;const m=f.worldTransform;f.hitArea?(c.style.left=`${(m.tx+p.x*m.a)*l}px`,c.style.top=`${(m.ty+p.y*m.d)*u}px`,c.style.width=`${p.width*m.a*l}px`,c.style.height=`${p.height*m.d*u}px`):(p=f.getBounds(),this.capHitArea(p),c.style.left=`${p.x*l}px`,c.style.top=`${p.y*u}px`,c.style.width=`${p.width*l}px`,c.style.height=`${p.height*u}px`,c.title!==f.accessibleTitle&&f.accessibleTitle!==null&&(c.title=f.accessibleTitle),c.getAttribute("aria-label")!==f.accessibleHint&&f.accessibleHint!==null&&c.setAttribute("aria-label",f.accessibleHint)),(f.accessibleTitle!==c.title||f.tabIndex!==c.tabIndex)&&(c.title=f.accessibleTitle,c.tabIndex=f.tabIndex,this.debug&&this.updateDebugHTML(c))}}this.renderId++}updateDebugHTML(t){t.innerHTML=`type: ${t.type}</br> title : ${t.title}</br> tabIndex: ${t.tabIndex}`}capHitArea(t){t.x<0&&(t.width+=t.x,t.x=0),t.y<0&&(t.height+=t.y,t.y=0);const{width:e,height:s}=this.renderer;t.x+t.width>e&&(t.width=e-t.x),t.y+t.height>s&&(t.height=s-t.y)}addChild(t){let e=this.pool.pop();e||(e=document.createElement("button"),e.style.width=`${ri}px`,e.style.height=`${ri}px`,e.style.backgroundColor=this.debug?"rgba(255,255,255,0.5)":"transparent",e.style.position="absolute",e.style.zIndex=Jh.toString(),e.style.borderStyle="none",navigator.userAgent.toLowerCase().includes("chrome")?e.setAttribute("aria-live","off"):e.setAttribute("aria-live","polite"),navigator.userAgent.match(/rv:.*Gecko\//)?e.setAttribute("aria-relevant","additions"):e.setAttribute("aria-relevant","text"),e.addEventListener("click",this._onClick.bind(this)),e.addEventListener("focus",this._onFocus.bind(this)),e.addEventListener("focusout",this._onFocusOut.bind(this))),e.style.pointerEvents=t.accessiblePointerEvents,e.type=t.accessibleType,t.accessibleTitle&&t.accessibleTitle!==null?e.title=t.accessibleTitle:(!t.accessibleHint||t.accessibleHint===null)&&(e.title=`displayObject ${t.tabIndex}`),t.accessibleHint&&t.accessibleHint!==null&&e.setAttribute("aria-label",t.accessibleHint),this.debug&&this.updateDebugHTML(e),t._accessibleActive=!0,t._accessibleDiv=e,e.displayObject=t,this.children.push(t),this.div.appendChild(t._accessibleDiv),t._accessibleDiv.tabIndex=t.tabIndex}_dispatchEvent(t,e){const{displayObject:s}=t.target,i=this.renderer.events.rootBoundary,n=Object.assign(new Je(i),{target:s});i.rootTarget=this.renderer.lastObjectRendered,e.forEach(a=>i.dispatchEvent(n,a))}_onClick(t){this._dispatchEvent(t,["click","pointertap","tap"])}_onFocus(t){t.target.getAttribute("aria-live")||t.target.setAttribute("aria-live","assertive"),this._dispatchEvent(t,["mouseover"])}_onFocusOut(t){t.target.getAttribute("aria-live")||t.target.setAttribute("aria-live","polite"),this._dispatchEvent(t,["mouseout"])}_onKeyDown(t){t.keyCode===tp&&this.activate()}_onMouseMove(t){t.movementX===0&&t.movementY===0||this.deactivate()}destroy(){this.destroyTouchHook(),this.div=null,globalThis.document.removeEventListener("mousemove",this._onMouseMove,!0),globalThis.removeEventListener("keydown",this._onKeyDown),this.pool=null,this.children=null,this.renderer=null}}Jn.extension={name:"accessibility",type:[D.RendererPlugin,D.CanvasRendererPlugin]},U.add(Jn);const el=class Ma{constructor(t){this.stage=new Ct,t=Object.assign({forceCanvas:!1},t),this.renderer=Rh(t),Ma._plugins.forEach(e=>{e.init.call(this,t)})}render(){this.renderer.render(this.stage)}get view(){var t;return(t=this.renderer)==null?void 0:t.view}get screen(){var t;return(t=this.renderer)==null?void 0:t.screen}destroy(t,e){const s=Ma._plugins.slice(0);s.reverse(),s.forEach(i=>{i.destroy.call(this)}),this.stage.destroy(e),this.stage=null,this.renderer.destroy(t),this.renderer=null}};el._plugins=[];let sl=el;U.handleByList(D.Application,sl._plugins);class ta{static init(t){Object.defineProperty(this,"resizeTo",{set(e){globalThis.removeEventListener("resize",this.queueResize),this._resizeTo=e,e&&(globalThis.addEventListener("resize",this.queueResize),this.resize())},get(){return this._resizeTo}}),this.queueResize=()=>{this._resizeTo&&(this.cancelResize(),this._resizeId=requestAnimationFrame(()=>this.resize()))},this.cancelResize=()=>{this._resizeId&&(cancelAnimationFrame(this._resizeId),this._resizeId=null)},this.resize=()=>{if(!this._resizeTo)return;this.cancelResize();let e,s;if(this._resizeTo===globalThis.window)e=globalThis.innerWidth,s=globalThis.innerHeight;else{const{clientWidth:i,clientHeight:n}=this._resizeTo;e=i,s=n}this.renderer.resize(e,s),this.render()},this._resizeId=null,this._resizeTo=null,this.resizeTo=t.resizeTo||null}static destroy(){globalThis.removeEventListener("resize",this.queueResize),this.cancelResize(),this.cancelResize=null,this.queueResize=null,this.resizeTo=null,this.resize=null}}ta.extension=D.Application,U.add(ta);const rl={loader:D.LoadParser,resolver:D.ResolveParser,cache:D.CacheParser,detection:D.DetectionParser};U.handle(D.Asset,r=>{const t=r.ref;Object.entries(rl).filter(([e])=>!!t[e]).forEach(([e,s])=>{var i;return U.add(Object.assign(t[e],{extension:(i=t[e].extension)!=null?i:s}))})},r=>{const t=r.ref;Object.keys(rl).filter(e=>!!t[e]).forEach(e=>U.remove(t[e]))});class ap{constructor(t,e=!1){this._loader=t,this._assetList=[],this._isLoading=!1,this._maxConcurrent=1,this.verbose=e}add(t){t.forEach(e=>{this._assetList.push(e)}),this.verbose&&console.log("[BackgroundLoader] assets: ",this._assetList),this._isActive&&!this._isLoading&&this._next()}async _next(){if(this._assetList.length&&this._isActive){this._isLoading=!0;const t=[],e=Math.min(this._assetList.length,this._maxConcurrent);for(let s=0;s<e;s++)t.push(this._assetList.pop());await this._loader.load(t),this._isLoading=!1,this._next()}}get active(){return this._isActive}set active(t){this._isActive!==t&&(this._isActive=t,t&&!this._isLoading&&this._next())}}function Ve(r,t){if(Array.isArray(t)){for(const e of t)if(r.startsWith(`data:${e}`))return!0;return!1}return r.startsWith(`data:${t}`)}function ve(r,t){const e=r.split("?")[0],s=gt.extname(e).toLowerCase();return Array.isArray(t)?t.includes(s):s===t}const Gt=(r,t,e=!1)=>(Array.isArray(r)||(r=[r]),t?r.map(s=>typeof s=="string"||e?t(s):s):r),ii=(r,t)=>{const e=t.split("?")[1];return e&&(r+=`?${e}`),r};function il(r,t,e,s,i){const n=t[e];for(let a=0;a<n.length;a++){const o=n[a];e<t.length-1?il(r.replace(s[e],o),t,e+1,s,i):i.push(r.replace(s[e],o))}}function nl(r){const t=/\{(.*?)\}/g,e=r.match(t),s=[];if(e){const i=[];e.forEach(n=>{const a=n.substring(1,n.length-1).split(",");i.push(a)}),il(r,i,0,e,s)}else s.push(r);return s}const Vs=r=>!Array.isArray(r);let op=class{constructor(){this._parsers=[],this._cache=new Map,this._cacheMap=new Map}reset(){this._cacheMap.clear(),this._cache.clear()}has(t){return this._cache.has(t)}get(t){return this._cache.get(t)}set(t,e){const s=Gt(t);let i;for(let o=0;o<this.parsers.length;o++){const h=this.parsers[o];if(h.test(e)){i=h.getCacheableAssets(s,e);break}}i||(i={},s.forEach(o=>{i[o]=e}));const n=Object.keys(i),a={cacheKeys:n,keys:s};if(s.forEach(o=>{this._cacheMap.set(o,a)}),n.forEach(o=>{this._cache.has(o)&&this._cache.get(o),this._cache.set(o,i[o])}),e instanceof L){const o=e;s.forEach(h=>{o.baseTexture!==L.EMPTY.baseTexture&&X.addToCache(o.baseTexture,h),L.addToCache(o,h)})}}remove(t){if(!this._cacheMap.has(t))return;const e=this._cacheMap.get(t);e.cacheKeys.forEach(s=>{this._cache.delete(s)}),e.keys.forEach(s=>{this._cacheMap.delete(s)})}get parsers(){return this._parsers}};const Se=new op;var hp=Object.defineProperty,lp=Object.defineProperties,up=Object.getOwnPropertyDescriptors,al=Object.getOwnPropertySymbols,cp=Object.prototype.hasOwnProperty,dp=Object.prototype.propertyIsEnumerable,ol=(r,t,e)=>t in r?hp(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,fp=(r,t)=>{for(var e in t||(t={}))cp.call(t,e)&&ol(r,e,t[e]);if(al)for(var e of al(t))dp.call(t,e)&&ol(r,e,t[e]);return r},pp=(r,t)=>lp(r,up(t));class mp{constructor(){this._parsers=[],this._parsersValidated=!1,this.parsers=new Proxy(this._parsers,{set:(t,e,s)=>(this._parsersValidated=!1,t[e]=s,!0)}),this.promiseCache={}}reset(){this._parsersValidated=!1,this.promiseCache={}}_getLoadPromiseAndParser(t,e){const s={promise:null,parser:null};return s.promise=(async()=>{var i,n;let a=null,o=null;if(e.loadParser&&(o=this._parserHash[e.loadParser]),!o){for(let h=0;h<this.parsers.length;h++){const l=this.parsers[h];if(l.load&&(i=l.test)!=null&&i.call(l,t,e,this)){o=l;break}}if(!o)return null}a=await o.load(t,e,this),s.parser=o;for(let h=0;h<this.parsers.length;h++){const l=this.parsers[h];l.parse&&l.parse&&await((n=l.testParse)==null?void 0:n.call(l,a,e,this))&&(a=await l.parse(a,e,this)||a,s.parser=l)}return a})(),s}async load(t,e){this._parsersValidated||this._validateParsers();let s=0;const i={},n=Vs(t),a=Gt(t,l=>({alias:[l],src:l})),o=a.length,h=a.map(async l=>{const u=gt.toAbsolute(l.src);if(!i[l.src])try{this.promiseCache[u]||(this.promiseCache[u]=this._getLoadPromiseAndParser(u,l)),i[l.src]=await this.promiseCache[u].promise,e&&e(++s/o)}catch(c){throw delete this.promiseCache[u],delete i[l.src],new Error(`[Loader.load] Failed to load ${u}.
${c}`)}});return await Promise.all(h),n?i[a[0].src]:i}async unload(t){const e=Gt(t,s=>({alias:[s],src:s})).map(async s=>{var i,n;const a=gt.toAbsolute(s.src),o=this.promiseCache[a];if(o){const h=await o.promise;delete this.promiseCache[a],(n=(i=o.parser)==null?void 0:i.unload)==null||n.call(i,h,s,this)}});await Promise.all(e)}_validateParsers(){this._parsersValidated=!0,this._parserHash=this._parsers.filter(t=>t.name).reduce((t,e)=>(t[e.name],pp(fp({},t),{[e.name]:e})),{})}}var $t=(r=>(r[r.Low=0]="Low",r[r.Normal=1]="Normal",r[r.High=2]="High",r))($t||{});const gp=".json",_p="application/json",hl={extension:{type:D.LoadParser,priority:$t.Low},name:"loadJson",test(r){return Ve(r,_p)||ve(r,gp)},async load(r){return await(await N.ADAPTER.fetch(r)).json()}};U.add(hl);const vp=".txt",yp="text/plain",ll={name:"loadTxt",extension:{type:D.LoadParser,priority:$t.Low},test(r){return Ve(r,yp)||ve(r,vp)},async load(r){return await(await N.ADAPTER.fetch(r)).text()}};U.add(ll);var xp=Object.defineProperty,bp=Object.defineProperties,Tp=Object.getOwnPropertyDescriptors,ul=Object.getOwnPropertySymbols,Ep=Object.prototype.hasOwnProperty,Ap=Object.prototype.propertyIsEnumerable,cl=(r,t,e)=>t in r?xp(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,wp=(r,t)=>{for(var e in t||(t={}))Ep.call(t,e)&&cl(r,e,t[e]);if(ul)for(var e of ul(t))Ap.call(t,e)&&cl(r,e,t[e]);return r},Sp=(r,t)=>bp(r,Tp(t));const Cp=["normal","bold","100","200","300","400","500","600","700","800","900"],Rp=[".ttf",".otf",".woff",".woff2"],Ip=["font/ttf","font/otf","font/woff","font/woff2"],Pp=/^(--|-?[A-Z_])[0-9A-Z_-]*$/i;function dl(r){const t=gt.extname(r),e=gt.basename(r,t).replace(/(-|_)/g," ").toLowerCase().split(" ").map(n=>n.charAt(0).toUpperCase()+n.slice(1));let s=e.length>0;for(const n of e)if(!n.match(Pp)){s=!1;break}let i=e.join(" ");return s||(i=`"${i.replace(/[\\"]/g,"\\$&")}"`),i}const Mp=/^[0-9A-Za-z%:/?#\[\]@!\$&'()\*\+,;=\-._~]*$/;function Dp(r){return Mp.test(r)?r:encodeURI(r)}const fl={extension:{type:D.LoadParser,priority:$t.Low},name:"loadWebFont",test(r){return Ve(r,Ip)||ve(r,Rp)},async load(r,t){var e,s,i,n,a,o;const h=N.ADAPTER.getFontFaceSet();if(h){const l=[],u=(s=(e=t.data)==null?void 0:e.family)!=null?s:dl(r),c=(a=(n=(i=t.data)==null?void 0:i.weights)==null?void 0:n.filter(f=>Cp.includes(f)))!=null?a:["normal"],d=(o=t.data)!=null?o:{};for(let f=0;f<c.length;f++){const p=c[f],m=new FontFace(u,`url(${Dp(r)})`,Sp(wp({},d),{weight:p}));await m.load(),h.add(m),l.push(m)}return l.length===1?l[0]:l}return null},unload(r){(Array.isArray(r)?r:[r]).forEach(t=>N.ADAPTER.getFontFaceSet().delete(t))}};U.add(fl);const Op=`(function(){"use strict";const e="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAwMCAO+ip1sAAAAASUVORK5CYII=";async function a(){try{if(typeof createImageBitmap!="function")return!1;const A=await(await fetch(e)).blob(),t=await createImageBitmap(A);return t.width===1&&t.height===1}catch(A){return!1}}a().then(A=>{self.postMessage(A)})})();
`;let ts=null,ea=class{constructor(){ts||(ts=URL.createObjectURL(new Blob([Op],{type:"application/javascript"}))),this.worker=new Worker(ts)}};ea.revokeObjectURL=function(){ts&&(URL.revokeObjectURL(ts),ts=null)};const Bp='(function(){"use strict";async function e(t){const a=await fetch(t);if(!a.ok)throw new Error(`[WorkerManager.loadImageBitmap] Failed to fetch ${t}: ${a.status} ${a.statusText}`);const s=await a.blob();return await createImageBitmap(s)}self.onmessage=async t=>{try{const a=await e(t.data.data[0]);self.postMessage({data:a,uuid:t.data.uuid,id:t.data.id},[a])}catch(a){self.postMessage({error:a,uuid:t.data.uuid,id:t.data.id})}}})();\n';let es=null;class pl{constructor(){es||(es=URL.createObjectURL(new Blob([Bp],{type:"application/javascript"}))),this.worker=new Worker(es)}}pl.revokeObjectURL=function(){es&&(URL.revokeObjectURL(es),es=null)};let ml=0,sa,Fp=class{constructor(){this._initialized=!1,this._createdWorkers=0,this.workerPool=[],this.queue=[],this.resolveHash={}}isImageBitmapSupported(){return this._isImageBitmapSupported!==void 0?this._isImageBitmapSupported:(this._isImageBitmapSupported=new Promise(t=>{const{worker:e}=new ea;e.addEventListener("message",s=>{e.terminate(),ea.revokeObjectURL(),t(s.data)})}),this._isImageBitmapSupported)}loadImageBitmap(t){return this._run("loadImageBitmap",[t])}async _initWorkers(){this._initialized||(this._initialized=!0)}getWorker(){sa===void 0&&(sa=navigator.hardwareConcurrency||4);let t=this.workerPool.pop();return!t&&this._createdWorkers<sa&&(this._createdWorkers++,t=new pl().worker,t.addEventListener("message",e=>{this.complete(e.data),this.returnWorker(e.target),this.next()})),t}returnWorker(t){this.workerPool.push(t)}complete(t){t.error!==void 0?this.resolveHash[t.uuid].reject(t.error):this.resolveHash[t.uuid].resolve(t.data),this.resolveHash[t.uuid]=null}async _run(t,e){await this._initWorkers();const s=new Promise((i,n)=>{this.queue.push({id:t,arguments:e,resolve:i,reject:n})});return this.next(),s}next(){if(!this.queue.length)return;const t=this.getWorker();if(!t)return;const e=this.queue.pop(),s=e.id;this.resolveHash[ml]={resolve:e.resolve,reject:e.reject},t.postMessage({data:e.arguments,uuid:ml++,id:s})}};const gl=new Fp;function ss(r,t,e){r.resource.internal=!0;const s=new L(r),i=()=>{delete t.promiseCache[e],Se.has(e)&&Se.remove(e)};return s.baseTexture.once("destroyed",()=>{e in t.promiseCache&&(console.warn("[Assets] A BaseTexture managed by Assets was destroyed instead of unloaded! Use Assets.unload() instead of destroying the BaseTexture."),i())}),s.once("destroyed",()=>{r.destroyed||(console.warn("[Assets] A Texture managed by Assets was destroyed instead of unloaded! Use Assets.unload() instead of destroying the Texture."),i())}),s}var Np=Object.defineProperty,_l=Object.getOwnPropertySymbols,Lp=Object.prototype.hasOwnProperty,Up=Object.prototype.propertyIsEnumerable,vl=(r,t,e)=>t in r?Np(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,yl=(r,t)=>{for(var e in t||(t={}))Lp.call(t,e)&&vl(r,e,t[e]);if(_l)for(var e of _l(t))Up.call(t,e)&&vl(r,e,t[e]);return r};const kp=[".jpeg",".jpg",".png",".webp",".avif"],Gp=["image/jpeg","image/png","image/webp","image/avif"];async function xl(r){const t=await N.ADAPTER.fetch(r);if(!t.ok)throw new Error(`[loadImageBitmap] Failed to fetch ${r}: ${t.status} ${t.statusText}`);const e=await t.blob();return await createImageBitmap(e)}const js={name:"loadTextures",extension:{type:D.LoadParser,priority:$t.High},config:{preferWorkers:!0,preferCreateImageBitmap:!0,crossOrigin:"anonymous"},test(r){return Ve(r,Gp)||ve(r,kp)},async load(r,t,e){var s,i;const n=globalThis.createImageBitmap&&this.config.preferCreateImageBitmap;let a;n?this.config.preferWorkers&&await gl.isImageBitmapSupported()?a=await gl.loadImageBitmap(r):a=await xl(r):a=await new Promise((l,u)=>{const c=new Image;c.crossOrigin=this.config.crossOrigin,c.src=r,c.complete?l(c):(c.onload=()=>l(c),c.onerror=d=>u(d))});const o=yl({},t.data);(s=o.resolution)!=null||(o.resolution=te(r)),n&&((i=o.resourceOptions)==null?void 0:i.ownsImageBitmap)===void 0&&(o.resourceOptions=yl({},o.resourceOptions),o.resourceOptions.ownsImageBitmap=!0);const h=new X(a,o);return h.resource.src=r,ss(h,e,r)},unload(r){r.destroy(!0)}};U.add(js);var $p=Object.defineProperty,bl=Object.getOwnPropertySymbols,Hp=Object.prototype.hasOwnProperty,Vp=Object.prototype.propertyIsEnumerable,Tl=(r,t,e)=>t in r?$p(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,jp=(r,t)=>{for(var e in t||(t={}))Hp.call(t,e)&&Tl(r,e,t[e]);if(bl)for(var e of bl(t))Vp.call(t,e)&&Tl(r,e,t[e]);return r};const Xp=".svg",zp="image/svg+xml",El={extension:{type:D.LoadParser,priority:$t.High},name:"loadSVG",test(r){return Ve(r,zp)||ve(r,Xp)},async testParse(r){return Wr.test(r)},async parse(r,t,e){var s;const i=new Wr(r,(s=t==null?void 0:t.data)==null?void 0:s.resourceOptions);await i.load();const n=new X(i,jp({resolution:te(r)},t==null?void 0:t.data));return n.resource.src=t.src,ss(n,e,t.src)},async load(r,t){return(await N.ADAPTER.fetch(r)).text()},unload:js.unload};U.add(El);var Wp=Object.defineProperty,Yp=Object.defineProperties,qp=Object.getOwnPropertyDescriptors,Al=Object.getOwnPropertySymbols,Kp=Object.prototype.hasOwnProperty,Zp=Object.prototype.propertyIsEnumerable,wl=(r,t,e)=>t in r?Wp(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,Sl=(r,t)=>{for(var e in t||(t={}))Kp.call(t,e)&&wl(r,e,t[e]);if(Al)for(var e of Al(t))Zp.call(t,e)&&wl(r,e,t[e]);return r},Qp=(r,t)=>Yp(r,qp(t));const Jp=[".mp4",".m4v",".webm",".ogv"],tm=["video/mp4","video/webm","video/ogg"],Cl={name:"loadVideo",extension:{type:D.LoadParser,priority:$t.High},config:{defaultAutoPlay:!0,defaultUpdateFPS:0,defaultLoop:!1,defaultMuted:!1,defaultPlaysinline:!0},test(r){return Ve(r,tm)||ve(r,Jp)},async load(r,t,e){var s;let i;const n=await(await N.ADAPTER.fetch(r)).blob(),a=URL.createObjectURL(n);try{const o=Qp(Sl({autoPlay:this.config.defaultAutoPlay,updateFPS:this.config.defaultUpdateFPS,loop:this.config.defaultLoop,muted:this.config.defaultMuted,playsinline:this.config.defaultPlaysinline},(s=t==null?void 0:t.data)==null?void 0:s.resourceOptions),{autoLoad:!0}),h=new $n(a,o);await h.load();const l=new X(h,Sl({alphaMode:await oo(),resolution:te(r)},t==null?void 0:t.data));l.resource.src=r,i=ss(l,e,r),i.baseTexture.once("destroyed",()=>{URL.revokeObjectURL(a)})}catch(o){throw URL.revokeObjectURL(a),o}return i},unload(r){r.destroy(!0)}};U.add(Cl);var em=Object.defineProperty,sm=Object.defineProperties,rm=Object.getOwnPropertyDescriptors,Rl=Object.getOwnPropertySymbols,im=Object.prototype.hasOwnProperty,nm=Object.prototype.propertyIsEnumerable,Il=(r,t,e)=>t in r?em(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,rs=(r,t)=>{for(var e in t||(t={}))im.call(t,e)&&Il(r,e,t[e]);if(Rl)for(var e of Rl(t))nm.call(t,e)&&Il(r,e,t[e]);return r},Pl=(r,t)=>sm(r,rm(t));class am{constructor(){this._defaultBundleIdentifierOptions={connector:"-",createBundleAssetId:(t,e)=>`${t}${this._bundleIdConnector}${e}`,extractAssetIdFromBundle:(t,e)=>e.replace(`${t}${this._bundleIdConnector}`,"")},this._bundleIdConnector=this._defaultBundleIdentifierOptions.connector,this._createBundleAssetId=this._defaultBundleIdentifierOptions.createBundleAssetId,this._extractAssetIdFromBundle=this._defaultBundleIdentifierOptions.extractAssetIdFromBundle,this._assetMap={},this._preferredOrder=[],this._parsers=[],this._resolverHash={},this._bundles={}}setBundleIdentifier(t){var e,s,i;if(this._bundleIdConnector=(e=t.connector)!=null?e:this._bundleIdConnector,this._createBundleAssetId=(s=t.createBundleAssetId)!=null?s:this._createBundleAssetId,this._extractAssetIdFromBundle=(i=t.extractAssetIdFromBundle)!=null?i:this._extractAssetIdFromBundle,this._extractAssetIdFromBundle("foo",this._createBundleAssetId("foo","bar"))!=="bar")throw new Error("[Resolver] GenerateBundleAssetId are not working correctly")}prefer(...t){t.forEach(e=>{this._preferredOrder.push(e),e.priority||(e.priority=Object.keys(e.params))}),this._resolverHash={}}set basePath(t){this._basePath=t}get basePath(){return this._basePath}set rootPath(t){this._rootPath=t}get rootPath(){return this._rootPath}get parsers(){return this._parsers}reset(){this.setBundleIdentifier(this._defaultBundleIdentifierOptions),this._assetMap={},this._preferredOrder=[],this._resolverHash={},this._rootPath=null,this._basePath=null,this._manifest=null,this._bundles={},this._defaultSearchParams=null}setDefaultSearchParams(t){if(typeof t=="string")this._defaultSearchParams=t;else{const e=t;this._defaultSearchParams=Object.keys(e).map(s=>`${encodeURIComponent(s)}=${encodeURIComponent(e[s])}`).join("&")}}getAlias(t){const{alias:e,name:s,src:i,srcs:n}=t;return Gt(e||s||i||n,a=>{var o;return typeof a=="string"?a:Array.isArray(a)?a.map(h=>{var l,u;return(u=(l=h==null?void 0:h.src)!=null?l:h==null?void 0:h.srcs)!=null?u:h}):a!=null&&a.src||a!=null&&a.srcs?(o=a.src)!=null?o:a.srcs:a},!0)}addManifest(t){this._manifest,this._manifest=t,t.bundles.forEach(e=>{this.addBundle(e.name,e.assets)})}addBundle(t,e){const s=[];Array.isArray(e)?e.forEach(i=>{var n,a;const o=(n=i.src)!=null?n:i.srcs,h=(a=i.alias)!=null?a:i.name;let l;if(typeof h=="string"){const u=this._createBundleAssetId(t,h);s.push(u),l=[h,u]}else{const u=h.map(c=>this._createBundleAssetId(t,c));s.push(...u),l=[...h,...u]}this.add(Pl(rs({},i),{alias:l,src:o}))}):Object.keys(e).forEach(i=>{var n;const a=[i,this._createBundleAssetId(t,i)];if(typeof e[i]=="string")this.add({alias:a,src:e[i]});else if(Array.isArray(e[i]))this.add({alias:a,src:e[i]});else{const o=e[i],h=(n=o.src)!=null?n:o.srcs;this.add(Pl(rs({},o),{alias:a,src:Array.isArray(h)?h:[h]}))}s.push(...a)}),this._bundles[t]=s}add(t,e,s,i,n){const a=[];typeof t=="string"||Array.isArray(t)&&typeof t[0]=="string"?a.push({alias:t,src:e,data:s,format:i,loadParser:n}):Array.isArray(t)?a.push(...t):a.push(t);let o;Gt(a).forEach(h=>{const{src:l,srcs:u}=h;let{data:c,format:d,loadParser:f}=h;const p=Gt(l||u).map(_=>typeof _=="string"?nl(_):Array.isArray(_)?_:[_]),m=this.getAlias(h),g=[];p.forEach(_=>{_.forEach(x=>{var y,b,T;let S={};if(typeof x!="object"){S.src=x;for(let A=0;A<this._parsers.length;A++){const w=this._parsers[A];if(w.test(x)){S=w.parse(x);break}}}else c=(y=x.data)!=null?y:c,d=(b=x.format)!=null?b:d,f=(T=x.loadParser)!=null?T:f,S=rs(rs({},S),x);if(!m)throw new Error(`[Resolver] alias is undefined for this asset: ${S.src}`);S=this.buildResolvedAsset(S,{aliases:m,data:c,format:d,loadParser:f}),g.push(S)})}),m.forEach(_=>{this._assetMap[_]=g})})}resolveBundle(t){const e=Vs(t);t=Gt(t);const s={};return t.forEach(i=>{const n=this._bundles[i];if(n){const a=this.resolve(n),o={};for(const h in a){const l=a[h];o[this._extractAssetIdFromBundle(i,h)]=l}s[i]=o}}),e?s[t[0]]:s}resolveUrl(t){const e=this.resolve(t);if(typeof t!="string"){const s={};for(const i in e)s[i]=e[i].src;return s}return e.src}resolve(t){const e=Vs(t);t=Gt(t);const s={};return t.forEach(i=>{var n;if(!this._resolverHash[i])if(this._assetMap[i]){let a=this._assetMap[i];const o=a[0],h=this._getPreferredOrder(a);h==null||h.priority.forEach(l=>{h.params[l].forEach(u=>{const c=a.filter(d=>d[l]?d[l]===u:!1);c.length&&(a=c)})}),this._resolverHash[i]=(n=a[0])!=null?n:o}else this._resolverHash[i]=this.buildResolvedAsset({alias:[i],src:i},{});s[i]=this._resolverHash[i]}),e?s[t[0]]:s}hasKey(t){return!!this._assetMap[t]}hasBundle(t){return!!this._bundles[t]}_getPreferredOrder(t){for(let e=0;e<t.length;e++){const s=t[0],i=this._preferredOrder.find(n=>n.params.format.includes(s.format));if(i)return i}return this._preferredOrder[0]}_appendDefaultSearchParams(t){if(!this._defaultSearchParams)return t;const e=/\?/.test(t)?"&":"?";return`${t}${e}${this._defaultSearchParams}`}buildResolvedAsset(t,e){var s,i;const{aliases:n,data:a,loadParser:o,format:h}=e;return(this._basePath||this._rootPath)&&(t.src=gt.toAbsolute(t.src,this._basePath,this._rootPath)),t.alias=(s=n!=null?n:t.alias)!=null?s:[t.src],t.src=this._appendDefaultSearchParams(t.src),t.data=rs(rs({},a||{}),t.data),t.loadParser=o!=null?o:t.loadParser,t.format=(i=h!=null?h:t.format)!=null?i:gt.extname(t.src).slice(1),t.srcs=t.src,t.name=t.alias,t}}class Ml{constructor(){this._detections=[],this._initialized=!1,this.resolver=new am,this.loader=new mp,this.cache=Se,this._backgroundLoader=new ap(this.loader),this._backgroundLoader.active=!0,this.reset()}async init(t={}){var e,s,i;if(this._initialized)return;if(this._initialized=!0,t.defaultSearchParams&&this.resolver.setDefaultSearchParams(t.defaultSearchParams),t.basePath&&(this.resolver.basePath=t.basePath),t.bundleIdentifier&&this.resolver.setBundleIdentifier(t.bundleIdentifier),t.manifest){let h=t.manifest;typeof h=="string"&&(h=await this.load(h)),this.resolver.addManifest(h)}const n=(s=(e=t.texturePreference)==null?void 0:e.resolution)!=null?s:1,a=typeof n=="number"?[n]:n,o=await this._detectFormats({preferredFormats:(i=t.texturePreference)==null?void 0:i.format,skipDetections:t.skipDetections,detections:this._detections});this.resolver.prefer({params:{format:o,resolution:a}}),t.preferences&&this.setPreferences(t.preferences)}add(t,e,s,i,n){this.resolver.add(t,e,s,i,n)}async load(t,e){this._initialized||await this.init();const s=Vs(t),i=Gt(t).map(o=>{if(typeof o!="string"){const h=this.resolver.getAlias(o);return h.some(l=>!this.resolver.hasKey(l))&&this.add(o),Array.isArray(h)?h[0]:h}return this.resolver.hasKey(o)||this.add({alias:o,src:o}),o}),n=this.resolver.resolve(i),a=await this._mapLoadToResolve(n,e);return s?a[i[0]]:a}addBundle(t,e){this.resolver.addBundle(t,e)}async loadBundle(t,e){this._initialized||await this.init();let s=!1;typeof t=="string"&&(s=!0,t=[t]);const i=this.resolver.resolveBundle(t),n={},a=Object.keys(i);let o=0,h=0;const l=()=>{e==null||e(++o/h)},u=a.map(c=>{const d=i[c];return h+=Object.keys(d).length,this._mapLoadToResolve(d,l).then(f=>{n[c]=f})});return await Promise.all(u),s?n[t[0]]:n}async backgroundLoad(t){this._initialized||await this.init(),typeof t=="string"&&(t=[t]);const e=this.resolver.resolve(t);this._backgroundLoader.add(Object.values(e))}async backgroundLoadBundle(t){this._initialized||await this.init(),typeof t=="string"&&(t=[t]);const e=this.resolver.resolveBundle(t);Object.values(e).forEach(s=>{this._backgroundLoader.add(Object.values(s))})}reset(){this.resolver.reset(),this.loader.reset(),this.cache.reset(),this._initialized=!1}get(t){if(typeof t=="string")return Se.get(t);const e={};for(let s=0;s<t.length;s++)e[s]=Se.get(t[s]);return e}async _mapLoadToResolve(t,e){const s=Object.values(t),i=Object.keys(t);this._backgroundLoader.active=!1;const n=await this.loader.load(s,e);this._backgroundLoader.active=!0;const a={};return s.forEach((o,h)=>{const l=n[o.src],u=[o.src];o.alias&&u.push(...o.alias),a[i[h]]=l,Se.set(u,l)}),a}async unload(t){this._initialized||await this.init();const e=Gt(t).map(i=>typeof i!="string"?i.src:i),s=this.resolver.resolve(e);await this._unloadFromResolved(s)}async unloadBundle(t){this._initialized||await this.init(),t=Gt(t);const e=this.resolver.resolveBundle(t),s=Object.keys(e).map(i=>this._unloadFromResolved(e[i]));await Promise.all(s)}async _unloadFromResolved(t){const e=Object.values(t);e.forEach(s=>{Se.remove(s.src)}),await this.loader.unload(e)}async _detectFormats(t){let e=[];t.preferredFormats&&(e=Array.isArray(t.preferredFormats)?t.preferredFormats:[t.preferredFormats]);for(const s of t.detections)t.skipDetections||await s.test()?e=await s.add(e):t.skipDetections||(e=await s.remove(e));return e=e.filter((s,i)=>e.indexOf(s)===i),e}get detections(){return this._detections}get preferWorkers(){return js.config.preferWorkers}set preferWorkers(t){this.setPreferences({preferWorkers:t})}setPreferences(t){this.loader.parsers.forEach(e=>{e.config&&Object.keys(e.config).filter(s=>s in t).forEach(s=>{e.config[s]=t[s]})})}}const Xs=new Ml;U.handleByList(D.LoadParser,Xs.loader.parsers).handleByList(D.ResolveParser,Xs.resolver.parsers).handleByList(D.CacheParser,Xs.cache.parsers).handleByList(D.DetectionParser,Xs.detections);const Dl={extension:D.CacheParser,test:r=>Array.isArray(r)&&r.every(t=>t instanceof L),getCacheableAssets:(r,t)=>{const e={};return r.forEach(s=>{t.forEach((i,n)=>{e[s+(n===0?"":n+1)]=i})}),e}};U.add(Dl);async function Ol(r){if("Image"in globalThis)return new Promise(t=>{const e=new Image;e.onload=()=>{t(!0)},e.onerror=()=>{t(!1)},e.src=r});if("createImageBitmap"in globalThis&&"fetch"in globalThis){try{const t=await(await fetch(r)).blob();await createImageBitmap(t)}catch(t){return!1}return!0}return!1}const Bl={extension:{type:D.DetectionParser,priority:1},test:async()=>Ol("data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgANogQEAwgMg8f8D///8WfhwB8+ErK42A="),add:async r=>[...r,"avif"],remove:async r=>r.filter(t=>t!=="avif")};U.add(Bl);const Fl={extension:{type:D.DetectionParser,priority:0},test:async()=>Ol("data:image/webp;base64,UklGRh4AAABXRUJQVlA4TBEAAAAvAAAAAAfQ//73v/+BiOh/AAA="),add:async r=>[...r,"webp"],remove:async r=>r.filter(t=>t!=="webp")};U.add(Fl);const Nl=["png","jpg","jpeg"],Ll={extension:{type:D.DetectionParser,priority:-1},test:()=>Promise.resolve(!0),add:async r=>[...r,...Nl],remove:async r=>r.filter(t=>!Nl.includes(t))};U.add(Ll);const om="WorkerGlobalScope"in globalThis&&globalThis instanceof globalThis.WorkerGlobalScope;function ra(r){return om?!1:document.createElement("video").canPlayType(r)!==""}const Ul={extension:{type:D.DetectionParser,priority:0},test:async()=>ra("video/webm"),add:async r=>[...r,"webm"],remove:async r=>r.filter(t=>t!=="webm")};U.add(Ul);const kl={extension:{type:D.DetectionParser,priority:0},test:async()=>ra("video/mp4"),add:async r=>[...r,"mp4","m4v"],remove:async r=>r.filter(t=>t!=="mp4"&&t!=="m4v")};U.add(kl);const Gl={extension:{type:D.DetectionParser,priority:0},test:async()=>ra("video/ogg"),add:async r=>[...r,"ogv"],remove:async r=>r.filter(t=>t!=="ogv")};U.add(Gl);const $l={extension:D.ResolveParser,test:js.test,parse:r=>{var t,e;return{resolution:parseFloat((e=(t=N.RETINA_PREFIX.exec(r))==null?void 0:t[1])!=null?e:"1"),format:gt.extname(r).slice(1),src:r}}};U.add($l);var Tt=(r=>(r[r.COMPRESSED_RGB_S3TC_DXT1_EXT=33776]="COMPRESSED_RGB_S3TC_DXT1_EXT",r[r.COMPRESSED_RGBA_S3TC_DXT1_EXT=33777]="COMPRESSED_RGBA_S3TC_DXT1_EXT",r[r.COMPRESSED_RGBA_S3TC_DXT3_EXT=33778]="COMPRESSED_RGBA_S3TC_DXT3_EXT",r[r.COMPRESSED_RGBA_S3TC_DXT5_EXT=33779]="COMPRESSED_RGBA_S3TC_DXT5_EXT",r[r.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT=35917]="COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT",r[r.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT=35918]="COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT",r[r.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT=35919]="COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT",r[r.COMPRESSED_SRGB_S3TC_DXT1_EXT=35916]="COMPRESSED_SRGB_S3TC_DXT1_EXT",r[r.COMPRESSED_R11_EAC=37488]="COMPRESSED_R11_EAC",r[r.COMPRESSED_SIGNED_R11_EAC=37489]="COMPRESSED_SIGNED_R11_EAC",r[r.COMPRESSED_RG11_EAC=37490]="COMPRESSED_RG11_EAC",r[r.COMPRESSED_SIGNED_RG11_EAC=37491]="COMPRESSED_SIGNED_RG11_EAC",r[r.COMPRESSED_RGB8_ETC2=37492]="COMPRESSED_RGB8_ETC2",r[r.COMPRESSED_RGBA8_ETC2_EAC=37496]="COMPRESSED_RGBA8_ETC2_EAC",r[r.COMPRESSED_SRGB8_ETC2=37493]="COMPRESSED_SRGB8_ETC2",r[r.COMPRESSED_SRGB8_ALPHA8_ETC2_EAC=37497]="COMPRESSED_SRGB8_ALPHA8_ETC2_EAC",r[r.COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2=37494]="COMPRESSED_RGB8_PUNCHTHROUGH_ALPHA1_ETC2",r[r.COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2=37495]="COMPRESSED_SRGB8_PUNCHTHROUGH_ALPHA1_ETC2",r[r.COMPRESSED_RGB_PVRTC_4BPPV1_IMG=35840]="COMPRESSED_RGB_PVRTC_4BPPV1_IMG",r[r.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG=35842]="COMPRESSED_RGBA_PVRTC_4BPPV1_IMG",r[r.COMPRESSED_RGB_PVRTC_2BPPV1_IMG=35841]="COMPRESSED_RGB_PVRTC_2BPPV1_IMG",r[r.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG=35843]="COMPRESSED_RGBA_PVRTC_2BPPV1_IMG",r[r.COMPRESSED_RGB_ETC1_WEBGL=36196]="COMPRESSED_RGB_ETC1_WEBGL",r[r.COMPRESSED_RGB_ATC_WEBGL=35986]="COMPRESSED_RGB_ATC_WEBGL",r[r.COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL=35987]="COMPRESSED_RGBA_ATC_EXPLICIT_ALPHA_WEBGL",r[r.COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL=34798]="COMPRESSED_RGBA_ATC_INTERPOLATED_ALPHA_WEBGL",r[r.COMPRESSED_RGBA_ASTC_4x4_KHR=37808]="COMPRESSED_RGBA_ASTC_4x4_KHR",r[r.COMPRESSED_RGBA_BPTC_UNORM_EXT=36492]="COMPRESSED_RGBA_BPTC_UNORM_EXT",r[r.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT=36493]="COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT",r[r.COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT=36494]="COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT",r[r.COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT=36495]="COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT",r))(Tt||{});const zs={33776:.5,33777:.5,33778:1,33779:1,35916:.5,35917:.5,35918:1,35919:1,37488:.5,37489:.5,37490:1,37491:1,37492:.5,37496:1,37493:.5,37497:1,37494:.5,37495:.5,35840:.5,35842:.5,35841:.25,35843:.25,36196:.5,35986:.5,35987:1,34798:1,37808:1,36492:1,36493:1,36494:1,36495:1};let ae,is;function Hl(){is={bptc:ae.getExtension("EXT_texture_compression_bptc"),astc:ae.getExtension("WEBGL_compressed_texture_astc"),etc:ae.getExtension("WEBGL_compressed_texture_etc"),s3tc:ae.getExtension("WEBGL_compressed_texture_s3tc"),s3tc_sRGB:ae.getExtension("WEBGL_compressed_texture_s3tc_srgb"),pvrtc:ae.getExtension("WEBGL_compressed_texture_pvrtc")||ae.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc"),etc1:ae.getExtension("WEBGL_compressed_texture_etc1"),atc:ae.getExtension("WEBGL_compressed_texture_atc")}}const Vl={extension:{type:D.DetectionParser,priority:2},test:async()=>{const r=N.ADAPTER.createCanvas().getContext("webgl");return r?(ae=r,!0):!1},add:async r=>{is||Hl();const t=[];for(const e in is)is[e]&&t.push(e);return[...t,...r]},remove:async r=>(is||Hl(),r.filter(t=>!(t in is)))};U.add(Vl);class jl extends Ts{constructor(t,e={width:1,height:1,autoLoad:!0}){let s,i;typeof t=="string"?(s=t,i=new Uint8Array):(s=null,i=t),super(i,e),this.origin=s,this.buffer=i?new Ar(i):null,this._load=null,this.loaded=!1,this.origin!==null&&e.autoLoad!==!1&&this.load(),this.origin===null&&this.buffer&&(this._load=Promise.resolve(this),this.loaded=!0,this.onBlobLoaded(this.buffer.rawBinaryData))}onBlobLoaded(t){}load(){return this._load?this._load:(this._load=fetch(this.origin).then(t=>t.blob()).then(t=>t.arrayBuffer()).then(t=>(this.data=new Uint32Array(t),this.buffer=new Ar(t),this.loaded=!0,this.onBlobLoaded(t),this.update(),this)),this._load)}}class Ce extends jl{constructor(t,e){super(t,e),this.format=e.format,this.levels=e.levels||1,this._width=e.width,this._height=e.height,this._extension=Ce._formatToExtension(this.format),(e.levelBuffers||this.buffer)&&(this._levelBuffers=e.levelBuffers||Ce._createLevelBuffers(t instanceof Uint8Array?t:this.buffer.uint8View,this.format,this.levels,4,4,this.width,this.height))}upload(t,e,s){const i=t.gl;if(!t.context.extensions[this._extension])throw new Error(`${this._extension} textures are not supported on the current machine`);if(!this._levelBuffers)return!1;i.pixelStorei(i.UNPACK_ALIGNMENT,4);for(let n=0,a=this.levels;n<a;n++){const{levelID:o,levelWidth:h,levelHeight:l,levelBuffer:u}=this._levelBuffers[n];i.compressedTexImage2D(i.TEXTURE_2D,o,this.format,h,l,0,u)}return!0}onBlobLoaded(){this._levelBuffers=Ce._createLevelBuffers(this.buffer.uint8View,this.format,this.levels,4,4,this.width,this.height)}static _formatToExtension(t){if(t>=33776&&t<=33779)return"s3tc";if(t>=35916&&t<=35919)return"s3tc_sRGB";if(t>=37488&&t<=37497)return"etc";if(t>=35840&&t<=35843)return"pvrtc";if(t===36196)return"etc1";if(t===35986||t===35987||t===34798)return"atc";if(t>=36492&&t<=36495)return"bptc";if(t===37808)return"astc";throw new Error(`Invalid (compressed) texture format given: ${t}`)}static _createLevelBuffers(t,e,s,i,n,a,o){const h=new Array(s);let l=t.byteOffset,u=a,c=o,d=u+i-1&~(i-1),f=c+n-1&~(n-1),p=d*f*zs[e];for(let m=0;m<s;m++)h[m]={levelID:m,levelWidth:s>1?u:d,levelHeight:s>1?c:f,levelBuffer:new Uint8Array(t.buffer,l,p)},l+=p,u=u>>1||1,c=c>>1||1,d=u+i-1&~(i-1),f=c+n-1&~(n-1),p=d*f*zs[e];return h}}const ia=4,ni=124,hm=32,Xl=20,lm=542327876,ai={SIZE:1,FLAGS:2,HEIGHT:3,WIDTH:4,MIPMAP_COUNT:7,PIXEL_FORMAT:19},um={SIZE:0,FLAGS:1,FOURCC:2,RGB_BITCOUNT:3,R_BIT_MASK:4,G_BIT_MASK:5,B_BIT_MASK:6,A_BIT_MASK:7},oi={DXGI_FORMAT:0,RESOURCE_DIMENSION:1,MISC_FLAG:2,ARRAY_SIZE:3,MISC_FLAGS2:4},cm=1,dm=2,fm=4,pm=64,mm=512,gm=131072,_m=827611204,vm=861165636,ym=894720068,xm=808540228,bm=4,Tm={[_m]:Tt.COMPRESSED_RGBA_S3TC_DXT1_EXT,[vm]:Tt.COMPRESSED_RGBA_S3TC_DXT3_EXT,[ym]:Tt.COMPRESSED_RGBA_S3TC_DXT5_EXT},Em={70:Tt.COMPRESSED_RGBA_S3TC_DXT1_EXT,71:Tt.COMPRESSED_RGBA_S3TC_DXT1_EXT,73:Tt.COMPRESSED_RGBA_S3TC_DXT3_EXT,74:Tt.COMPRESSED_RGBA_S3TC_DXT3_EXT,76:Tt.COMPRESSED_RGBA_S3TC_DXT5_EXT,77:Tt.COMPRESSED_RGBA_S3TC_DXT5_EXT,72:Tt.COMPRESSED_SRGB_ALPHA_S3TC_DXT1_EXT,75:Tt.COMPRESSED_SRGB_ALPHA_S3TC_DXT3_EXT,78:Tt.COMPRESSED_SRGB_ALPHA_S3TC_DXT5_EXT,96:Tt.COMPRESSED_RGB_BPTC_SIGNED_FLOAT_EXT,95:Tt.COMPRESSED_RGB_BPTC_UNSIGNED_FLOAT_EXT,98:Tt.COMPRESSED_RGBA_BPTC_UNORM_EXT,99:Tt.COMPRESSED_SRGB_ALPHA_BPTC_UNORM_EXT};function zl(r){const t=new Uint32Array(r);if(t[0]!==lm)throw new Error("Invalid DDS file magic word");const e=new Uint32Array(r,0,ni/Uint32Array.BYTES_PER_ELEMENT),s=e[ai.HEIGHT],i=e[ai.WIDTH],n=e[ai.MIPMAP_COUNT],a=new Uint32Array(r,ai.PIXEL_FORMAT*Uint32Array.BYTES_PER_ELEMENT,hm/Uint32Array.BYTES_PER_ELEMENT),o=a[cm];if(o&fm){const h=a[um.FOURCC];if(h!==xm){const x=Tm[h],y=ia+ni,b=new Uint8Array(r,y);return[new Ce(b,{format:x,width:i,height:s,levels:n})]}const l=ia+ni,u=new Uint32Array(t.buffer,l,Xl/Uint32Array.BYTES_PER_ELEMENT),c=u[oi.DXGI_FORMAT],d=u[oi.RESOURCE_DIMENSION],f=u[oi.MISC_FLAG],p=u[oi.ARRAY_SIZE],m=Em[c];if(m===void 0)throw new Error(`DDSParser cannot parse texture data with DXGI format ${c}`);if(f===bm)throw new Error("DDSParser does not support cubemap textures");if(d===6)throw new Error("DDSParser does not supported 3D texture data");const g=new Array,_=ia+ni+Xl;if(p===1)g.push(new Uint8Array(r,_));else{const x=zs[m];let y=0,b=i,T=s;for(let A=0;A<n;A++){const w=Math.max(1,b+3&-4),R=Math.max(1,T+3&-4),M=w*R*x;y+=M,b=b>>>1,T=T>>>1}let S=_;for(let A=0;A<p;A++)g.push(new Uint8Array(r,S,y)),S+=y}return g.map(x=>new Ce(x,{format:m,width:i,height:s,levels:n}))}throw o&pm?new Error("DDSParser does not support uncompressed texture data."):o&mm?new Error("DDSParser does not supported YUV uncompressed texture data."):o&gm?new Error("DDSParser does not support single-channel (lumninance) texture data!"):o&dm?new Error("DDSParser does not support single-channel (alpha) texture data!"):new Error("DDSParser failed to load a texture file due to an unknown reason!")}const Wl=[171,75,84,88,32,49,49,187,13,10,26,10],Am=67305985,qt={FILE_IDENTIFIER:0,ENDIANNESS:12,GL_TYPE:16,GL_TYPE_SIZE:20,GL_FORMAT:24,GL_INTERNAL_FORMAT:28,GL_BASE_INTERNAL_FORMAT:32,PIXEL_WIDTH:36,PIXEL_HEIGHT:40,PIXEL_DEPTH:44,NUMBER_OF_ARRAY_ELEMENTS:48,NUMBER_OF_FACES:52,NUMBER_OF_MIPMAP_LEVELS:56,BYTES_OF_KEY_VALUE_DATA:60},na=64,aa={[$.UNSIGNED_BYTE]:1,[$.UNSIGNED_SHORT]:2,[$.INT]:4,[$.UNSIGNED_INT]:4,[$.FLOAT]:4,[$.HALF_FLOAT]:8},Yl={[P.RGBA]:4,[P.RGB]:3,[P.RG]:2,[P.RED]:1,[P.LUMINANCE]:1,[P.LUMINANCE_ALPHA]:2,[P.ALPHA]:1},ql={[$.UNSIGNED_SHORT_4_4_4_4]:2,[$.UNSIGNED_SHORT_5_5_5_1]:2,[$.UNSIGNED_SHORT_5_6_5]:2};function Kl(r,t,e=!1){const s=new DataView(t);if(!wm(r,s))return null;const i=s.getUint32(qt.ENDIANNESS,!0)===Am,n=s.getUint32(qt.GL_TYPE,i),a=s.getUint32(qt.GL_FORMAT,i),o=s.getUint32(qt.GL_INTERNAL_FORMAT,i),h=s.getUint32(qt.PIXEL_WIDTH,i),l=s.getUint32(qt.PIXEL_HEIGHT,i)||1,u=s.getUint32(qt.PIXEL_DEPTH,i)||1,c=s.getUint32(qt.NUMBER_OF_ARRAY_ELEMENTS,i)||1,d=s.getUint32(qt.NUMBER_OF_FACES,i),f=s.getUint32(qt.NUMBER_OF_MIPMAP_LEVELS,i),p=s.getUint32(qt.BYTES_OF_KEY_VALUE_DATA,i);if(l===0||u!==1)throw new Error("Only 2D textures are supported");if(d!==1)throw new Error("CubeTextures are not supported by KTXLoader yet!");if(c!==1)throw new Error("WebGL does not support array textures");const m=4,g=4,_=h+3&-4,x=l+3&-4,y=new Array(c);let b=h*l;n===0&&(b=_*x);let T;if(n!==0?aa[n]?T=aa[n]*Yl[a]:T=ql[n]:T=zs[o],T===void 0)throw new Error("Unable to resolve the pixel format stored in the *.ktx file!");const S=e?Cm(s,p,i):null;let A=b*T,w=h,R=l,M=_,H=x,B=na+p;for(let E=0;E<f;E++){const I=s.getUint32(B,i);let V=B+4;for(let q=0;q<c;q++){let j=y[q];j||(j=y[q]=new Array(f)),j[E]={levelID:E,levelWidth:f>1||n!==0?w:M,levelHeight:f>1||n!==0?R:H,levelBuffer:new Uint8Array(t,V,A)},V+=A}B+=I+4,B=B%4!==0?B+4-B%4:B,w=w>>1||1,R=R>>1||1,M=w+m-1&~(m-1),H=R+g-1&~(g-1),A=M*H*T}return n!==0?{uncompressed:y.map(E=>{let I=E[0].levelBuffer,V=!1;return n===$.FLOAT?I=new Float32Array(E[0].levelBuffer.buffer,E[0].levelBuffer.byteOffset,E[0].levelBuffer.byteLength/4):n===$.UNSIGNED_INT?(V=!0,I=new Uint32Array(E[0].levelBuffer.buffer,E[0].levelBuffer.byteOffset,E[0].levelBuffer.byteLength/4)):n===$.INT&&(V=!0,I=new Int32Array(E[0].levelBuffer.buffer,E[0].levelBuffer.byteOffset,E[0].levelBuffer.byteLength/4)),{resource:new Ts(I,{width:E[0].levelWidth,height:E[0].levelHeight}),type:n,format:V?Sm(a):a}}),kvData:S}:{compressed:y.map(E=>new Ce(null,{format:o,width:h,height:l,levels:f,levelBuffers:E})),kvData:S}}function wm(r,t){for(let e=0;e<Wl.length;e++)if(t.getUint8(e)!==Wl[e])return!1;return!0}function Sm(r){switch(r){case P.RGBA:return P.RGBA_INTEGER;case P.RGB:return P.RGB_INTEGER;case P.RG:return P.RG_INTEGER;case P.RED:return P.RED_INTEGER;default:return r}}function Cm(r,t,e){const s=new Map;let i=0;for(;i<t;){const n=r.getUint32(na+i,e),a=na+i+4,o=3-(n+3)%4;if(n===0||n>t-i){console.error("KTXLoader: keyAndValueByteSize out of bounds");break}let h=0;for(;h<n&&r.getUint8(a+h)!==0;h++);if(h===-1){console.error("KTXLoader: Failed to find null byte terminating kvData key");break}const l=new TextDecoder().decode(new Uint8Array(r.buffer,a,h)),u=new DataView(r.buffer,a+h+1,n-h-1);s.set(l,u),i+=4+n+o}return s}var Rm=Object.defineProperty,Zl=Object.getOwnPropertySymbols,Im=Object.prototype.hasOwnProperty,Pm=Object.prototype.propertyIsEnumerable,Ql=(r,t,e)=>t in r?Rm(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,Mm=(r,t)=>{for(var e in t||(t={}))Im.call(t,e)&&Ql(r,e,t[e]);if(Zl)for(var e of Zl(t))Pm.call(t,e)&&Ql(r,e,t[e]);return r};const Jl={extension:{type:D.LoadParser,priority:$t.High},name:"loadDDS",test(r){return ve(r,".dds")},async load(r,t,e){const s=await(await N.ADAPTER.fetch(r)).arrayBuffer(),i=zl(s).map(n=>{const a=new X(n,Mm({mipmap:Ht.OFF,alphaMode:wt.NO_PREMULTIPLIED_ALPHA,resolution:te(r)},t.data));return ss(a,e,r)});return i.length===1?i[0]:i},unload(r){Array.isArray(r)?r.forEach(t=>t.destroy(!0)):r.destroy(!0)}};U.add(Jl);var Dm=Object.defineProperty,tu=Object.getOwnPropertySymbols,Om=Object.prototype.hasOwnProperty,Bm=Object.prototype.propertyIsEnumerable,eu=(r,t,e)=>t in r?Dm(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,Fm=(r,t)=>{for(var e in t||(t={}))Om.call(t,e)&&eu(r,e,t[e]);if(tu)for(var e of tu(t))Bm.call(t,e)&&eu(r,e,t[e]);return r};const su={extension:{type:D.LoadParser,priority:$t.High},name:"loadKTX",test(r){return ve(r,".ktx")},async load(r,t,e){const s=await(await N.ADAPTER.fetch(r)).arrayBuffer(),{compressed:i,uncompressed:n,kvData:a}=Kl(r,s),o=i!=null?i:n,h=Fm({mipmap:Ht.OFF,alphaMode:wt.NO_PREMULTIPLIED_ALPHA,resolution:te(r)},t.data),l=o.map(u=>{var c;o===n&&Object.assign(h,{type:u.type,format:u.format});const d=(c=u.resource)!=null?c:u,f=new X(d,h);return f.ktxKeyValueData=a,ss(f,e,r)});return l.length===1?l[0]:l},unload(r){Array.isArray(r)?r.forEach(t=>t.destroy(!0)):r.destroy(!0)}};U.add(su);const Nm=["s3tc","s3tc_sRGB","etc","etc1","pvrtc","atc","astc","bptc"],ru={extension:D.ResolveParser,test:r=>{const t=gt.extname(r).slice(1);return["basis","ktx","dds"].includes(t)},parse:r=>{var t,e,s,i;const n=r.split("."),a=n.pop();if(["ktx","dds"].includes(a)){const o=n.pop();if(Nm.includes(o))return{resolution:parseFloat((e=(t=N.RETINA_PREFIX.exec(r))==null?void 0:t[1])!=null?e:"1"),format:o,src:r}}return{resolution:parseFloat((i=(s=N.RETINA_PREFIX.exec(r))==null?void 0:s[1])!=null?i:"1"),format:a,src:r}}};U.add(ru);const hi=new z,Lm=4,iu=class lr{constructor(t){this.renderer=t,this._rendererPremultipliedAlpha=!1}contextChange(){var t;const e=(t=this.renderer)==null?void 0:t.gl.getContextAttributes();this._rendererPremultipliedAlpha=!!(e&&e.alpha&&e.premultipliedAlpha)}async image(t,e,s,i){const n=new Image;return n.src=await this.base64(t,e,s,i),n}async base64(t,e,s,i){const n=this.canvas(t,i);if(n.toBlob!==void 0)return new Promise((a,o)=>{n.toBlob(h=>{if(!h){o(new Error("ICanvas.toBlob failed!"));return}const l=new FileReader;l.onload=()=>a(l.result),l.onerror=o,l.readAsDataURL(h)},e,s)});if(n.toDataURL!==void 0)return n.toDataURL(e,s);if(n.convertToBlob!==void 0){const a=await n.convertToBlob({type:e,quality:s});return new Promise((o,h)=>{const l=new FileReader;l.onload=()=>o(l.result),l.onerror=h,l.readAsDataURL(a)})}throw new Error("Extract.base64() requires ICanvas.toDataURL, ICanvas.toBlob, or ICanvas.convertToBlob to be implemented")}canvas(t,e){const{pixels:s,width:i,height:n,flipY:a,premultipliedAlpha:o}=this._rawPixels(t,e);a&&lr._flipY(s,i,n),o&&lr._unpremultiplyAlpha(s);const h=new bs(i,n,1),l=new ImageData(new Uint8ClampedArray(s.buffer),i,n);return h.context.putImageData(l,0,0),h.canvas}pixels(t,e){const{pixels:s,width:i,height:n,flipY:a,premultipliedAlpha:o}=this._rawPixels(t,e);return a&&lr._flipY(s,i,n),o&&lr._unpremultiplyAlpha(s),s}_rawPixels(t,e){const s=this.renderer;if(!s)throw new Error("The Extract has already been destroyed");let i,n=!1,a=!1,o,h=!1;t&&(t instanceof Yt?o=t:(o=s.generateTexture(t,{region:e,resolution:s.resolution,multisample:s.multisample}),h=!0,e&&(hi.width=e.width,hi.height=e.height,e=hi)));const l=s.gl;if(o){if(i=o.baseTexture.resolution,e=e!=null?e:o.frame,n=!1,a=o.baseTexture.alphaMode>0&&o.baseTexture.format===P.RGBA,!h){s.renderTexture.bind(o);const f=o.framebuffer.glFramebuffers[s.CONTEXT_UID];f.blitFramebuffer&&s.framebuffer.bind(f.blitFramebuffer)}}else i=s.resolution,e||(e=hi,e.width=s.width/i,e.height=s.height/i),n=!0,a=this._rendererPremultipliedAlpha,s.renderTexture.bind();const u=Math.max(Math.round(e.width*i),1),c=Math.max(Math.round(e.height*i),1),d=new Uint8Array(Lm*u*c);return l.readPixels(Math.round(e.x*i),Math.round(e.y*i),u,c,l.RGBA,l.UNSIGNED_BYTE,d),h&&(o==null||o.destroy(!0)),{pixels:d,width:u,height:c,flipY:n,premultipliedAlpha:a}}destroy(){this.renderer=null}static _flipY(t,e,s){const i=e<<2,n=s>>1,a=new Uint8Array(i);for(let o=0;o<n;o++){const h=o*i,l=(s-o-1)*i;a.set(t.subarray(h,h+i)),t.copyWithin(h,l,l+i),t.set(a,l)}}static _unpremultiplyAlpha(t){t instanceof Uint8ClampedArray&&(t=new Uint8Array(t.buffer));const e=t.length;for(let s=0;s<e;s+=4){const i=t[s+3];if(i!==0){const n=255.001/i;t[s]=t[s]*n+.5,t[s+1]=t[s+1]*n+.5,t[s+2]=t[s+2]*n+.5}}}};iu.extension={name:"extract",type:D.RendererSystem};let nu=iu;U.add(nu);const Ws={build(r){const t=r.points;let e,s,i,n,a,o;if(r.type===rt.CIRC){const p=r.shape;e=p.x,s=p.y,a=o=p.radius,i=n=0}else if(r.type===rt.ELIP){const p=r.shape;e=p.x,s=p.y,a=p.width,o=p.height,i=n=0}else{const p=r.shape,m=p.width/2,g=p.height/2;e=p.x+m,s=p.y+g,a=o=Math.max(0,Math.min(p.radius,Math.min(m,g))),i=m-a,n=g-o}if(!(a>=0&&o>=0&&i>=0&&n>=0)){t.length=0;return}const h=Math.ceil(2.3*Math.sqrt(a+o)),l=h*8+(i?4:0)+(n?4:0);if(t.length=l,l===0)return;if(h===0){t.length=8,t[0]=t[6]=e+i,t[1]=t[3]=s+n,t[2]=t[4]=e-i,t[5]=t[7]=s-n;return}let u=0,c=h*4+(i?2:0)+2,d=c,f=l;{const p=i+a,m=n,g=e+p,_=e-p,x=s+m;if(t[u++]=g,t[u++]=x,t[--c]=x,t[--c]=_,n){const y=s-m;t[d++]=_,t[d++]=y,t[--f]=y,t[--f]=g}}for(let p=1;p<h;p++){const m=Math.PI/2*(p/h),g=i+Math.cos(m)*a,_=n+Math.sin(m)*o,x=e+g,y=e-g,b=s+_,T=s-_;t[u++]=x,t[u++]=b,t[--c]=b,t[--c]=y,t[d++]=y,t[d++]=T,t[--f]=T,t[--f]=x}{const p=i,m=n+o,g=e+p,_=e-p,x=s+m,y=s-m;t[u++]=g,t[u++]=x,t[--f]=y,t[--f]=g,i&&(t[u++]=_,t[u++]=x,t[--f]=y,t[--f]=_)}},triangulate(r,t){const e=r.points,s=t.points,i=t.indices;if(e.length===0)return;let n=s.length/2;const a=n;let o,h;if(r.type!==rt.RREC){const u=r.shape;o=u.x,h=u.y}else{const u=r.shape;o=u.x+u.width/2,h=u.y+u.height/2}const l=r.matrix;s.push(r.matrix?l.a*o+l.c*h+l.tx:o,r.matrix?l.b*o+l.d*h+l.ty:h),n++,s.push(e[0],e[1]);for(let u=2;u<e.length;u+=2)s.push(e[u],e[u+1]),i.push(n++,a,n);i.push(a+1,a,n)}};function au(r,t=!1){const e=r.length;if(e<6)return;let s=0;for(let i=0,n=r[e-2],a=r[e-1];i<e;i+=2){const o=r[i],h=r[i+1];s+=(o-n)*(h+a),n=o,a=h}if(!t&&s>0||t&&s<=0){const i=e/2;for(let n=i+i%2;n<e;n+=2){const a=e-n-2,o=e-n-1,h=n,l=n+1;[r[a],r[h]]=[r[h],r[a]],[r[o],r[l]]=[r[l],r[o]]}}}const oa={build(r){r.points=r.shape.points.slice()},triangulate(r,t){let e=r.points;const s=r.holes,i=t.points,n=t.indices;if(e.length>=6){au(e,!1);const a=[];for(let l=0;l<s.length;l++){const u=s[l];au(u.points,!0),a.push(e.length/2),e=e.concat(u.points)}const o=Za(e,a,2);if(!o)return;const h=i.length/2;for(let l=0;l<o.length;l+=3)n.push(o[l]+h),n.push(o[l+1]+h),n.push(o[l+2]+h);for(let l=0;l<e.length;l++)i.push(e[l])}}},ou={build(r){const t=r.shape,e=t.x,s=t.y,i=t.width,n=t.height,a=r.points;a.length=0,i>=0&&n>=0&&a.push(e,s,e+i,s,e+i,s+n,e,s+n)},triangulate(r,t){const e=r.points,s=t.points;if(e.length===0)return;const i=s.length/2;s.push(e[0],e[1],e[2],e[3],e[6],e[7],e[4],e[5]),t.indices.push(i,i+1,i+2,i+1,i+2,i+3)}},hu={build(r){Ws.build(r)},triangulate(r,t){Ws.triangulate(r,t)}};var Mt=(r=>(r.MITER="miter",r.BEVEL="bevel",r.ROUND="round",r))(Mt||{}),ye=(r=>(r.BUTT="butt",r.ROUND="round",r.SQUARE="square",r))(ye||{});const Re={adaptive:!0,maxLength:10,minSegments:8,maxSegments:2048,epsilon:1e-4,_segmentsCount(r,t=20){if(!this.adaptive||!r||isNaN(r))return t;let e=Math.ceil(r/this.maxLength);return e<this.minSegments?e=this.minSegments:e>this.maxSegments&&(e=this.maxSegments),e}},Um=Re;class ha{static curveTo(t,e,s,i,n,a){const o=a[a.length-2],h=a[a.length-1]-e,l=o-t,u=i-e,c=s-t,d=Math.abs(h*c-l*u);if(d<1e-8||n===0)return(a[a.length-2]!==t||a[a.length-1]!==e)&&a.push(t,e),null;const f=h*h+l*l,p=u*u+c*c,m=h*u+l*c,g=n*Math.sqrt(f)/d,_=n*Math.sqrt(p)/d,x=g*m/f,y=_*m/p,b=g*c+_*l,T=g*u+_*h,S=l*(_+x),A=h*(_+x),w=c*(g+y),R=u*(g+y),M=Math.atan2(A-T,S-b),H=Math.atan2(R-T,w-b);return{cx:b+t,cy:T+e,radius:n,startAngle:M,endAngle:H,anticlockwise:l*u>c*h}}static arc(t,e,s,i,n,a,o,h,l){const u=o-a,c=Re._segmentsCount(Math.abs(u)*n,Math.ceil(Math.abs(u)/As)*40),d=u/(c*2),f=d*2,p=Math.cos(d),m=Math.sin(d),g=c-1,_=g%1/g;for(let x=0;x<=g;++x){const y=x+_*x,b=d+a+f*y,T=Math.cos(b),S=-Math.sin(b);l.push((p*T+m*S)*n+s,(p*-S+m*T)*n+i)}}}class lu{constructor(){this.reset()}begin(t,e,s){this.reset(),this.style=t,this.start=e,this.attribStart=s}end(t,e){this.attribSize=e-this.attribStart,this.size=t-this.start}reset(){this.style=null,this.size=0,this.start=0,this.attribStart=0,this.attribSize=0}}class li{static curveLength(t,e,s,i,n,a,o,h){let l=0,u=0,c=0,d=0,f=0,p=0,m=0,g=0,_=0,x=0,y=0,b=t,T=e;for(let S=1;S<=10;++S)u=S/10,c=u*u,d=c*u,f=1-u,p=f*f,m=p*f,g=m*t+3*p*u*s+3*f*c*n+d*o,_=m*e+3*p*u*i+3*f*c*a+d*h,x=b-g,y=T-_,b=g,T=_,l+=Math.sqrt(x*x+y*y);return l}static curveTo(t,e,s,i,n,a,o){const h=o[o.length-2],l=o[o.length-1];o.length-=2;const u=Re._segmentsCount(li.curveLength(h,l,t,e,s,i,n,a));let c=0,d=0,f=0,p=0,m=0;o.push(h,l);for(let g=1,_=0;g<=u;++g)_=g/u,c=1-_,d=c*c,f=d*c,p=_*_,m=p*_,o.push(f*h+3*d*_*t+3*c*p*s+m*n,f*l+3*d*_*e+3*c*p*i+m*a)}}function uu(r,t,e,s,i,n,a,o){const h=r-e*i,l=t-s*i,u=r+e*n,c=t+s*n;let d,f;a?(d=s,f=-e):(d=-s,f=e);const p=h+d,m=l+f,g=u+d,_=c+f;return o.push(p,m,g,_),2}function je(r,t,e,s,i,n,a,o){const h=e-r,l=s-t;let u=Math.atan2(h,l),c=Math.atan2(i-r,n-t);o&&u<c?u+=Math.PI*2:!o&&u>c&&(c+=Math.PI*2);let d=u;const f=c-u,p=Math.abs(f),m=Math.sqrt(h*h+l*l),g=(15*p*Math.sqrt(m)/Math.PI>>0)+1,_=f/g;if(d+=_,o){a.push(r,t,e,s);for(let x=1,y=d;x<g;x++,y+=_)a.push(r,t,r+Math.sin(y)*m,t+Math.cos(y)*m);a.push(r,t,i,n)}else{a.push(e,s,r,t);for(let x=1,y=d;x<g;x++,y+=_)a.push(r+Math.sin(y)*m,t+Math.cos(y)*m,r,t);a.push(i,n,r,t)}return g*2}function km(r,t){const e=r.shape;let s=r.points||e.points.slice();const i=t.closePointEps;if(s.length===0)return;const n=r.lineStyle,a=new K(s[0],s[1]),o=new K(s[s.length-2],s[s.length-1]),h=e.type!==rt.POLY||e.closeStroke,l=Math.abs(a.x-o.x)<i&&Math.abs(a.y-o.y)<i;if(h){s=s.slice(),l&&(s.pop(),s.pop(),o.set(s[s.length-2],s[s.length-1]));const j=(a.x+o.x)*.5,W=(o.y+a.y)*.5;s.unshift(j,W),s.push(j,W)}const u=t.points,c=s.length/2;let d=s.length;const f=u.length/2,p=n.width/2,m=p*p,g=n.miterLimit*n.miterLimit;let _=s[0],x=s[1],y=s[2],b=s[3],T=0,S=0,A=-(x-b),w=_-y,R=0,M=0,H=Math.sqrt(A*A+w*w);A/=H,w/=H,A*=p,w*=p;const B=n.alignment,E=(1-B)*2,I=B*2;h||(n.cap===ye.ROUND?d+=je(_-A*(E-I)*.5,x-w*(E-I)*.5,_-A*E,x-w*E,_+A*I,x+w*I,u,!0)+2:n.cap===ye.SQUARE&&(d+=uu(_,x,A,w,E,I,!0,u))),u.push(_-A*E,x-w*E,_+A*I,x+w*I);for(let j=1;j<c-1;++j){_=s[(j-1)*2],x=s[(j-1)*2+1],y=s[j*2],b=s[j*2+1],T=s[(j+1)*2],S=s[(j+1)*2+1],A=-(x-b),w=_-y,H=Math.sqrt(A*A+w*w),A/=H,w/=H,A*=p,w*=p,R=-(b-S),M=y-T,H=Math.sqrt(R*R+M*M),R/=H,M/=H,R*=p,M*=p;const W=y-_,ht=x-b,F=y-T,O=S-b,Z=W*F+ht*O,Q=ht*F-O*W,J=Q<0;if(Math.abs(Q)<.001*Math.abs(Z)){u.push(y-A*E,b-w*E,y+A*I,b+w*I),Z>=0&&(n.join===Mt.ROUND?d+=je(y,b,y-A*E,b-w*E,y-R*E,b-M*E,u,!1)+4:d+=2,u.push(y-R*I,b-M*I,y+R*E,b+M*E));continue}const st=(-A+_)*(-w+b)-(-A+y)*(-w+x),et=(-R+T)*(-M+b)-(-R+y)*(-M+S),it=(W*et-F*st)/Q,lt=(O*st-ht*et)/Q,vt=(it-y)*(it-y)+(lt-b)*(lt-b),nt=y+(it-y)*E,ut=b+(lt-b)*E,mt=y-(it-y)*I,yt=b-(lt-b)*I,oe=Math.min(W*W+ht*ht,F*F+O*O),he=J?E:I,nr=oe+he*he*m,xg=vt<=nr;let xi=n.join;if(xi===Mt.MITER&&vt/m>g&&(xi=Mt.BEVEL),xg)switch(xi){case Mt.MITER:{u.push(nt,ut,mt,yt);break}case Mt.BEVEL:{J?u.push(nt,ut,y+A*I,b+w*I,nt,ut,y+R*I,b+M*I):u.push(y-A*E,b-w*E,mt,yt,y-R*E,b-M*E,mt,yt),d+=2;break}case Mt.ROUND:{J?(u.push(nt,ut,y+A*I,b+w*I),d+=je(y,b,y+A*I,b+w*I,y+R*I,b+M*I,u,!0)+4,u.push(nt,ut,y+R*I,b+M*I)):(u.push(y-A*E,b-w*E,mt,yt),d+=je(y,b,y-A*E,b-w*E,y-R*E,b-M*E,u,!1)+4,u.push(y-R*E,b-M*E,mt,yt));break}}else{switch(u.push(y-A*E,b-w*E,y+A*I,b+w*I),xi){case Mt.MITER:{J?u.push(mt,yt,mt,yt):u.push(nt,ut,nt,ut),d+=2;break}case Mt.ROUND:{J?d+=je(y,b,y+A*I,b+w*I,y+R*I,b+M*I,u,!0)+2:d+=je(y,b,y-A*E,b-w*E,y-R*E,b-M*E,u,!1)+2;break}}u.push(y-R*E,b-M*E,y+R*I,b+M*I),d+=2}}_=s[(c-2)*2],x=s[(c-2)*2+1],y=s[(c-1)*2],b=s[(c-1)*2+1],A=-(x-b),w=_-y,H=Math.sqrt(A*A+w*w),A/=H,w/=H,A*=p,w*=p,u.push(y-A*E,b-w*E,y+A*I,b+w*I),h||(n.cap===ye.ROUND?d+=je(y-A*(E-I)*.5,b-w*(E-I)*.5,y-A*E,b-w*E,y+A*I,b+w*I,u,!1)+2:n.cap===ye.SQUARE&&(d+=uu(y,b,A,w,E,I,!1,u)));const V=t.indices,q=Re.epsilon*Re.epsilon;for(let j=f;j<d+f-2;++j)_=u[j*2],x=u[j*2+1],y=u[(j+1)*2],b=u[(j+1)*2+1],T=u[(j+2)*2],S=u[(j+2)*2+1],!(Math.abs(_*(b-S)+y*(S-x)+T*(x-b))<q)&&V.push(j,j+1,j+2)}function Gm(r,t){let e=0;const s=r.shape,i=r.points||s.points,n=s.type!==rt.POLY||s.closeStroke;if(i.length===0)return;const a=t.points,o=t.indices,h=i.length/2,l=a.length/2;let u=l;for(a.push(i[0],i[1]),e=1;e<h;e++)a.push(i[e*2],i[e*2+1]),o.push(u,u+1),u++;n&&o.push(u,l)}function la(r,t){r.lineStyle.native?Gm(r,t):km(r,t)}class ui{static curveLength(t,e,s,i,n,a){const o=t-2*s+n,h=e-2*i+a,l=2*s-2*t,u=2*i-2*e,c=4*(o*o+h*h),d=4*(o*l+h*u),f=l*l+u*u,p=2*Math.sqrt(c+d+f),m=Math.sqrt(c),g=2*c*m,_=2*Math.sqrt(f),x=d/m;return(g*p+m*d*(p-_)+(4*f*c-d*d)*Math.log((2*m+x+p)/(x+_)))/(4*g)}static curveTo(t,e,s,i,n){const a=n[n.length-2],o=n[n.length-1],h=Re._segmentsCount(ui.curveLength(a,o,t,e,s,i));let l=0,u=0;for(let c=1;c<=h;++c){const d=c/h;l=a+(t-a)*d,u=o+(e-o)*d,n.push(l+(t+(s-t)*d-l)*d,u+(e+(i-e)*d-u)*d)}}}const ci={[rt.POLY]:oa,[rt.CIRC]:Ws,[rt.ELIP]:Ws,[rt.RECT]:ou,[rt.RREC]:hu},ua=[],Ys=[];class qs{constructor(t,e=null,s=null,i=null){this.points=[],this.holes=[],this.shape=t,this.lineStyle=s,this.fillStyle=e,this.matrix=i,this.type=t.type}clone(){return new qs(this.shape,this.fillStyle,this.lineStyle,this.matrix)}destroy(){this.shape=null,this.holes.length=0,this.holes=null,this.points.length=0,this.points=null,this.lineStyle=null,this.fillStyle=null}}const ns=new K,cu=class Hu extends nn{constructor(){super(),this.closePointEps=1e-4,this.boundsPadding=0,this.uvsFloat32=null,this.indicesUint16=null,this.batchable=!1,this.points=[],this.colors=[],this.uvs=[],this.indices=[],this.textureIds=[],this.graphicsData=[],this.drawCalls=[],this.batchDirty=-1,this.batches=[],this.dirty=0,this.cacheDirty=-1,this.clearDirty=0,this.shapeIndex=0,this._bounds=new Ls,this.boundsDirty=-1}get bounds(){return this.updateBatches(),this.boundsDirty!==this.dirty&&(this.boundsDirty=this.dirty,this.calculateBounds()),this._bounds}invalidate(){this.boundsDirty=-1,this.dirty++,this.batchDirty++,this.shapeIndex=0,this.points.length=0,this.colors.length=0,this.uvs.length=0,this.indices.length=0,this.textureIds.length=0;for(let t=0;t<this.drawCalls.length;t++)this.drawCalls[t].texArray.clear(),Ys.push(this.drawCalls[t]);this.drawCalls.length=0;for(let t=0;t<this.batches.length;t++){const e=this.batches[t];e.reset(),ua.push(e)}this.batches.length=0}clear(){return this.graphicsData.length>0&&(this.invalidate(),this.clearDirty++,this.graphicsData.length=0),this}drawShape(t,e=null,s=null,i=null){const n=new qs(t,e,s,i);return this.graphicsData.push(n),this.dirty++,this}drawHole(t,e=null){if(!this.graphicsData.length)return null;const s=new qs(t,null,null,e),i=this.graphicsData[this.graphicsData.length-1];return s.lineStyle=i.lineStyle,i.holes.push(s),this.dirty++,this}destroy(){super.destroy();for(let t=0;t<this.graphicsData.length;++t)this.graphicsData[t].destroy();this.points.length=0,this.points=null,this.colors.length=0,this.colors=null,this.uvs.length=0,this.uvs=null,this.indices.length=0,this.indices=null,this.indexBuffer.destroy(),this.indexBuffer=null,this.graphicsData.length=0,this.graphicsData=null,this.drawCalls.length=0,this.drawCalls=null,this.batches.length=0,this.batches=null,this._bounds=null}containsPoint(t){const e=this.graphicsData;for(let s=0;s<e.length;++s){const i=e[s];if(i.fillStyle.visible&&i.shape&&(i.matrix?i.matrix.applyInverse(t,ns):ns.copyFrom(t),i.shape.contains(ns.x,ns.y))){let n=!1;if(i.holes){for(let a=0;a<i.holes.length;a++)if(i.holes[a].shape.contains(ns.x,ns.y)){n=!0;break}}if(!n)return!0}}return!1}updateBatches(){if(!this.graphicsData.length){this.batchable=!0;return}if(!this.validateBatching())return;this.cacheDirty=this.dirty;const t=this.uvs,e=this.graphicsData;let s=null,i=null;this.batches.length>0&&(s=this.batches[this.batches.length-1],i=s.style);for(let h=this.shapeIndex;h<e.length;h++){this.shapeIndex++;const l=e[h],u=l.fillStyle,c=l.lineStyle;ci[l.type].build(l),l.matrix&&this.transformPoints(l.points,l.matrix),(u.visible||c.visible)&&this.processHoles(l.holes);for(let d=0;d<2;d++){const f=d===0?u:c;if(!f.visible)continue;const p=f.texture.baseTexture,m=this.indices.length,g=this.points.length/2;p.wrapMode=Zt.REPEAT,d===0?this.processFill(l):this.processLine(l);const _=this.points.length/2-g;_!==0&&(s&&!this._compareStyles(i,f)&&(s.end(m,g),s=null),s||(s=ua.pop()||new lu,s.begin(f,m,g),this.batches.push(s),i=f),this.addUvs(this.points,t,f.texture,g,_,f.matrix))}}const n=this.indices.length,a=this.points.length/2;if(s&&s.end(n,a),this.batches.length===0){this.batchable=!0;return}const o=a>65535;this.indicesUint16&&this.indices.length===this.indicesUint16.length&&o===this.indicesUint16.BYTES_PER_ELEMENT>2?this.indicesUint16.set(this.indices):this.indicesUint16=o?new Uint32Array(this.indices):new Uint16Array(this.indices),this.batchable=this.isBatchable(),this.batchable?this.packBatches():this.buildDrawCalls()}_compareStyles(t,e){return!(!t||!e||t.texture.baseTexture!==e.texture.baseTexture||t.color+t.alpha!==e.color+e.alpha||!!t.native!=!!e.native)}validateBatching(){if(this.dirty===this.cacheDirty||!this.graphicsData.length)return!1;for(let t=0,e=this.graphicsData.length;t<e;t++){const s=this.graphicsData[t],i=s.fillStyle,n=s.lineStyle;if(i&&!i.texture.baseTexture.valid||n&&!n.texture.baseTexture.valid)return!1}return!0}packBatches(){this.batchDirty++,this.uvsFloat32=new Float32Array(this.uvs);const t=this.batches;for(let e=0,s=t.length;e<s;e++){const i=t[e];for(let n=0;n<i.size;n++){const a=i.start+n;this.indicesUint16[a]=this.indicesUint16[a]-i.attribStart}}}isBatchable(){if(this.points.length>65535*2)return!1;const t=this.batches;for(let e=0;e<t.length;e++)if(t[e].style.native)return!1;return this.points.length<Hu.BATCHABLE_SIZE*2}buildDrawCalls(){let t=++X._globalBatch;for(let c=0;c<this.drawCalls.length;c++)this.drawCalls[c].texArray.clear(),Ys.push(this.drawCalls[c]);this.drawCalls.length=0;const e=this.colors,s=this.textureIds;let i=Ys.pop();i||(i=new Sr,i.texArray=new Nr),i.texArray.count=0,i.start=0,i.size=0,i.type=Ot.TRIANGLES;let n=0,a=null,o=0,h=!1,l=Ot.TRIANGLES,u=0;this.drawCalls.push(i);for(let c=0;c<this.batches.length;c++){const d=this.batches[c],f=8,p=d.style,m=p.texture.baseTexture;h!==!!p.native&&(h=!!p.native,l=h?Ot.LINES:Ot.TRIANGLES,a=null,n=f,t++),a!==m&&(a=m,m._batchEnabled!==t&&(n===f&&(t++,n=0,i.size>0&&(i=Ys.pop(),i||(i=new Sr,i.texArray=new Nr),this.drawCalls.push(i)),i.start=u,i.size=0,i.texArray.count=0,i.type=l),m.touched=1,m._batchEnabled=t,m._batchLocation=n,m.wrapMode=Zt.REPEAT,i.texArray.elements[i.texArray.count++]=m,n++)),i.size+=d.size,u+=d.size,o=m._batchLocation,this.addColors(e,p.color,p.alpha,d.attribSize,d.attribStart),this.addTextureIds(s,o,d.attribSize,d.attribStart)}X._globalBatch=t,this.packAttributes()}packAttributes(){const t=this.points,e=this.uvs,s=this.colors,i=this.textureIds,n=new ArrayBuffer(t.length*3*4),a=new Float32Array(n),o=new Uint32Array(n);let h=0;for(let l=0;l<t.length/2;l++)a[h++]=t[l*2],a[h++]=t[l*2+1],a[h++]=e[l*2],a[h++]=e[l*2+1],o[h++]=s[l],a[h++]=i[l];this._buffer.update(n),this._indexBuffer.update(this.indicesUint16)}processFill(t){t.holes.length?oa.triangulate(t,this):ci[t.type].triangulate(t,this)}processLine(t){la(t,this);for(let e=0;e<t.holes.length;e++)la(t.holes[e],this)}processHoles(t){for(let e=0;e<t.length;e++){const s=t[e];ci[s.type].build(s),s.matrix&&this.transformPoints(s.points,s.matrix)}}calculateBounds(){const t=this._bounds;t.clear(),t.addVertexData(this.points,0,this.points.length),t.pad(this.boundsPadding,this.boundsPadding)}transformPoints(t,e){for(let s=0;s<t.length/2;s++){const i=t[s*2],n=t[s*2+1];t[s*2]=e.a*i+e.c*n+e.tx,t[s*2+1]=e.b*i+e.d*n+e.ty}}addColors(t,e,s,i,n=0){const a=Y.shared.setValue(e).toLittleEndianNumber(),o=Y.shared.setValue(a).toPremultiplied(s);t.length=Math.max(t.length,n+i);for(let h=0;h<i;h++)t[n+h]=o}addTextureIds(t,e,s,i=0){t.length=Math.max(t.length,i+s);for(let n=0;n<s;n++)t[i+n]=e}addUvs(t,e,s,i,n,a=null){let o=0;const h=e.length,l=s.frame;for(;o<n;){let c=t[(i+o)*2],d=t[(i+o)*2+1];if(a){const f=a.a*c+a.c*d+a.tx;d=a.b*c+a.d*d+a.ty,c=f}o++,e.push(c/l.width,d/l.height)}const u=s.baseTexture;(l.width<u.width||l.height<u.height)&&this.adjustUvs(e,s,h,n)}adjustUvs(t,e,s,i){const n=e.baseTexture,a=1e-6,o=s+i*2,h=e.frame,l=h.width/n.width,u=h.height/n.height;let c=h.x/h.width,d=h.y/h.height,f=Math.floor(t[s]+a),p=Math.floor(t[s+1]+a);for(let m=s+2;m<o;m+=2)f=Math.min(f,Math.floor(t[m]+a)),p=Math.min(p,Math.floor(t[m+1]+a));c-=f,d-=p;for(let m=s;m<o;m+=2)t[m]=(t[m]+c)*l,t[m+1]=(t[m+1]+d)*u}};cu.BATCHABLE_SIZE=100;let du=cu;class Ks{constructor(){this.color=16777215,this.alpha=1,this.texture=L.WHITE,this.matrix=null,this.visible=!1,this.reset()}clone(){const t=new Ks;return t.color=this.color,t.alpha=this.alpha,t.texture=this.texture,t.matrix=this.matrix,t.visible=this.visible,t}reset(){this.color=16777215,this.alpha=1,this.texture=L.WHITE,this.matrix=null,this.visible=!1}destroy(){this.texture=null,this.matrix=null}}class di extends Ks{constructor(){super(...arguments),this.width=0,this.alignment=.5,this.native=!1,this.cap=ye.BUTT,this.join=Mt.MITER,this.miterLimit=10}clone(){const t=new di;return t.color=this.color,t.alpha=this.alpha,t.texture=this.texture,t.matrix=this.matrix,t.visible=this.visible,t.width=this.width,t.alignment=this.alignment,t.native=this.native,t.cap=this.cap,t.join=this.join,t.miterLimit=this.miterLimit,t}reset(){super.reset(),this.color=0,this.alignment=.5,this.width=0,this.native=!1,this.cap=ye.BUTT,this.join=Mt.MITER,this.miterLimit=10}}const ca={},da=class wi extends Ct{constructor(t=null){super(),this.shader=null,this.pluginName="batch",this.currentPath=null,this.batches=[],this.batchTint=-1,this.batchDirty=-1,this.vertexData=null,this._fillStyle=new Ks,this._lineStyle=new di,this._matrix=null,this._holeMode=!1,this.state=ee.for2d(),this._geometry=t||new du,this._geometry.refCount++,this._transformID=-1,this._tintColor=new Y(16777215),this.blendMode=C.NORMAL}get geometry(){return this._geometry}clone(){return this.finishPoly(),new wi(this._geometry)}set blendMode(t){this.state.blendMode=t}get blendMode(){return this.state.blendMode}get tint(){return this._tintColor.value}set tint(t){this._tintColor.setValue(t)}get fill(){return this._fillStyle}get line(){return this._lineStyle}lineStyle(t=null,e=0,s,i=.5,n=!1){return typeof t=="number"&&(t={width:t,color:e,alpha:s,alignment:i,native:n}),this.lineTextureStyle(t)}lineTextureStyle(t){const e={width:0,texture:L.WHITE,color:t!=null&&t.texture?16777215:0,matrix:null,alignment:.5,native:!1,cap:ye.BUTT,join:Mt.MITER,miterLimit:10};t=Object.assign(e,t),this.normalizeColor(t),this.currentPath&&this.startPoly();const s=t.width>0&&t.alpha>0;return s?(t.matrix&&(t.matrix=t.matrix.clone(),t.matrix.invert()),Object.assign(this._lineStyle,{visible:s},t)):this._lineStyle.reset(),this}startPoly(){if(this.currentPath){const t=this.currentPath.points,e=this.currentPath.points.length;e>2&&(this.drawShape(this.currentPath),this.currentPath=new Be,this.currentPath.closeStroke=!1,this.currentPath.points.push(t[e-2],t[e-1]))}else this.currentPath=new Be,this.currentPath.closeStroke=!1}finishPoly(){this.currentPath&&(this.currentPath.points.length>2?(this.drawShape(this.currentPath),this.currentPath=null):this.currentPath.points.length=0)}moveTo(t,e){return this.startPoly(),this.currentPath.points[0]=t,this.currentPath.points[1]=e,this}lineTo(t,e){this.currentPath||this.moveTo(0,0);const s=this.currentPath.points,i=s[s.length-2],n=s[s.length-1];return(i!==t||n!==e)&&s.push(t,e),this}_initCurve(t=0,e=0){this.currentPath?this.currentPath.points.length===0&&(this.currentPath.points=[t,e]):this.moveTo(t,e)}quadraticCurveTo(t,e,s,i){this._initCurve();const n=this.currentPath.points;return n.length===0&&this.moveTo(0,0),ui.curveTo(t,e,s,i,n),this}bezierCurveTo(t,e,s,i,n,a){return this._initCurve(),li.curveTo(t,e,s,i,n,a,this.currentPath.points),this}arcTo(t,e,s,i,n){this._initCurve(t,e);const a=this.currentPath.points,o=ha.curveTo(t,e,s,i,n,a);if(o){const{cx:h,cy:l,radius:u,startAngle:c,endAngle:d,anticlockwise:f}=o;this.arc(h,l,u,c,d,f)}return this}arc(t,e,s,i,n,a=!1){if(i===n)return this;if(!a&&n<=i?n+=As:a&&i<=n&&(i+=As),n-i===0)return this;const o=t+Math.cos(i)*s,h=e+Math.sin(i)*s,l=this._geometry.closePointEps;let u=this.currentPath?this.currentPath.points:null;if(u){const c=Math.abs(u[u.length-2]-o),d=Math.abs(u[u.length-1]-h);c<l&&d<l||u.push(o,h)}else this.moveTo(o,h),u=this.currentPath.points;return ha.arc(o,h,t,e,s,i,n,a,u),this}beginFill(t=0,e){return this.beginTextureFill({texture:L.WHITE,color:t,alpha:e})}normalizeColor(t){var e,s;const i=Y.shared.setValue((e=t.color)!=null?e:0);t.color=i.toNumber(),(s=t.alpha)!=null||(t.alpha=i.alpha)}beginTextureFill(t){const e={texture:L.WHITE,color:16777215,matrix:null};t=Object.assign(e,t),this.normalizeColor(t),this.currentPath&&this.startPoly();const s=t.alpha>0;return s?(t.matrix&&(t.matrix=t.matrix.clone(),t.matrix.invert()),Object.assign(this._fillStyle,{visible:s},t)):this._fillStyle.reset(),this}endFill(){return this.finishPoly(),this._fillStyle.reset(),this}drawRect(t,e,s,i){return this.drawShape(new z(t,e,s,i))}drawRoundedRect(t,e,s,i,n){return this.drawShape(new Pr(t,e,s,i,n))}drawCircle(t,e,s){return this.drawShape(new Rr(t,e,s))}drawEllipse(t,e,s,i){return this.drawShape(new Ir(t,e,s,i))}drawPolygon(...t){let e,s=!0;const i=t[0];i.points?(s=i.closeStroke,e=i.points):Array.isArray(t[0])?e=t[0]:e=t;const n=new Be(e);return n.closeStroke=s,this.drawShape(n),this}drawShape(t){return this._holeMode?this._geometry.drawHole(t,this._matrix):this._geometry.drawShape(t,this._fillStyle.clone(),this._lineStyle.clone(),this._matrix),this}clear(){return this._geometry.clear(),this._lineStyle.reset(),this._fillStyle.reset(),this._boundsID++,this._matrix=null,this._holeMode=!1,this.currentPath=null,this}isFastRect(){const t=this._geometry.graphicsData;return t.length===1&&t[0].shape.type===rt.RECT&&!t[0].matrix&&!t[0].holes.length&&!(t[0].lineStyle.visible&&t[0].lineStyle.width)}_render(t){this.finishPoly();const e=this._geometry;e.updateBatches(),e.batchable?(this.batchDirty!==e.batchDirty&&this._populateBatches(),this._renderBatched(t)):(t.batch.flush(),this._renderDirect(t))}_populateBatches(){const t=this._geometry,e=this.blendMode,s=t.batches.length;this.batchTint=-1,this._transformID=-1,this.batchDirty=t.batchDirty,this.batches.length=s,this.vertexData=new Float32Array(t.points);for(let i=0;i<s;i++){const n=t.batches[i],a=n.style.color,o=new Float32Array(this.vertexData.buffer,n.attribStart*4*2,n.attribSize*2),h=new Float32Array(t.uvsFloat32.buffer,n.attribStart*4*2,n.attribSize*2),l=new Uint16Array(t.indicesUint16.buffer,n.start*2,n.size),u={vertexData:o,blendMode:e,indices:l,uvs:h,_batchRGB:Y.shared.setValue(a).toRgbArray(),_tintRGB:a,_texture:n.style.texture,alpha:n.style.alpha,worldAlpha:1};this.batches[i]=u}}_renderBatched(t){if(this.batches.length){t.batch.setObjectRenderer(t.plugins[this.pluginName]),this.calculateVertices(),this.calculateTints();for(let e=0,s=this.batches.length;e<s;e++){const i=this.batches[e];i.worldAlpha=this.worldAlpha*i.alpha,t.plugins[this.pluginName].render(i)}}}_renderDirect(t){const e=this._resolveDirectShader(t),s=this._geometry,i=this.worldAlpha,n=e.uniforms,a=s.drawCalls;n.translationMatrix=this.transform.worldTransform,Y.shared.setValue(this._tintColor).premultiply(i).toArray(n.tint),t.shader.bind(e),t.geometry.bind(s,e),t.state.set(this.state);for(let o=0,h=a.length;o<h;o++)this._renderDrawCallDirect(t,s.drawCalls[o])}_renderDrawCallDirect(t,e){const{texArray:s,type:i,size:n,start:a}=e,o=s.count;for(let h=0;h<o;h++)t.texture.bind(s.elements[h],h);t.geometry.draw(i,n,a)}_resolveDirectShader(t){let e=this.shader;const s=this.pluginName;if(!e){if(!ca[s]){const{maxTextures:i}=t.plugins[s],n=new Int32Array(i);for(let h=0;h<i;h++)n[h]=h;const a={tint:new Float32Array([1,1,1,1]),translationMatrix:new tt,default:Lt.from({uSamplers:n},!0)},o=t.plugins[s]._shader.program;ca[s]=new Wt(o,a)}e=ca[s]}return e}_calculateBounds(){this.finishPoly();const t=this._geometry;if(!t.graphicsData.length)return;const{minX:e,minY:s,maxX:i,maxY:n}=t.bounds;this._bounds.addFrame(this.transform,e,s,i,n)}containsPoint(t){return this.worldTransform.applyInverse(t,wi._TEMP_POINT),this._geometry.containsPoint(wi._TEMP_POINT)}calculateTints(){if(this.batchTint!==this.tint){this.batchTint=this._tintColor.toNumber();for(let t=0;t<this.batches.length;t++){const e=this.batches[t];e._tintRGB=Y.shared.setValue(this._tintColor).multiply(e._batchRGB).toLittleEndianNumber()}}}calculateVertices(){const t=this.transform._worldID;if(this._transformID===t)return;this._transformID=t;const e=this.transform.worldTransform,s=e.a,i=e.b,n=e.c,a=e.d,o=e.tx,h=e.ty,l=this._geometry.points,u=this.vertexData;let c=0;for(let d=0;d<l.length;d+=2){const f=l[d],p=l[d+1];u[c++]=s*f+n*p+o,u[c++]=a*p+i*f+h}}closePath(){const t=this.currentPath;return t&&(t.closeStroke=!0,this.finishPoly()),this}setMatrix(t){return this._matrix=t,this}beginHole(){return this.finishPoly(),this._holeMode=!0,this}endHole(){return this.finishPoly(),this._holeMode=!1,this}destroy(t){this._geometry.refCount--,this._geometry.refCount===0&&this._geometry.dispose(),this._matrix=null,this.currentPath=null,this._lineStyle.destroy(),this._lineStyle=null,this._fillStyle.destroy(),this._fillStyle=null,this._geometry=null,this.shader=null,this.vertexData=null,this.batches.length=0,this.batches=null,super.destroy(t)}};da.curves=Re,da._TEMP_POINT=new K;let as=da;const $m={buildPoly:oa,buildCircle:Ws,buildRectangle:ou,buildRoundedRectangle:hu,buildLine:la,ArcUtils:ha,BezierUtils:li,QuadraticUtils:ui,BatchPart:lu,FILL_COMMANDS:ci,BATCH_POOL:ua,DRAW_CALL_POOL:Ys};class fu{constructor(t,e){this.uvBuffer=t,this.uvMatrix=e,this.data=null,this._bufferUpdateId=-1,this._textureUpdateId=-1,this._updateID=0}update(t){if(!t&&this._bufferUpdateId===this.uvBuffer._updateID&&this._textureUpdateId===this.uvMatrix._updateID)return;this._bufferUpdateId=this.uvBuffer._updateID,this._textureUpdateId=this.uvMatrix._updateID;const e=this.uvBuffer.data;(!this.data||this.data.length!==e.length)&&(this.data=new Float32Array(e.length)),this.uvMatrix.multiplyUvs(e,this.data),this._updateID++}}const fa=new K,pu=new Be,mu=class Vu extends Ct{constructor(t,e,s,i=Ot.TRIANGLES){super(),this.geometry=t,this.shader=e,this.state=s||ee.for2d(),this.drawMode=i,this.start=0,this.size=0,this.uvs=null,this.indices=null,this.vertexData=new Float32Array(1),this.vertexDirty=-1,this._transformID=-1,this._roundPixels=N.ROUND_PIXELS,this.batchUvs=null}get geometry(){return this._geometry}set geometry(t){this._geometry!==t&&(this._geometry&&(this._geometry.refCount--,this._geometry.refCount===0&&this._geometry.dispose()),this._geometry=t,this._geometry&&this._geometry.refCount++,this.vertexDirty=-1)}get uvBuffer(){return this.geometry.buffers[1]}get verticesBuffer(){return this.geometry.buffers[0]}set material(t){this.shader=t}get material(){return this.shader}set blendMode(t){this.state.blendMode=t}get blendMode(){return this.state.blendMode}set roundPixels(t){this._roundPixels!==t&&(this._transformID=-1),this._roundPixels=t}get roundPixels(){return this._roundPixels}get tint(){return"tint"in this.shader?this.shader.tint:null}set tint(t){this.shader.tint=t}get tintValue(){return this.shader.tintValue}get texture(){return"texture"in this.shader?this.shader.texture:null}set texture(t){this.shader.texture=t}_render(t){const e=this.geometry.buffers[0].data;this.shader.batchable&&this.drawMode===Ot.TRIANGLES&&e.length<Vu.BATCHABLE_SIZE*2?this._renderToBatch(t):this._renderDefault(t)}_renderDefault(t){const e=this.shader;e.alpha=this.worldAlpha,e.update&&e.update(),t.batch.flush(),e.uniforms.translationMatrix=this.transform.worldTransform.toArray(!0),t.shader.bind(e),t.state.set(this.state),t.geometry.bind(this.geometry,e),t.geometry.draw(this.drawMode,this.size,this.start,this.geometry.instanceCount)}_renderToBatch(t){const e=this.geometry,s=this.shader;s.uvMatrix&&(s.uvMatrix.update(),this.calculateUvs()),this.calculateVertices(),this.indices=e.indexBuffer.data,this._tintRGB=s._tintRGB,this._texture=s.texture;const i=this.material.pluginName;t.batch.setObjectRenderer(t.plugins[i]),t.plugins[i].render(this)}calculateVertices(){const t=this.geometry.buffers[0],e=t.data,s=t._updateID;if(s===this.vertexDirty&&this._transformID===this.transform._worldID)return;this._transformID=this.transform._worldID,this.vertexData.length!==e.length&&(this.vertexData=new Float32Array(e.length));const i=this.transform.worldTransform,n=i.a,a=i.b,o=i.c,h=i.d,l=i.tx,u=i.ty,c=this.vertexData;for(let d=0;d<c.length/2;d++){const f=e[d*2],p=e[d*2+1];c[d*2]=n*f+o*p+l,c[d*2+1]=a*f+h*p+u}if(this._roundPixels){const d=N.RESOLUTION;for(let f=0;f<c.length;++f)c[f]=Math.round(c[f]*d)/d}this.vertexDirty=s}calculateUvs(){const t=this.geometry.buffers[1],e=this.shader;e.uvMatrix.isSimple?this.uvs=t.data:(this.batchUvs||(this.batchUvs=new fu(t,e.uvMatrix)),this.batchUvs.update(),this.uvs=this.batchUvs.data)}_calculateBounds(){this.calculateVertices(),this._bounds.addVertexData(this.vertexData,0,this.vertexData.length)}containsPoint(t){if(!this.getBounds().contains(t.x,t.y))return!1;this.worldTransform.applyInverse(t,fa);const e=this.geometry.getBuffer("aVertexPosition").data,s=pu.points,i=this.geometry.getIndex().data,n=i.length,a=this.drawMode===4?3:1;for(let o=0;o+2<n;o+=a){const h=i[o]*2,l=i[o+1]*2,u=i[o+2]*2;if(s[0]=e[h],s[1]=e[h+1],s[2]=e[l],s[3]=e[l+1],s[4]=e[u],s[5]=e[u+1],pu.contains(fa.x,fa.y))return!0}return!1}destroy(t){super.destroy(t),this._cachedTexture&&(this._cachedTexture.destroy(),this._cachedTexture=null),this.geometry=null,this.shader=null,this.state=null,this.uvs=null,this.indices=null,this.vertexData=null}};mu.BATCHABLE_SIZE=100;let At=mu;class Zs extends fe{constructor(t,e,s){super();const i=new dt(t),n=new dt(e,!0),a=new dt(s,!0,!0);this.addAttribute("aVertexPosition",i,2,!1,$.FLOAT).addAttribute("aTextureCoord",n,2,!1,$.FLOAT).addIndex(a),this._updateId=-1}get vertexDirtyId(){return this.buffers[0]._updateID}}var Hm=`varying vec2 vTextureCoord;
uniform vec4 uColor;

uniform sampler2D uSampler;

void main(void)
{
    gl_FragColor = texture2D(uSampler, vTextureCoord) * uColor;
}
`,Vm=`attribute vec2 aVertexPosition;
attribute vec2 aTextureCoord;

uniform mat3 projectionMatrix;
uniform mat3 translationMatrix;
uniform mat3 uTextureMatrix;

varying vec2 vTextureCoord;

void main(void)
{
    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);

    vTextureCoord = (uTextureMatrix * vec3(aTextureCoord, 1.0)).xy;
}
`;class Xe extends Wt{constructor(t,e){const s={uSampler:t,alpha:1,uTextureMatrix:tt.IDENTITY,uColor:new Float32Array([1,1,1,1])};e=Object.assign({tint:16777215,alpha:1,pluginName:"batch"},e),e.uniforms&&Object.assign(s,e.uniforms),super(e.program||se.from(Vm,Hm),s),this._colorDirty=!1,this.uvMatrix=new $r(t),this.batchable=e.program===void 0,this.pluginName=e.pluginName,this._tintColor=new Y(e.tint),this._tintRGB=this._tintColor.toLittleEndianNumber(),this._colorDirty=!0,this.alpha=e.alpha}get texture(){return this.uniforms.uSampler}set texture(t){this.uniforms.uSampler!==t&&(!this.uniforms.uSampler.baseTexture.alphaMode!=!t.baseTexture.alphaMode&&(this._colorDirty=!0),this.uniforms.uSampler=t,this.uvMatrix.texture=t)}set alpha(t){t!==this._alpha&&(this._alpha=t,this._colorDirty=!0)}get alpha(){return this._alpha}set tint(t){t!==this.tint&&(this._tintColor.setValue(t),this._tintRGB=this._tintColor.toLittleEndianNumber(),this._colorDirty=!0)}get tint(){return this._tintColor.value}get tintValue(){return this._tintColor.toNumber()}update(){if(this._colorDirty){this._colorDirty=!1;const t=this.texture.baseTexture.alphaMode;Y.shared.setValue(this._tintColor).premultiply(this._alpha,t).toArray(this.uniforms.uColor)}this.uvMatrix.update()&&(this.uniforms.uTextureMatrix=this.uvMatrix.mapCoord)}}class gu extends Zs{constructor(t=100,e=100,s=10,i=10){super(),this.segWidth=s,this.segHeight=i,this.width=t,this.height=e,this.build()}build(){const t=this.segWidth*this.segHeight,e=[],s=[],i=[],n=this.segWidth-1,a=this.segHeight-1,o=this.width/n,h=this.height/a;for(let u=0;u<t;u++){const c=u%this.segWidth,d=u/this.segWidth|0;e.push(c*o,d*h),s.push(c/n,d/a)}const l=n*a;for(let u=0;u<l;u++){const c=u%n,d=u/n|0,f=d*this.segWidth+c,p=d*this.segWidth+c+1,m=(d+1)*this.segWidth+c,g=(d+1)*this.segWidth+c+1;i.push(f,p,m,p,g,m)}this.buffers[0].data=new Float32Array(e),this.buffers[1].data=new Float32Array(s),this.indexBuffer.data=new Uint16Array(i),this.buffers[0].update(),this.buffers[1].update(),this.indexBuffer.update()}}class _u extends Zs{constructor(t=200,e,s=0){super(new Float32Array(e.length*4),new Float32Array(e.length*4),new Uint16Array((e.length-1)*6)),this.points=e,this._width=t,this.textureScale=s,this.build()}get width(){return this._width}build(){const t=this.points;if(!t)return;const e=this.getBuffer("aVertexPosition"),s=this.getBuffer("aTextureCoord"),i=this.getIndex();if(t.length<1)return;e.data.length/4!==t.length&&(e.data=new Float32Array(t.length*4),s.data=new Float32Array(t.length*4),i.data=new Uint16Array((t.length-1)*6));const n=s.data,a=i.data;n[0]=0,n[1]=0,n[2]=0,n[3]=1;let o=0,h=t[0];const l=this._width*this.textureScale,u=t.length;for(let d=0;d<u;d++){const f=d*4;if(this.textureScale>0){const p=h.x-t[d].x,m=h.y-t[d].y,g=Math.sqrt(p*p+m*m);h=t[d],o+=g/l}else o=d/(u-1);n[f]=o,n[f+1]=0,n[f+2]=o,n[f+3]=1}let c=0;for(let d=0;d<u-1;d++){const f=d*2;a[c++]=f,a[c++]=f+1,a[c++]=f+2,a[c++]=f+2,a[c++]=f+1,a[c++]=f+3}s.update(),i.update(),this.updateVertices()}updateVertices(){const t=this.points;if(t.length<1)return;let e=t[0],s,i=0,n=0;const a=this.buffers[0].data,o=t.length,h=this.textureScale>0?this.textureScale*this._width/2:this._width/2;for(let l=0;l<o;l++){const u=t[l],c=l*4;l<t.length-1?s=t[l+1]:s=u,n=-(s.x-e.x),i=s.y-e.y;let d=(1-l/(o-1))*10;d>1&&(d=1);const f=Math.sqrt(i*i+n*n);f<1e-6?(i=0,n=0):(i/=f,n/=f,i*=h,n*=h),a[c]=u.x+i,a[c+1]=u.y+n,a[c+2]=u.x-i,a[c+3]=u.y-n,e=u}this.buffers[0].update()}update(){this.textureScale>0?this.build():this.updateVertices()}}class vu extends At{constructor(t,e,s){const i=new gu(t.width,t.height,e,s),n=new Xe(L.WHITE);super(i,n),this.texture=t,this.autoResize=!0}textureUpdated(){this._textureID=this.shader.texture._updateID;const t=this.geometry,{width:e,height:s}=this.shader.texture;this.autoResize&&(t.width!==e||t.height!==s)&&(t.width=this.shader.texture.width,t.height=this.shader.texture.height,t.build())}set texture(t){this.shader.texture!==t&&(this.shader.texture=t,this._textureID=-1,t.baseTexture.valid?this.textureUpdated():t.once("update",this.textureUpdated,this))}get texture(){return this.shader.texture}_render(t){this._textureID!==this.shader.texture._updateID&&this.textureUpdated(),super._render(t)}destroy(t){this.shader.texture.off("update",this.textureUpdated,this),super.destroy(t)}}const fi=10;class Qs extends vu{constructor(t,e,s,i,n){var a,o,h,l,u,c,d,f;super(L.WHITE,4,4),this._origWidth=t.orig.width,this._origHeight=t.orig.height,this._width=this._origWidth,this._height=this._origHeight,this._leftWidth=(o=e!=null?e:(a=t.defaultBorders)==null?void 0:a.left)!=null?o:fi,this._rightWidth=(l=i!=null?i:(h=t.defaultBorders)==null?void 0:h.right)!=null?l:fi,this._topHeight=(c=s!=null?s:(u=t.defaultBorders)==null?void 0:u.top)!=null?c:fi,this._bottomHeight=(f=n!=null?n:(d=t.defaultBorders)==null?void 0:d.bottom)!=null?f:fi,this.texture=t}textureUpdated(){this._textureID=this.shader.texture._updateID,this._refresh()}get vertices(){return this.geometry.getBuffer("aVertexPosition").data}set vertices(t){this.geometry.getBuffer("aVertexPosition").data=t}updateHorizontalVertices(){const t=this.vertices,e=this._getMinScale();t[9]=t[11]=t[13]=t[15]=this._topHeight*e,t[17]=t[19]=t[21]=t[23]=this._height-this._bottomHeight*e,t[25]=t[27]=t[29]=t[31]=this._height}updateVerticalVertices(){const t=this.vertices,e=this._getMinScale();t[2]=t[10]=t[18]=t[26]=this._leftWidth*e,t[4]=t[12]=t[20]=t[28]=this._width-this._rightWidth*e,t[6]=t[14]=t[22]=t[30]=this._width}_getMinScale(){const t=this._leftWidth+this._rightWidth,e=this._width>t?1:this._width/t,s=this._topHeight+this._bottomHeight,i=this._height>s?1:this._height/s;return Math.min(e,i)}get width(){return this._width}set width(t){this._width=t,this._refresh()}get height(){return this._height}set height(t){this._height=t,this._refresh()}get leftWidth(){return this._leftWidth}set leftWidth(t){this._leftWidth=t,this._refresh()}get rightWidth(){return this._rightWidth}set rightWidth(t){this._rightWidth=t,this._refresh()}get topHeight(){return this._topHeight}set topHeight(t){this._topHeight=t,this._refresh()}get bottomHeight(){return this._bottomHeight}set bottomHeight(t){this._bottomHeight=t,this._refresh()}_refresh(){const t=this.texture,e=this.geometry.buffers[1].data;this._origWidth=t.orig.width,this._origHeight=t.orig.height;const s=1/this._origWidth,i=1/this._origHeight;e[0]=e[8]=e[16]=e[24]=0,e[1]=e[3]=e[5]=e[7]=0,e[6]=e[14]=e[22]=e[30]=1,e[25]=e[27]=e[29]=e[31]=1,e[2]=e[10]=e[18]=e[26]=s*this._leftWidth,e[4]=e[12]=e[20]=e[28]=1-s*this._rightWidth,e[9]=e[11]=e[13]=e[15]=i*this._topHeight,e[17]=e[19]=e[21]=e[23]=1-i*this._bottomHeight,this.updateHorizontalVertices(),this.updateVerticalVertices(),this.geometry.buffers[0].update(),this.geometry.buffers[1].update()}}class yu extends At{constructor(t=L.EMPTY,e,s,i,n){const a=new Zs(e,s,i);a.getBuffer("aVertexPosition").static=!1;const o=new Xe(t);super(a,o,null,n),this.autoUpdate=!0}get vertices(){return this.geometry.getBuffer("aVertexPosition").data}set vertices(t){this.geometry.getBuffer("aVertexPosition").data=t}_render(t){this.autoUpdate&&this.geometry.getBuffer("aVertexPosition").update(),super._render(t)}}class xu extends At{constructor(t,e,s=0){const i=new _u(t.height,e,s),n=new Xe(t);s>0&&(t.baseTexture.wrapMode=Zt.REPEAT),super(i,n),this.autoUpdate=!0}_render(t){const e=this.geometry;(this.autoUpdate||e._width!==this.shader.texture.height)&&(e._width=this.shader.texture.height,e.update()),super._render(t)}}class bu{constructor(t){this.maxItemsPerFrame=t,this.itemsLeft=0}beginFrame(){this.itemsLeft=this.maxItemsPerFrame}allowedToUpload(){return this.itemsLeft-- >0}}function jm(r,t){var e;let s=!1;if((e=r==null?void 0:r._textures)!=null&&e.length){for(let i=0;i<r._textures.length;i++)if(r._textures[i]instanceof L){const n=r._textures[i].baseTexture;t.includes(n)||(t.push(n),s=!0)}}return s}function Xm(r,t){if(r.baseTexture instanceof X){const e=r.baseTexture;return t.includes(e)||t.push(e),!0}return!1}function zm(r,t){if(r._texture&&r._texture instanceof L){const e=r._texture.baseTexture;return t.includes(e)||t.push(e),!0}return!1}function Wm(r,t){return t instanceof Jr?(t.updateText(!0),!0):!1}function Ym(r,t){if(t instanceof _e){const e=t.toFontString();return ge.measureFont(e),!0}return!1}function qm(r,t){if(r instanceof Jr){t.includes(r.style)||t.push(r.style),t.includes(r)||t.push(r);const e=r._texture.baseTexture;return t.includes(e)||t.push(e),!0}return!1}function Km(r,t){return r instanceof _e?(t.includes(r)||t.push(r),!0):!1}const Tu=class ju{constructor(t){this.limiter=new bu(ju.uploadsPerFrame),this.renderer=t,this.uploadHookHelper=null,this.queue=[],this.addHooks=[],this.uploadHooks=[],this.completes=[],this.ticking=!1,this.delayedTick=()=>{this.queue&&this.prepareItems()},this.registerFindHook(qm),this.registerFindHook(Km),this.registerFindHook(jm),this.registerFindHook(Xm),this.registerFindHook(zm),this.registerUploadHook(Wm),this.registerUploadHook(Ym)}upload(t){return new Promise(e=>{t&&this.add(t),this.queue.length?(this.completes.push(e),this.ticking||(this.ticking=!0,bt.system.addOnce(this.tick,this,me.UTILITY))):e()})}tick(){setTimeout(this.delayedTick,0)}prepareItems(){for(this.limiter.beginFrame();this.queue.length&&this.limiter.allowedToUpload();){const t=this.queue[0];let e=!1;if(t&&!t._destroyed){for(let s=0,i=this.uploadHooks.length;s<i;s++)if(this.uploadHooks[s](this.uploadHookHelper,t)){this.queue.shift(),e=!0;break}}e||this.queue.shift()}if(this.queue.length)bt.system.addOnce(this.tick,this,me.UTILITY);else{this.ticking=!1;const t=this.completes.slice(0);this.completes.length=0;for(let e=0,s=t.length;e<s;e++)t[e]()}}registerFindHook(t){return t&&this.addHooks.push(t),this}registerUploadHook(t){return t&&this.uploadHooks.push(t),this}add(t){for(let e=0,s=this.addHooks.length;e<s&&!this.addHooks[e](t,this.queue);e++);if(t instanceof Ct)for(let e=t.children.length-1;e>=0;e--)this.add(t.children[e]);return this}destroy(){this.ticking&&bt.system.remove(this.tick,this),this.ticking=!1,this.addHooks=null,this.uploadHooks=null,this.renderer=null,this.completes=null,this.queue=null,this.limiter=null,this.uploadHookHelper=null}};Tu.uploadsPerFrame=4;let Js=Tu;Object.defineProperties(N,{UPLOADS_PER_FRAME:{get(){return Js.uploadsPerFrame},set(r){Js.uploadsPerFrame=r}}});function Eu(r,t){return t instanceof X?(t._glTextures[r.CONTEXT_UID]||r.texture.bind(t),!0):!1}function Zm(r,t){if(!(t instanceof as))return!1;const{geometry:e}=t;t.finishPoly(),e.updateBatches();const{batches:s}=e;for(let i=0;i<s.length;i++){const{texture:n}=s[i].style;n&&Eu(r,n.baseTexture)}return e.batchable||r.geometry.bind(e,t._resolveDirectShader(r)),!0}function Qm(r,t){return r instanceof as?(t.push(r),!0):!1}class pa extends Js{constructor(t){super(t),this.uploadHookHelper=this.renderer,this.registerFindHook(Qm),this.registerUploadHook(Eu),this.registerUploadHook(Zm)}}pa.extension={name:"prepare",type:D.RendererSystem},U.add(pa);class Jm{constructor(t){this.maxMilliseconds=t,this.frameStart=0}beginFrame(){this.frameStart=Date.now()}allowedToUpload(){return Date.now()-this.frameStart<this.maxMilliseconds}}class pi extends Ut{constructor(t,e=!0){super(t[0]instanceof L?t[0]:t[0].texture),this._textures=null,this._durations=null,this._autoUpdate=e,this._isConnectedToTicker=!1,this.animationSpeed=1,this.loop=!0,this.updateAnchor=!1,this.onComplete=null,this.onFrameChange=null,this.onLoop=null,this._currentTime=0,this._playing=!1,this._previousFrame=null,this.textures=t}stop(){this._playing&&(this._playing=!1,this._autoUpdate&&this._isConnectedToTicker&&(bt.shared.remove(this.update,this),this._isConnectedToTicker=!1))}play(){this._playing||(this._playing=!0,this._autoUpdate&&!this._isConnectedToTicker&&(bt.shared.add(this.update,this,me.HIGH),this._isConnectedToTicker=!0))}gotoAndStop(t){this.stop(),this.currentFrame=t}gotoAndPlay(t){this.currentFrame=t,this.play()}update(t){if(!this._playing)return;const e=this.animationSpeed*t,s=this.currentFrame;if(this._durations!==null){let i=this._currentTime%1*this._durations[this.currentFrame];for(i+=e/60*1e3;i<0;)this._currentTime--,i+=this._durations[this.currentFrame];const n=Math.sign(this.animationSpeed*t);for(this._currentTime=Math.floor(this._currentTime);i>=this._durations[this.currentFrame];)i-=this._durations[this.currentFrame]*n,this._currentTime+=n;this._currentTime+=i/this._durations[this.currentFrame]}else this._currentTime+=e;this._currentTime<0&&!this.loop?(this.gotoAndStop(0),this.onComplete&&this.onComplete()):this._currentTime>=this._textures.length&&!this.loop?(this.gotoAndStop(this._textures.length-1),this.onComplete&&this.onComplete()):s!==this.currentFrame&&(this.loop&&this.onLoop&&(this.animationSpeed>0&&this.currentFrame<s||this.animationSpeed<0&&this.currentFrame>s)&&this.onLoop(),this.updateTexture())}updateTexture(){const t=this.currentFrame;this._previousFrame!==t&&(this._previousFrame=t,this._texture=this._textures[t],this._textureID=-1,this._textureTrimmedID=-1,this._cachedTint=16777215,this.uvs=this._texture._uvs.uvsFloat32,this.updateAnchor&&this._anchor.copyFrom(this._texture.defaultAnchor),this.onFrameChange&&this.onFrameChange(this.currentFrame))}destroy(t){this.stop(),super.destroy(t),this.onComplete=null,this.onFrameChange=null,this.onLoop=null}static fromFrames(t){const e=[];for(let s=0;s<t.length;++s)e.push(L.from(t[s]));return new pi(e)}static fromImages(t){const e=[];for(let s=0;s<t.length;++s)e.push(L.from(t[s]));return new pi(e)}get totalFrames(){return this._textures.length}get textures(){return this._textures}set textures(t){if(t[0]instanceof L)this._textures=t,this._durations=null;else{this._textures=[],this._durations=[];for(let e=0;e<t.length;e++)this._textures.push(t[e].texture),this._durations.push(t[e].time)}this._previousFrame=null,this.gotoAndStop(0),this.updateTexture()}get currentFrame(){let t=Math.floor(this._currentTime)%this._textures.length;return t<0&&(t+=this._textures.length),t}set currentFrame(t){if(t<0||t>this.totalFrames-1)throw new Error(`[AnimatedSprite]: Invalid frame index value ${t}, expected to be between 0 and totalFrames ${this.totalFrames}.`);const e=this.currentFrame;this._currentTime=t,e!==this.currentFrame&&this.updateTexture()}get playing(){return this._playing}get autoUpdate(){return this._autoUpdate}set autoUpdate(t){t!==this._autoUpdate&&(this._autoUpdate=t,!this._autoUpdate&&this._isConnectedToTicker?(bt.shared.remove(this.update,this),this._isConnectedToTicker=!1):this._autoUpdate&&!this._isConnectedToTicker&&this._playing&&(bt.shared.add(this.update,this),this._isConnectedToTicker=!0))}}const Au=class ur{constructor(t,e,s){this.linkedSheets=[],(t instanceof X||t instanceof L)&&(t={texture:t,data:e,resolutionFilename:s});const{texture:i,data:n,resolutionFilename:a=null,cachePrefix:o=""}=t;this.cachePrefix=o,this._texture=i instanceof L?i:null,this.baseTexture=i instanceof X?i:this._texture.baseTexture,this.textures={},this.animations={},this.data=n;const h=this.baseTexture.resource;this.resolution=this._updateResolution(a||(h?h.url:null)),this._frames=this.data.frames,this._frameKeys=Object.keys(this._frames),this._batchIndex=0,this._callback=null}_updateResolution(t=null){const{scale:e}=this.data.meta;let s=te(t,null);return s===null&&(s=typeof e=="number"?e:parseFloat(e!=null?e:"1")),s!==1&&this.baseTexture.setResolution(s),s}parse(){return new Promise(t=>{this._callback=t,this._batchIndex=0,this._frameKeys.length<=ur.BATCH_SIZE?(this._processFrames(0),this._processAnimations(),this._parseComplete()):this._nextBatch()})}_processFrames(t){let e=t;const s=ur.BATCH_SIZE;for(;e-t<s&&e<this._frameKeys.length;){const i=this._frameKeys[e],n=this._frames[i],a=n.frame;if(a){let o=null,h=null;const l=n.trimmed!==!1&&n.sourceSize?n.sourceSize:n.frame,u=new z(0,0,Math.floor(l.w)/this.resolution,Math.floor(l.h)/this.resolution);n.rotated?o=new z(Math.floor(a.x)/this.resolution,Math.floor(a.y)/this.resolution,Math.floor(a.h)/this.resolution,Math.floor(a.w)/this.resolution):o=new z(Math.floor(a.x)/this.resolution,Math.floor(a.y)/this.resolution,Math.floor(a.w)/this.resolution,Math.floor(a.h)/this.resolution),n.trimmed!==!1&&n.spriteSourceSize&&(h=new z(Math.floor(n.spriteSourceSize.x)/this.resolution,Math.floor(n.spriteSourceSize.y)/this.resolution,Math.floor(a.w)/this.resolution,Math.floor(a.h)/this.resolution)),this.textures[i]=new L(this.baseTexture,o,u,h,n.rotated?2:0,n.anchor,n.borders),L.addToCache(this.textures[i],this.cachePrefix+i.toString())}e++}}_processAnimations(){const t=this.data.animations||{};for(const e in t){this.animations[e]=[];for(let s=0;s<t[e].length;s++){const i=t[e][s];this.animations[e].push(this.textures[i])}}}_parseComplete(){const t=this._callback;this._callback=null,this._batchIndex=0,t.call(this,this.textures)}_nextBatch(){this._processFrames(this._batchIndex*ur.BATCH_SIZE),this._batchIndex++,setTimeout(()=>{this._batchIndex*ur.BATCH_SIZE<this._frameKeys.length?this._nextBatch():(this._processAnimations(),this._parseComplete())},0)}destroy(t=!1){var e;for(const s in this.textures)this.textures[s].destroy();this._frames=null,this._frameKeys=null,this.data=null,this.textures=null,t&&((e=this._texture)==null||e.destroy(),this.baseTexture.destroy()),this._texture=null,this.baseTexture=null,this.linkedSheets=[]}};Au.BATCH_SIZE=1e3;let ma=Au;const tg=["jpg","png","jpeg","avif","webp","s3tc","s3tc_sRGB","etc","etc1","pvrtc","atc","astc","bptc"];function wu(r,t,e){const s={};if(r.forEach(i=>{s[i]=t}),Object.keys(t.textures).forEach(i=>{s[`${t.cachePrefix}${i}`]=t.textures[i]}),!e){const i=gt.dirname(r[0]);t.linkedSheets.forEach((n,a)=>{Object.assign(s,wu([`${i}/${t.data.meta.related_multi_packs[a]}`],n,!0))})}return s}const Su={extension:D.Asset,cache:{test:r=>r instanceof ma,getCacheableAssets:(r,t)=>wu(r,t,!1)},resolver:{test:r=>{const t=r.split("?")[0].split("."),e=t.pop(),s=t.pop();return e==="json"&&tg.includes(s)},parse:r=>{var t,e;const s=r.split(".");return{resolution:parseFloat((e=(t=N.RETINA_PREFIX.exec(r))==null?void 0:t[1])!=null?e:"1"),format:s[s.length-2],src:r}}},loader:{name:"spritesheetLoader",extension:{type:D.LoadParser,priority:$t.Normal},async testParse(r,t){return gt.extname(t.src).toLowerCase()===".json"&&!!r.frames},async parse(r,t,e){var s,i,n;const{texture:a,imageFilename:o,cachePrefix:h}=(s=t==null?void 0:t.data)!=null?s:{};let l=gt.dirname(t.src);l&&l.lastIndexOf("/")!==l.length-1&&(l+="/");let u;if(a&&a.baseTexture)u=a;else{const f=ii(l+(o!=null?o:r.meta.image),t.src);u=(await e.load([f]))[f]}const c=new ma({texture:u.baseTexture,data:r,resolutionFilename:t.src,cachePrefix:h});await c.parse();const d=(i=r==null?void 0:r.meta)==null?void 0:i.related_multi_packs;if(Array.isArray(d)){const f=[];for(const m of d){if(typeof m!="string")continue;let g=l+m;(n=t.data)!=null&&n.ignoreMultiPack||(g=ii(g,t.src),f.push(e.load({src:g,data:{ignoreMultiPack:!0}})))}const p=await Promise.all(f);c.linkedSheets=p,p.forEach(m=>{m.linkedSheets=[c].concat(c.linkedSheets.filter(g=>g!==m))})}return c},unload(r){r.destroy(!0)}}};U.add(Su);class tr{constructor(){this.info=[],this.common=[],this.page=[],this.char=[],this.kerning=[],this.distanceField=[]}}class er{static test(t){return typeof t=="string"&&t.startsWith("info face=")}static parse(t){const e=t.match(/^[a-z]+\s+.+$/gm),s={info:[],common:[],page:[],char:[],chars:[],kerning:[],kernings:[],distanceField:[]};for(const n in e){const a=e[n].match(/^[a-z]+/gm)[0],o=e[n].match(/[a-zA-Z]+=([^\s"']+|"([^"]*)")/gm),h={};for(const l in o){const u=o[l].split("="),c=u[0],d=u[1].replace(/"/gm,""),f=parseFloat(d),p=isNaN(f)?d:f;h[c]=p}s[a].push(h)}const i=new tr;return s.info.forEach(n=>i.info.push({face:n.face,size:parseInt(n.size,10)})),s.common.forEach(n=>i.common.push({lineHeight:parseInt(n.lineHeight,10)})),s.page.forEach(n=>i.page.push({id:parseInt(n.id,10),file:n.file})),s.char.forEach(n=>i.char.push({id:parseInt(n.id,10),page:parseInt(n.page,10),x:parseInt(n.x,10),y:parseInt(n.y,10),width:parseInt(n.width,10),height:parseInt(n.height,10),xoffset:parseInt(n.xoffset,10),yoffset:parseInt(n.yoffset,10),xadvance:parseInt(n.xadvance,10)})),s.kerning.forEach(n=>i.kerning.push({first:parseInt(n.first,10),second:parseInt(n.second,10),amount:parseInt(n.amount,10)})),s.distanceField.forEach(n=>i.distanceField.push({distanceRange:parseInt(n.distanceRange,10),fieldType:n.fieldType})),i}}class mi{static test(t){const e=t;return typeof t!="string"&&"getElementsByTagName"in t&&e.getElementsByTagName("page").length&&e.getElementsByTagName("info")[0].getAttribute("face")!==null}static parse(t){const e=new tr,s=t.getElementsByTagName("info"),i=t.getElementsByTagName("common"),n=t.getElementsByTagName("page"),a=t.getElementsByTagName("char"),o=t.getElementsByTagName("kerning"),h=t.getElementsByTagName("distanceField");for(let l=0;l<s.length;l++)e.info.push({face:s[l].getAttribute("face"),size:parseInt(s[l].getAttribute("size"),10)});for(let l=0;l<i.length;l++)e.common.push({lineHeight:parseInt(i[l].getAttribute("lineHeight"),10)});for(let l=0;l<n.length;l++)e.page.push({id:parseInt(n[l].getAttribute("id"),10)||0,file:n[l].getAttribute("file")});for(let l=0;l<a.length;l++){const u=a[l];e.char.push({id:parseInt(u.getAttribute("id"),10),page:parseInt(u.getAttribute("page"),10)||0,x:parseInt(u.getAttribute("x"),10),y:parseInt(u.getAttribute("y"),10),width:parseInt(u.getAttribute("width"),10),height:parseInt(u.getAttribute("height"),10),xoffset:parseInt(u.getAttribute("xoffset"),10),yoffset:parseInt(u.getAttribute("yoffset"),10),xadvance:parseInt(u.getAttribute("xadvance"),10)})}for(let l=0;l<o.length;l++)e.kerning.push({first:parseInt(o[l].getAttribute("first"),10),second:parseInt(o[l].getAttribute("second"),10),amount:parseInt(o[l].getAttribute("amount"),10)});for(let l=0;l<h.length;l++)e.distanceField.push({fieldType:h[l].getAttribute("fieldType"),distanceRange:parseInt(h[l].getAttribute("distanceRange"),10)});return e}}class gi{static test(t){return typeof t=="string"&&t.includes("<font>")?mi.test(N.ADAPTER.parseXML(t)):!1}static parse(t){return mi.parse(N.ADAPTER.parseXML(t))}}const ga=[er,mi,gi];function Cu(r){for(let t=0;t<ga.length;t++)if(ga[t].test(r))return ga[t];return null}function eg(r,t,e,s,i,n){const a=e.fill;if(Array.isArray(a)){if(a.length===1)return a[0]}else return a;let o;const h=e.dropShadow?e.dropShadowDistance:0,l=e.padding||0,u=r.width/s-h-l*2,c=r.height/s-h-l*2,d=a.slice(),f=e.fillGradientStops.slice();if(!f.length){const p=d.length+1;for(let m=1;m<p;++m)f.push(m/p)}if(d.unshift(a[0]),f.unshift(0),d.push(a[a.length-1]),f.push(1),e.fillGradientType===$s.LINEAR_VERTICAL){o=t.createLinearGradient(u/2,l,u/2,c+l);let p=0;const m=(n.fontProperties.fontSize+e.strokeThickness)/c;for(let g=0;g<i.length;g++){const _=n.lineHeight*g;for(let x=0;x<d.length;x++){let y=0;typeof f[x]=="number"?y=f[x]:y=x/d.length;const b=_/c+y*m;let T=Math.max(p,b);T=Math.min(T,1),o.addColorStop(T,d[x]),p=T}}}else{o=t.createLinearGradient(l,c/2,u+l,c/2);const p=d.length+1;let m=1;for(let g=0;g<d.length;g++){let _;typeof f[g]=="number"?_=f[g]:_=m/p,o.addColorStop(_,d[g]),m++}}return o}function sg(r,t,e,s,i,n,a){const o=e.text,h=e.fontProperties;t.translate(s,i),t.scale(n,n);const l=a.strokeThickness/2,u=-(a.strokeThickness/2);if(t.font=a.toFontString(),t.lineWidth=a.strokeThickness,t.textBaseline=a.textBaseline,t.lineJoin=a.lineJoin,t.miterLimit=a.miterLimit,t.fillStyle=eg(r,t,a,n,[o],e),t.strokeStyle=a.stroke,a.dropShadow){const c=a.dropShadowColor,d=a.dropShadowBlur*n,f=a.dropShadowDistance*n;t.shadowColor=Y.shared.setValue(c).setAlpha(a.dropShadowAlpha).toRgbaString(),t.shadowBlur=d,t.shadowOffsetX=Math.cos(a.dropShadowAngle)*f,t.shadowOffsetY=Math.sin(a.dropShadowAngle)*f}else t.shadowColor="black",t.shadowBlur=0,t.shadowOffsetX=0,t.shadowOffsetY=0;a.stroke&&a.strokeThickness&&t.strokeText(o,l,u+e.lineHeight-h.descent),a.fill&&t.fillText(o,l,u+e.lineHeight-h.descent),t.setTransform(1,0,0,1,0,0),t.fillStyle="rgba(0, 0, 0, 0)"}function _i(r){return r.codePointAt?r.codePointAt(0):r.charCodeAt(0)}function Ru(r){return Array.from?Array.from(r):r.split("")}function rg(r){typeof r=="string"&&(r=[r]);const t=[];for(let e=0,s=r.length;e<s;e++){const i=r[e];if(Array.isArray(i)){if(i.length!==2)throw new Error(`[BitmapFont]: Invalid character range length, expecting 2 got ${i.length}.`);const n=i[0].charCodeAt(0),a=i[1].charCodeAt(0);if(a<n)throw new Error("[BitmapFont]: Invalid character range.");for(let o=n,h=a;o<=h;o++)t.push(String.fromCharCode(o))}else t.push(...Ru(i))}if(t.length===0)throw new Error("[BitmapFont]: Empty set when resolving characters.");return t}var ig=Object.defineProperty,vi=Object.getOwnPropertySymbols,Iu=Object.prototype.hasOwnProperty,Pu=Object.prototype.propertyIsEnumerable,Mu=(r,t,e)=>t in r?ig(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,ng=(r,t)=>{for(var e in t||(t={}))Iu.call(t,e)&&Mu(r,e,t[e]);if(vi)for(var e of vi(t))Pu.call(t,e)&&Mu(r,e,t[e]);return r},ag=(r,t)=>{var e={};for(var s in r)Iu.call(r,s)&&t.indexOf(s)<0&&(e[s]=r[s]);if(r!=null&&vi)for(var s of vi(r))t.indexOf(s)<0&&Pu.call(r,s)&&(e[s]=r[s]);return e};const Ie=class le{constructor(t,e,s){var i,n;const[a]=t.info,[o]=t.common,[h]=t.page,[l]=t.distanceField,u=te(h.file),c={};this._ownsTextures=s,this.font=a.face,this.size=a.size,this.lineHeight=o.lineHeight/u,this.chars={},this.pageTextures=c;for(let d=0;d<t.page.length;d++){const{id:f,file:p}=t.page[d];c[f]=e instanceof Array?e[d]:e[p],l!=null&&l.fieldType&&l.fieldType!=="none"&&(c[f].baseTexture.alphaMode=wt.NO_PREMULTIPLIED_ALPHA,c[f].baseTexture.mipmap=Ht.OFF)}for(let d=0;d<t.char.length;d++){const{id:f,page:p}=t.char[d];let{x:m,y:g,width:_,height:x,xoffset:y,yoffset:b,xadvance:T}=t.char[d];m/=u,g/=u,_/=u,x/=u,y/=u,b/=u,T/=u;const S=new z(m+c[p].frame.x/u,g+c[p].frame.y/u,_,x);this.chars[f]={xOffset:y,yOffset:b,xAdvance:T,kerning:{},texture:new L(c[p].baseTexture,S),page:p}}for(let d=0;d<t.kerning.length;d++){let{first:f,second:p,amount:m}=t.kerning[d];f/=u,p/=u,m/=u,this.chars[p]&&(this.chars[p].kerning[f]=m)}this.distanceFieldRange=l==null?void 0:l.distanceRange,this.distanceFieldType=(n=(i=l==null?void 0:l.fieldType)==null?void 0:i.toLowerCase())!=null?n:"none"}destroy(){for(const t in this.chars)this.chars[t].texture.destroy(),this.chars[t].texture=null;for(const t in this.pageTextures)this._ownsTextures&&this.pageTextures[t].destroy(!0),this.pageTextures[t]=null;this.chars=null,this.pageTextures=null}static install(t,e,s){let i;if(t instanceof tr)i=t;else{const a=Cu(t);if(!a)throw new Error("Unrecognized data format for font.");i=a.parse(t)}e instanceof L&&(e=[e]);const n=new le(i,e,s);return le.available[n.font]=n,n}static uninstall(t){const e=le.available[t];if(!e)throw new Error(`No font found named '${t}'`);e.destroy(),delete le.available[t]}static from(t,e,s){if(!t)throw new Error("[BitmapFont] Property `name` is required.");const i=Object.assign({},le.defaultOptions,s),{chars:n,padding:a,resolution:o,textureWidth:h,textureHeight:l}=i,u=ag(i,["chars","padding","resolution","textureWidth","textureHeight"]),c=rg(n),d=e instanceof _e?e:new _e(e),f=h,p=new tr;p.info[0]={face:d.fontFamily,size:d.fontSize},p.common[0]={lineHeight:d.fontSize};let m=0,g=0,_,x,y,b=0;const T=[],S=[];for(let w=0;w<c.length;w++){_||(_=N.ADAPTER.createCanvas(),_.width=h,_.height=l,x=_.getContext("2d"),y=new X(_,ng({resolution:o},u)),T.push(y),S.push(new L(y)),p.page.push({id:S.length-1,file:""}));const R=c[w],M=ge.measureText(R,d,!1,_),H=M.width,B=Math.ceil(M.height),E=Math.ceil((d.fontStyle==="italic"?2:1)*H);if(g>=l-B*o){if(g===0)throw new Error(`[BitmapFont] textureHeight ${l}px is too small (fontFamily: '${d.fontFamily}', fontSize: ${d.fontSize}px, char: '${R}')`);--w,_=null,x=null,y=null,g=0,m=0,b=0;continue}if(b=Math.max(B+M.fontProperties.descent,b),E*o+m>=f){if(m===0)throw new Error(`[BitmapFont] textureWidth ${h}px is too small (fontFamily: '${d.fontFamily}', fontSize: ${d.fontSize}px, char: '${R}')`);--w,g+=b*o,g=Math.ceil(g),m=0,b=0;continue}sg(_,x,M,m,g,o,d);const I=_i(M.text);p.char.push({id:I,page:S.length-1,x:m/o,y:g/o,width:E,height:B,xoffset:0,yoffset:0,xadvance:H-(d.dropShadow?d.dropShadowDistance:0)-(d.stroke?d.strokeThickness:0)}),m+=(E+2*a)*o,m=Math.ceil(m)}if(!(s!=null&&s.skipKerning))for(let w=0,R=c.length;w<R;w++){const M=c[w];for(let H=0;H<R;H++){const B=c[H],E=x.measureText(M).width,I=x.measureText(B).width,V=x.measureText(M+B).width-(E+I);V&&p.kerning.push({first:_i(M),second:_i(B),amount:V})}}const A=new le(p,S,!0);return le.available[t]!==void 0&&le.uninstall(t),le.available[t]=A,A}};Ie.ALPHA=[["a","z"],["A","Z"]," "],Ie.NUMERIC=[["0","9"]],Ie.ALPHANUMERIC=[["a","z"],["A","Z"],["0","9"]," "],Ie.ASCII=[[" ","~"]],Ie.defaultOptions={resolution:1,textureWidth:512,textureHeight:512,padding:4,chars:Ie.ALPHANUMERIC},Ie.available={};let xe=Ie;var og=`// Pixi texture info\r
varying vec2 vTextureCoord;\r
uniform sampler2D uSampler;\r
\r
// Tint\r
uniform vec4 uColor;\r
\r
// on 2D applications fwidth is screenScale / glyphAtlasScale * distanceFieldRange\r
uniform float uFWidth;\r
\r
void main(void) {\r
\r
  // To stack MSDF and SDF we need a non-pre-multiplied-alpha texture.\r
  vec4 texColor = texture2D(uSampler, vTextureCoord);\r
\r
  // MSDF\r
  float median = texColor.r + texColor.g + texColor.b -\r
                  min(texColor.r, min(texColor.g, texColor.b)) -\r
                  max(texColor.r, max(texColor.g, texColor.b));\r
  // SDF\r
  median = min(median, texColor.a);\r
\r
  float screenPxDistance = uFWidth * (median - 0.5);\r
  float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);\r
  if (median < 0.01) {\r
    alpha = 0.0;\r
  } else if (median > 0.99) {\r
    alpha = 1.0;\r
  }\r
\r
  // Gamma correction for coverage-like alpha\r
  float luma = dot(uColor.rgb, vec3(0.299, 0.587, 0.114));\r
  float gamma = mix(1.0, 1.0 / 2.2, luma);\r
  float coverage = pow(uColor.a * alpha, gamma);  \r
\r
  // NPM Textures, NPM outputs\r
  gl_FragColor = vec4(uColor.rgb, coverage);\r
}\r
`,hg=`// Mesh material default fragment\r
attribute vec2 aVertexPosition;\r
attribute vec2 aTextureCoord;\r
\r
uniform mat3 projectionMatrix;\r
uniform mat3 translationMatrix;\r
uniform mat3 uTextureMatrix;\r
\r
varying vec2 vTextureCoord;\r
\r
void main(void)\r
{\r
    gl_Position = vec4((projectionMatrix * translationMatrix * vec3(aVertexPosition, 1.0)).xy, 0.0, 1.0);\r
\r
    vTextureCoord = (uTextureMatrix * vec3(aTextureCoord, 1.0)).xy;\r
}\r
`;const Du=[],Ou=[],Bu=[],Fu=class Xu extends Ct{constructor(t,e={}){super();const{align:s,tint:i,maxWidth:n,letterSpacing:a,fontName:o,fontSize:h}=Object.assign({},Xu.styleDefaults,e);if(!xe.available[o])throw new Error(`Missing BitmapFont "${o}"`);this._activePagesMeshData=[],this._textWidth=0,this._textHeight=0,this._align=s,this._tintColor=new Y(i),this._font=void 0,this._fontName=o,this._fontSize=h,this.text=t,this._maxWidth=n,this._maxLineHeight=0,this._letterSpacing=a,this._anchor=new pe(()=>{this.dirty=!0},this,0,0),this._roundPixels=N.ROUND_PIXELS,this.dirty=!0,this._resolution=N.RESOLUTION,this._autoResolution=!0,this._textureCache={}}updateText(){var t;const e=xe.available[this._fontName],s=this.fontSize,i=s/e.size,n=new K,a=[],o=[],h=[],l=this._text.replace(/(?:\r\n|\r)/g,`
`)||" ",u=Ru(l),c=this._maxWidth*e.size/s,d=e.distanceFieldType==="none"?Du:Ou;let f=null,p=0,m=0,g=0,_=-1,x=0,y=0,b=0,T=0;for(let B=0;B<u.length;B++){const E=u[B],I=_i(E);if(/(?:\s)/.test(E)&&(_=B,x=p,T++),E==="\r"||E===`
`){o.push(p),h.push(-1),m=Math.max(m,p),++g,++y,n.x=0,n.y+=e.lineHeight,f=null,T=0;continue}const V=e.chars[I];if(!V)continue;f&&V.kerning[f]&&(n.x+=V.kerning[f]);const q=Bu.pop()||{texture:L.EMPTY,line:0,charCode:0,prevSpaces:0,position:new K};q.texture=V.texture,q.line=g,q.charCode=I,q.position.x=Math.round(n.x+V.xOffset+this._letterSpacing/2),q.position.y=Math.round(n.y+V.yOffset),q.prevSpaces=T,a.push(q),p=q.position.x+Math.max(V.xAdvance-V.xOffset,V.texture.orig.width),n.x+=V.xAdvance+this._letterSpacing,b=Math.max(b,V.yOffset+V.texture.height),f=I,_!==-1&&c>0&&n.x>c&&(++y,Oe(a,1+_-y,1+B-_),B=_,_=-1,o.push(x),h.push(a.length>0?a[a.length-1].prevSpaces:0),m=Math.max(m,x),g++,n.x=0,n.y+=e.lineHeight,f=null,T=0)}const S=u[u.length-1];S!=="\r"&&S!==`
`&&(/(?:\s)/.test(S)&&(p=x),o.push(p),m=Math.max(m,p),h.push(-1));const A=[];for(let B=0;B<=g;B++){let E=0;this._align==="right"?E=m-o[B]:this._align==="center"?E=(m-o[B])/2:this._align==="justify"&&(E=h[B]<0?0:(m-o[B])/h[B]),A.push(E)}const w=a.length,R={},M=[],H=this._activePagesMeshData;d.push(...H);for(let B=0;B<w;B++){const E=a[B].texture,I=E.baseTexture.uid;if(!R[I]){let V=d.pop();if(!V){const j=new Zs;let W,ht;e.distanceFieldType==="none"?(W=new Xe(L.EMPTY),ht=C.NORMAL):(W=new Xe(L.EMPTY,{program:se.from(hg,og),uniforms:{uFWidth:0}}),ht=C.NORMAL_NPM);const F=new At(j,W);F.blendMode=ht,V={index:0,indexCount:0,vertexCount:0,uvsCount:0,total:0,mesh:F,vertices:null,uvs:null,indices:null}}V.index=0,V.indexCount=0,V.vertexCount=0,V.uvsCount=0,V.total=0;const{_textureCache:q}=this;q[I]=q[I]||new L(E.baseTexture),V.mesh.texture=q[I],V.mesh.tint=this._tintColor.value,M.push(V),R[I]=V}R[I].total++}for(let B=0;B<H.length;B++)M.includes(H[B])||this.removeChild(H[B].mesh);for(let B=0;B<M.length;B++)M[B].mesh.parent!==this&&this.addChild(M[B].mesh);this._activePagesMeshData=M;for(const B in R){const E=R[B],I=E.total;if(!(((t=E.indices)==null?void 0:t.length)>6*I)||E.vertices.length<At.BATCHABLE_SIZE*2)E.vertices=new Float32Array(4*2*I),E.uvs=new Float32Array(4*2*I),E.indices=new Uint16Array(6*I);else{const V=E.total,q=E.vertices;for(let j=V*4*2;j<q.length;j++)q[j]=0}E.mesh.size=6*I}for(let B=0;B<w;B++){const E=a[B];let I=E.position.x+A[E.line]*(this._align==="justify"?E.prevSpaces:1);this._roundPixels&&(I=Math.round(I));const V=I*i,q=E.position.y*i,j=E.texture,W=R[j.baseTexture.uid],ht=j.frame,F=j._uvs,O=W.index++;W.indices[O*6+0]=0+O*4,W.indices[O*6+1]=1+O*4,W.indices[O*6+2]=2+O*4,W.indices[O*6+3]=0+O*4,W.indices[O*6+4]=2+O*4,W.indices[O*6+5]=3+O*4,W.vertices[O*8+0]=V,W.vertices[O*8+1]=q,W.vertices[O*8+2]=V+ht.width*i,W.vertices[O*8+3]=q,W.vertices[O*8+4]=V+ht.width*i,W.vertices[O*8+5]=q+ht.height*i,W.vertices[O*8+6]=V,W.vertices[O*8+7]=q+ht.height*i,W.uvs[O*8+0]=F.x0,W.uvs[O*8+1]=F.y0,W.uvs[O*8+2]=F.x1,W.uvs[O*8+3]=F.y1,W.uvs[O*8+4]=F.x2,W.uvs[O*8+5]=F.y2,W.uvs[O*8+6]=F.x3,W.uvs[O*8+7]=F.y3}this._textWidth=m*i,this._textHeight=(n.y+e.lineHeight)*i;for(const B in R){const E=R[B];if(this.anchor.x!==0||this.anchor.y!==0){let j=0;const W=this._textWidth*this.anchor.x,ht=this._textHeight*this.anchor.y;for(let F=0;F<E.total;F++)E.vertices[j++]-=W,E.vertices[j++]-=ht,E.vertices[j++]-=W,E.vertices[j++]-=ht,E.vertices[j++]-=W,E.vertices[j++]-=ht,E.vertices[j++]-=W,E.vertices[j++]-=ht}this._maxLineHeight=b*i;const I=E.mesh.geometry.getBuffer("aVertexPosition"),V=E.mesh.geometry.getBuffer("aTextureCoord"),q=E.mesh.geometry.getIndex();I.data=E.vertices,V.data=E.uvs,q.data=E.indices,I.update(),V.update(),q.update()}for(let B=0;B<a.length;B++)Bu.push(a[B]);this._font=e,this.dirty=!1}updateTransform(){this.validate(),this.containerUpdateTransform()}_render(t){this._autoResolution&&this._resolution!==t.resolution&&(this._resolution=t.resolution,this.dirty=!0);const{distanceFieldRange:e,distanceFieldType:s,size:i}=xe.available[this._fontName];if(s!=="none"){const{a:n,b:a,c:o,d:h}=this.worldTransform,l=Math.sqrt(n*n+a*a),u=Math.sqrt(o*o+h*h),c=(Math.abs(l)+Math.abs(u))/2,d=this.fontSize/i,f=t._view.resolution;for(const p of this._activePagesMeshData)p.mesh.shader.uniforms.uFWidth=c*e*d*f}super._render(t)}getLocalBounds(){return this.validate(),super.getLocalBounds()}validate(){const t=xe.available[this._fontName];if(!t)throw new Error(`Missing BitmapFont "${this._fontName}"`);this._font!==t&&(this.dirty=!0),this.dirty&&this.updateText()}get tint(){return this._tintColor.value}set tint(t){if(this.tint!==t){this._tintColor.setValue(t);for(let e=0;e<this._activePagesMeshData.length;e++)this._activePagesMeshData[e].mesh.tint=t}}get align(){return this._align}set align(t){this._align!==t&&(this._align=t,this.dirty=!0)}get fontName(){return this._fontName}set fontName(t){if(!xe.available[t])throw new Error(`Missing BitmapFont "${t}"`);this._fontName!==t&&(this._fontName=t,this.dirty=!0)}get fontSize(){var t;return(t=this._fontSize)!=null?t:xe.available[this._fontName].size}set fontSize(t){this._fontSize!==t&&(this._fontSize=t,this.dirty=!0)}get anchor(){return this._anchor}set anchor(t){typeof t=="number"?this._anchor.set(t):this._anchor.copyFrom(t)}get text(){return this._text}set text(t){t=String(t==null?"":t),this._text!==t&&(this._text=t,this.dirty=!0)}get maxWidth(){return this._maxWidth}set maxWidth(t){this._maxWidth!==t&&(this._maxWidth=t,this.dirty=!0)}get maxLineHeight(){return this.validate(),this._maxLineHeight}get textWidth(){return this.validate(),this._textWidth}get letterSpacing(){return this._letterSpacing}set letterSpacing(t){this._letterSpacing!==t&&(this._letterSpacing=t,this.dirty=!0)}get roundPixels(){return this._roundPixels}set roundPixels(t){t!==this._roundPixels&&(this._roundPixels=t,this.dirty=!0)}get textHeight(){return this.validate(),this._textHeight}get resolution(){return this._resolution}set resolution(t){this._autoResolution=!1,this._resolution!==t&&(this._resolution=t,this.dirty=!0)}destroy(t){const{_textureCache:e}=this,s=xe.available[this._fontName].distanceFieldType==="none"?Du:Ou;s.push(...this._activePagesMeshData);for(const i of this._activePagesMeshData)this.removeChild(i.mesh);this._activePagesMeshData=[],s.filter(i=>e[i.mesh.texture.baseTexture.uid]).forEach(i=>{i.mesh.texture=L.EMPTY});for(const i in e)e[i].destroy(),delete e[i];this._font=null,this._tintColor=null,this._textureCache=null,super.destroy(t)}};Fu.styleDefaults={align:"left",tint:16777215,maxWidth:0,letterSpacing:0};let lg=Fu;const ug=[".xml",".fnt"],Nu={extension:{type:D.LoadParser,priority:$t.Normal},name:"loadBitmapFont",test(r){return ug.includes(gt.extname(r).toLowerCase())},async testParse(r){return er.test(r)||gi.test(r)},async parse(r,t,e){const s=er.test(r)?er.parse(r):gi.parse(r),{src:i}=t,{page:n}=s,a=[];for(let l=0;l<n.length;++l){const u=n[l].file;let c=gt.join(gt.dirname(i),u);c=ii(c,i),a.push(c)}const o=await e.load(a),h=a.map(l=>o[l]);return xe.install(s,h,!0)},async load(r,t){return(await N.ADAPTER.fetch(r)).text()},unload(r){r.destroy()}};U.add(Nu);var cg=Object.defineProperty,dg=Object.defineProperties,fg=Object.getOwnPropertyDescriptors,Lu=Object.getOwnPropertySymbols,pg=Object.prototype.hasOwnProperty,mg=Object.prototype.propertyIsEnumerable,Uu=(r,t,e)=>t in r?cg(r,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):r[t]=e,gg=(r,t)=>{for(var e in t||(t={}))pg.call(t,e)&&Uu(r,e,t[e]);if(Lu)for(var e of Lu(t))mg.call(t,e)&&Uu(r,e,t[e]);return r},_g=(r,t)=>dg(r,fg(t));const _a=class ls extends _e{constructor(){super(...arguments),this._fonts=[],this._overrides=[],this._stylesheet="",this.fontsDirty=!1}static from(t){return new ls(Object.keys(ls.defaultOptions).reduce((e,s)=>_g(gg({},e),{[s]:t[s]}),{}))}cleanFonts(){this._fonts.length>0&&(this._fonts.forEach(t=>{URL.revokeObjectURL(t.src),t.refs--,t.refs===0&&(t.fontFace&&document.fonts.delete(t.fontFace),delete ls.availableFonts[t.originalUrl])}),this.fontFamily="Arial",this._fonts.length=0,this.styleID++,this.fontsDirty=!0)}loadFont(t,e={}){const{availableFonts:s}=ls;if(s[t]){const i=s[t];return this._fonts.push(i),i.refs++,this.styleID++,this.fontsDirty=!0,Promise.resolve()}return N.ADAPTER.fetch(t).then(i=>i.blob()).then(async i=>new Promise((n,a)=>{const o=URL.createObjectURL(i),h=new FileReader;h.onload=()=>n([o,h.result]),h.onerror=a,h.readAsDataURL(i)})).then(async([i,n])=>{const a=Object.assign({family:gt.basename(t,gt.extname(t)),weight:"normal",style:"normal",display:"auto",src:i,dataSrc:n,refs:1,originalUrl:t,fontFace:null},e);s[t]=a,this._fonts.push(a),this.styleID++;const o=new FontFace(a.family,`url(${a.src})`,{weight:a.weight,style:a.style,display:a.display});a.fontFace=o,await o.load(),document.fonts.add(o),await document.fonts.ready,this.styleID++,this.fontsDirty=!0})}addOverride(...t){const e=t.filter(s=>!this._overrides.includes(s));e.length>0&&(this._overrides.push(...e),this.styleID++)}removeOverride(...t){const e=t.filter(s=>this._overrides.includes(s));e.length>0&&(this._overrides=this._overrides.filter(s=>!e.includes(s)),this.styleID++)}toCSS(t){return[`transform: scale(${t})`,"transform-origin: top left","display: inline-block",`color: ${this.normalizeColor(this.fill)}`,`font-size: ${this.fontSize}px`,`font-family: ${this.fontFamily}`,`font-weight: ${this.fontWeight}`,`font-style: ${this.fontStyle}`,`font-variant: ${this.fontVariant}`,`letter-spacing: ${this.letterSpacing}px`,`text-align: ${this.align}`,`padding: ${this.padding}px`,`white-space: ${this.whiteSpace}`,...this.lineHeight?[`line-height: ${this.lineHeight}px`]:[],...this.wordWrap?[`word-wrap: ${this.breakWords?"break-all":"break-word"}`,`max-width: ${this.wordWrapWidth}px`]:[],...this.strokeThickness?[`-webkit-text-stroke-width: ${this.strokeThickness}px`,`-webkit-text-stroke-color: ${this.normalizeColor(this.stroke)}`,`text-stroke-width: ${this.strokeThickness}px`,`text-stroke-color: ${this.normalizeColor(this.stroke)}`,"paint-order: stroke"]:[],...this.dropShadow?[this.dropShadowToCSS()]:[],...this._overrides].join(";")}toGlobalCSS(){return this._fonts.reduce((t,e)=>`${t}
            @font-face {
                font-family: "${e.family}";
                src: url('${e.dataSrc}');
                font-weight: ${e.weight};
                font-style: ${e.style};
                font-display: ${e.display};
            }`,this._stylesheet)}get stylesheet(){return this._stylesheet}set stylesheet(t){this._stylesheet!==t&&(this._stylesheet=t,this.styleID++)}normalizeColor(t){return Array.isArray(t)&&(t=Ao(t)),typeof t=="number"?Eo(t):t}dropShadowToCSS(){let t=this.normalizeColor(this.dropShadowColor);const e=this.dropShadowAlpha,s=Math.round(Math.cos(this.dropShadowAngle)*this.dropShadowDistance),i=Math.round(Math.sin(this.dropShadowAngle)*this.dropShadowDistance);t.startsWith("#")&&e<1&&(t+=(e*255|0).toString(16).padStart(2,"0"));const n=`${s}px ${i}px`;return this.dropShadowBlur>0?`text-shadow: ${n} ${this.dropShadowBlur}px ${t}`:`text-shadow: ${n} ${t}`}reset(){Object.assign(this,ls.defaultOptions)}onBeforeDraw(){const{fontsDirty:t}=this;return this.fontsDirty=!1,this.isSafari&&this._fonts.length>0&&t?new Promise(e=>setTimeout(e,100)):Promise.resolve()}get isSafari(){const{userAgent:t}=N.ADAPTER.getNavigator();return/^((?!chrome|android).)*safari/i.test(t)}set fillGradientStops(t){console.warn("[HTMLTextStyle] fillGradientStops is not supported by HTMLText")}get fillGradientStops(){return super.fillGradientStops}set fillGradientType(t){console.warn("[HTMLTextStyle] fillGradientType is not supported by HTMLText")}get fillGradientType(){return super.fillGradientType}set miterLimit(t){console.warn("[HTMLTextStyle] miterLimit is not supported by HTMLText")}get miterLimit(){return super.miterLimit}set trim(t){console.warn("[HTMLTextStyle] trim is not supported by HTMLText")}get trim(){return super.trim}set textBaseline(t){console.warn("[HTMLTextStyle] textBaseline is not supported by HTMLText")}get textBaseline(){return super.textBaseline}set leading(t){console.warn("[HTMLTextStyle] leading is not supported by HTMLText")}get leading(){return super.leading}set lineJoin(t){console.warn("[HTMLTextStyle] lineJoin is not supported by HTMLText")}get lineJoin(){return super.lineJoin}};_a.availableFonts={},_a.defaultOptions={align:"left",breakWords:!1,dropShadow:!1,dropShadowAlpha:1,dropShadowAngle:Math.PI/6,dropShadowBlur:0,dropShadowColor:"black",dropShadowDistance:5,fill:"black",fontFamily:"Arial",fontSize:26,fontStyle:"normal",fontVariant:"normal",fontWeight:"normal",letterSpacing:0,lineHeight:0,padding:0,stroke:"black",strokeThickness:0,whiteSpace:"normal",wordWrap:!1,wordWrapWidth:100};let yi=_a;const sr=class us extends Ut{constructor(t="",e={}){var s;super(L.EMPTY),this._text=null,this._style=null,this._autoResolution=!0,this.localStyleID=-1,this.dirty=!1,this._updateID=0,this.ownsStyle=!1;const i=new Image,n=L.from(i,{scaleMode:N.SCALE_MODE,resourceOptions:{autoLoad:!1}});n.orig=new z,n.trim=new z,this.texture=n;const a="http://www.w3.org/2000/svg",o="http://www.w3.org/1999/xhtml",h=document.createElementNS(a,"svg"),l=document.createElementNS(a,"foreignObject"),u=document.createElementNS(o,"div"),c=document.createElementNS(o,"style");l.setAttribute("width","10000"),l.setAttribute("height","10000"),l.style.overflow="hidden",h.appendChild(l),this.maxWidth=us.defaultMaxWidth,this.maxHeight=us.defaultMaxHeight,this._domElement=u,this._styleElement=c,this._svgRoot=h,this._foreignObject=l,this._foreignObject.appendChild(c),this._foreignObject.appendChild(u),this._image=i,this._loadImage=new Image,this._autoResolution=us.defaultAutoResolution,this._resolution=(s=us.defaultResolution)!=null?s:N.RESOLUTION,this.text=t,this.style=e}measureText(t){var e,s;const{text:i,style:n,resolution:a}=Object.assign({text:this._text,style:this._style,resolution:this._resolution},t);Object.assign(this._domElement,{innerHTML:i,style:n.toCSS(a)}),this._styleElement.textContent=n.toGlobalCSS(),document.body.appendChild(this._svgRoot);const o=this._domElement.getBoundingClientRect();this._svgRoot.remove();const{width:h,height:l}=o,u=Math.min(this.maxWidth,Math.ceil(h)),c=Math.min(this.maxHeight,Math.ceil(l));return this._svgRoot.setAttribute("width",u.toString()),this._svgRoot.setAttribute("height",c.toString()),i!==this._text&&(this._domElement.innerHTML=this._text),n!==this._style&&(Object.assign(this._domElement,{style:(e=this._style)==null?void 0:e.toCSS(a)}),this._styleElement.textContent=(s=this._style)==null?void 0:s.toGlobalCSS()),{width:u+n.padding*2,height:c+n.padding*2}}async updateText(t=!0){const{style:e,_image:s,_loadImage:i}=this;if(this.localStyleID!==e.styleID&&(this.dirty=!0,this.localStyleID=e.styleID),!this.dirty&&t)return;const{width:n,height:a}=this.measureText();s.width=i.width=Math.ceil(Math.max(1,n)),s.height=i.height=Math.ceil(Math.max(1,a)),this._updateID++;const o=this._updateID;await new Promise(h=>{i.onload=async()=>{if(o<this._updateID){h();return}await e.onBeforeDraw(),s.src=i.src,i.onload=null,i.src="",this.updateTexture(),h()};const l=new XMLSerializer().serializeToString(this._svgRoot);i.src=`data:image/svg+xml;charset=utf8,${encodeURIComponent(l)}`})}get source(){return this._image}updateTexture(){const{style:t,texture:e,_image:s,resolution:i}=this,{padding:n}=t,{baseTexture:a}=e;e.trim.width=e._frame.width=s.width/i,e.trim.height=e._frame.height=s.height/i,e.trim.x=-n,e.trim.y=-n,e.orig.width=e._frame.width-n*2,e.orig.height=e._frame.height-n*2,this._onTextureUpdate(),a.setRealSize(s.width,s.height,i),this.dirty=!1}_render(t){this._autoResolution&&this._resolution!==t.resolution&&(this._resolution=t.resolution,this.dirty=!0),this.updateText(!0),super._render(t)}_renderCanvas(t){this._autoResolution&&this._resolution!==t.resolution&&(this._resolution=t.resolution,this.dirty=!0),this.updateText(!0),super._renderCanvas(t)}getLocalBounds(t){return this.updateText(!0),super.getLocalBounds(t)}_calculateBounds(){this.updateText(!0),this.calculateVertices(),this._bounds.addQuad(this.vertexData)}_onStyleChange(){this.dirty=!0}destroy(t){var e,s,i,n,a;typeof t=="boolean"&&(t={children:t}),t=Object.assign({},us.defaultDestroyOptions,t),super.destroy(t);const o=null;this.ownsStyle&&((e=this._style)==null||e.cleanFonts()),this._style=o,(s=this._svgRoot)==null||s.remove(),this._svgRoot=o,(i=this._domElement)==null||i.remove(),this._domElement=o,(n=this._foreignObject)==null||n.remove(),this._foreignObject=o,(a=this._styleElement)==null||a.remove(),this._styleElement=o,this._loadImage.src="",this._loadImage.onload=null,this._loadImage=o,this._image.src="",this._image=o}get width(){return this.updateText(!0),Math.abs(this.scale.x)*this._image.width/this.resolution}set width(t){this.updateText(!0);const e=de(this.scale.x)||1;this.scale.x=e*t/this._image.width/this.resolution,this._width=t}get height(){return this.updateText(!0),Math.abs(this.scale.y)*this._image.height/this.resolution}set height(t){this.updateText(!0);const e=de(this.scale.y)||1;this.scale.y=e*t/this._image.height/this.resolution,this._height=t}get style(){return this._style}set style(t){this._style!==t&&(t=t||{},t instanceof yi?(this.ownsStyle=!1,this._style=t):t instanceof _e?(console.warn("[HTMLText] Cloning TextStyle, if this is not what you want, use HTMLTextStyle"),this.ownsStyle=!0,this._style=yi.from(t)):(this.ownsStyle=!0,this._style=new yi(t)),this.localStyleID=-1,this.dirty=!0)}get text(){return this._text}set text(t){t=String(t===""||t===null||t===void 0?" ":t),t=this.sanitiseText(t),this._text!==t&&(this._text=t,this.dirty=!0)}get resolution(){return this._resolution}set resolution(t){this._autoResolution=!1,this._resolution!==t&&(this._resolution=t,this.dirty=!0)}sanitiseText(t){return t.replace(/<br>/gi,"<br/>").replace(/<hr>/gi,"<hr/>").replace(/&nbsp;/gi,"&#160;")}};sr.defaultDestroyOptions={texture:!0,children:!1,baseTexture:!0},sr.defaultMaxWidth=2024,sr.defaultMaxHeight=2024,sr.defaultAutoResolution=!0;let vg=sr;const Pe=new z;class va{constructor(t){this.renderer=t}async image(t,e,s,i){const n=new Image;return n.src=await this.base64(t,e,s,i),n}async base64(t,e,s,i){const n=this.canvas(t,i);if(n.toBlob!==void 0)return new Promise((a,o)=>{n.toBlob(h=>{if(!h){o(new Error("ICanvas.toBlob failed!"));return}const l=new FileReader;l.onload=()=>a(l.result),l.onerror=o,l.readAsDataURL(h)},e,s)});if(n.toDataURL!==void 0)return n.toDataURL(e,s);if(n.convertToBlob!==void 0){const a=await n.convertToBlob({type:e,quality:s});return new Promise((o,h)=>{const l=new FileReader;l.onload=()=>o(l.result),l.onerror=h,l.readAsDataURL(a)})}throw new Error("CanvasExtract.base64() requires ICanvas.toDataURL, ICanvas.toBlob, or ICanvas.convertToBlob to be implemented")}canvas(t,e){const s=this.renderer;if(!s)throw new Error("The CanvasExtract has already been destroyed");let i,n,a;t&&(t instanceof Yt?a=t:(a=s.generateTexture(t,{region:e,resolution:s.resolution}),e&&(Pe.width=e.width,Pe.height=e.height,e=Pe))),a?(i=a.baseTexture._canvasRenderTarget.context,n=a.baseTexture._canvasRenderTarget.resolution,e=e!=null?e:a.frame):(i=s.canvasContext.rootContext,n=s._view.resolution,e||(e=Pe,e.width=s.width/n,e.height=s.height/n));const o=Math.round(e.x*n),h=Math.round(e.y*n),l=Math.max(Math.round(e.width*n),1),u=Math.max(Math.round(e.height*n),1),c=new bs(l,u,1),d=i.getImageData(o,h,l,u);return c.context.putImageData(d,0,0),c.canvas}pixels(t,e){const s=this.renderer;if(!s)throw new Error("The CanvasExtract has already been destroyed");let i,n,a;t&&(t instanceof Yt?a=t:(a=s.generateTexture(t,{region:e,resolution:s.resolution}),e&&(Pe.width=e.width,Pe.height=e.height,e=Pe))),a?(i=a.baseTexture._canvasRenderTarget.context,n=a.baseTexture._canvasRenderTarget.resolution,e=e!=null?e:a.frame):(i=s.canvasContext.rootContext,n=s.resolution,e||(e=Pe,e.width=s.width/n,e.height=s.height/n));const o=Math.round(e.x*n),h=Math.round(e.y*n),l=Math.max(Math.round(e.width*n),1),u=Math.max(Math.round(e.height*n),1);return i.getImageData(o,h,l,u).data}destroy(){this.renderer=null}}va.extension={name:"extract",type:D.CanvasRendererSystem},U.add(va);let ya;const rr=new tt;as.prototype.generateCanvasTexture=function(r,t=1){const e=this.getLocalBounds(new z);e.width=Math.max(e.width,1/t),e.height=Math.max(e.height,1/t);const s=Yt.create({width:e.width,height:e.height,scaleMode:r,resolution:t});ya||(ya=new Ns),this.transform.updateLocalTransform(),this.transform.localTransform.copyTo(rr),rr.invert(),rr.tx-=e.x,rr.ty-=e.y,ya.render(this,{renderTexture:s,clear:!0,transform:rr});const i=L.from(s.baseTexture._canvasRenderTarget.canvas,{scaleMode:r});return i.baseTexture.setResolution(t),i},as.prototype.cachedGraphicsData=[],as.prototype._renderCanvas=function(r){this.isMask!==!0&&(this.finishPoly(),r.plugins.graphics.render(this))};class ir{static offsetPolygon(t,e){const s=[],i=t.length;e=ir.isPolygonClockwise(t)?e:-1*e;for(let n=0;n<i;n+=2){let a=n-2;a<0&&(a+=i);const o=(n+2)%i;let h=t[n]-t[a],l=t[n+1]-t[a+1],u=Math.sqrt(h*h+l*l);h/=u,l/=u,h*=e,l*=e;const c=-l,d=h,f=[t[a]+c,t[a+1]+d],p=[t[n]+c,t[n+1]+d];let m=t[o]-t[n],g=t[o+1]-t[n+1];u=Math.sqrt(m*m+g*g),m/=u,g/=u,m*=e,g*=e;const _=-g,x=m,y=[t[n]+_,t[n+1]+x],b=[t[o]+_,t[o+1]+x],T=ir.findIntersection(f[0],f[1],p[0],p[1],y[0],y[1],b[0],b[1]);T&&s.push(...T)}return s}static findIntersection(t,e,s,i,n,a,o,h){const l=(h-a)*(s-t)-(o-n)*(i-e),u=(o-n)*(e-a)-(h-a)*(t-n),c=(s-t)*(e-a)-(i-e)*(t-n);if(l===0)return u===0&&c===0?[(t+s)/2,(e+i)/2]:null;const d=u/l;return[t+d*(s-t),e+d*(i-e)]}static isPolygonClockwise(t){let e=0;for(let s=0,i=t.length-2;s<t.length;i=s,s+=2)e+=(t[s]-t[i])*(t[s+1]+t[i+1]);return e>0}}class xa{constructor(t){this._svgMatrix=null,this._tempMatrix=new tt,this.renderer=t}_calcCanvasStyle(t,e){let s;return t.texture&&t.texture.baseTexture!==L.WHITE.baseTexture?t.texture.valid?(s=_t.getTintedPattern(t.texture,e),this.setPatternTransform(s,t.matrix||tt.IDENTITY)):s="#808080":s=`#${`00000${(e|0).toString(16)}`.slice(-6)}`,s}render(t){const e=this.renderer,s=e.canvasContext.activeContext,i=t.worldAlpha,n=t.transform.worldTransform;e.canvasContext.setContextTransform(n),e.canvasContext.setBlendMode(t.blendMode);const a=t.geometry.graphicsData;let o,h;const l=Y.shared.setValue(t.tint).toArray();for(let u=0;u<a.length;u++){const c=a[u],d=c.shape,f=c.fillStyle,p=c.lineStyle,m=c.fillStyle.color|0,g=c.lineStyle.color|0;if(c.matrix&&e.canvasContext.setContextTransform(n.copyTo(this._tempMatrix).append(c.matrix)),f.visible&&(o=this._calcCanvasStyle(f,Y.shared.setValue(m).multiply(l).toNumber())),p.visible&&(h=this._calcCanvasStyle(p,Y.shared.setValue(g).multiply(l).toNumber())),s.lineWidth=p.width,s.lineCap=p.cap,s.lineJoin=p.join,s.miterLimit=p.miterLimit,c.type===rt.POLY){s.beginPath();const _=d;let x=_.points;const y=c.holes;let b,T,S,A,w;s.moveTo(x[0],x[1]);for(let R=2;R<x.length;R+=2)s.lineTo(x[R],x[R+1]);if(_.closeStroke&&s.closePath(),y.length>0){w=[],b=0,S=x[0],A=x[1];for(let R=2;R+2<x.length;R+=2)b+=(x[R]-S)*(x[R+3]-A)-(x[R+2]-S)*(x[R+1]-A);for(let R=0;R<y.length;R++)if(x=y[R].shape.points,!!x){T=0,S=x[0],A=x[1];for(let M=2;M+2<x.length;M+=2)T+=(x[M]-S)*(x[M+3]-A)-(x[M+2]-S)*(x[M+1]-A);if(T*b<0){s.moveTo(x[0],x[1]);for(let M=2;M<x.length;M+=2)s.lineTo(x[M],x[M+1])}else{s.moveTo(x[x.length-2],x[x.length-1]);for(let M=x.length-4;M>=0;M-=2)s.lineTo(x[M],x[M+1])}y[R].shape.closeStroke&&s.closePath(),w[R]=T*b<0}}f.visible&&(s.globalAlpha=f.alpha*i,s.fillStyle=o,s.fill()),p.visible&&this.paintPolygonStroke(_,p,h,y,w,i,s)}else if(c.type===rt.RECT){const _=d;if(f.visible&&(s.globalAlpha=f.alpha*i,s.fillStyle=o,s.fillRect(_.x,_.y,_.width,_.height)),p.visible){const x=p.width*(.5-(1-p.alignment)),y=_.width+2*x,b=_.height+2*x;s.globalAlpha=p.alpha*i,s.strokeStyle=h,s.strokeRect(_.x-x,_.y-x,y,b)}}else if(c.type===rt.CIRC){const _=d;if(s.beginPath(),s.arc(_.x,_.y,_.radius,0,2*Math.PI),s.closePath(),f.visible&&(s.globalAlpha=f.alpha*i,s.fillStyle=o,s.fill()),p.visible){if(p.alignment!==.5){const x=p.width*(.5-(1-p.alignment));s.beginPath(),s.arc(_.x,_.y,_.radius+x,0,2*Math.PI),s.closePath()}s.globalAlpha=p.alpha*i,s.strokeStyle=h,s.stroke()}}else if(c.type===rt.ELIP){const _=d,x=p.alignment===1;if(x||this.paintEllipse(_,f,p,o,i,s),p.visible){if(p.alignment!==.5){const y=.5522848,b=p.width*(.5-(1-p.alignment)),T=(_.width+b)*2,S=(_.height+b)*2,A=_.x-T/2,w=_.y-S/2,R=T/2*y,M=S/2*y,H=A+T,B=w+S,E=A+T/2,I=w+S/2;s.beginPath(),s.moveTo(A,I),s.bezierCurveTo(A,I-M,E-R,w,E,w),s.bezierCurveTo(E+R,w,H,I-M,H,I),s.bezierCurveTo(H,I+M,E+R,B,E,B),s.bezierCurveTo(E-R,B,A,I+M,A,I),s.closePath()}s.globalAlpha=p.alpha*i,s.strokeStyle=h,s.stroke()}x&&this.paintEllipse(_,f,p,o,i,s)}else if(c.type===rt.RREC){const _=d,x=p.alignment===1;if(x||this.paintRoundedRectangle(_,f,p,o,i,s),p.visible){if(p.alignment!==.5){const y=_.width,b=_.height,T=p.width*(.5-(1-p.alignment)),S=_.x-T,A=_.y-T,w=_.width+2*T,R=_.height+2*T,M=T*(p.alignment>=1?Math.min(w/y,R/b):Math.min(y/w,b/R));let H=_.radius+M;const B=Math.min(w,R)/2;H=H>B?B:H,s.beginPath(),s.moveTo(S,A+H),s.lineTo(S,A+R-H),s.quadraticCurveTo(S,A+R,S+H,A+R),s.lineTo(S+w-H,A+R),s.quadraticCurveTo(S+w,A+R,S+w,A+R-H),s.lineTo(S+w,A+H),s.quadraticCurveTo(S+w,A,S+w-H,A),s.lineTo(S+H,A),s.quadraticCurveTo(S,A,S,A+H),s.closePath()}s.globalAlpha=p.alpha*i,s.strokeStyle=h,s.stroke()}x&&this.paintRoundedRectangle(_,f,p,o,i,s)}}}paintPolygonStroke(t,e,s,i,n,a,o){if(e.alignment!==.5){const h=e.width*(.5-(1-e.alignment));let l=ir.offsetPolygon(t.points,h),u;o.beginPath(),o.moveTo(l[0],l[1]);for(let c=2;c<l.length;c+=2)o.lineTo(l[c],l[c+1]);t.closeStroke&&o.closePath();for(let c=0;c<i.length;c++){if(u=i[c].shape.points,l=ir.offsetPolygon(u,h),n[c]){o.moveTo(l[0],l[1]);for(let d=2;d<l.length;d+=2)o.lineTo(l[d],l[d+1])}else{o.moveTo(l[l.length-2],l[l.length-1]);for(let d=l.length-4;d>=0;d-=2)o.lineTo(l[d],l[d+1])}i[c].shape.closeStroke&&o.closePath()}}o.globalAlpha=e.alpha*a,o.strokeStyle=s,o.stroke()}paintEllipse(t,e,s,i,n,a){const o=t.width*2,h=t.height*2,l=t.x-o/2,u=t.y-h/2,c=.5522848,d=o/2*c,f=h/2*c,p=l+o,m=u+h,g=l+o/2,_=u+h/2;s.alignment===0&&a.save(),a.beginPath(),a.moveTo(l,_),a.bezierCurveTo(l,_-f,g-d,u,g,u),a.bezierCurveTo(g+d,u,p,_-f,p,_),a.bezierCurveTo(p,_+f,g+d,m,g,m),a.bezierCurveTo(g-d,m,l,_+f,l,_),a.closePath(),s.alignment===0&&a.clip(),e.visible&&(a.globalAlpha=e.alpha*n,a.fillStyle=i,a.fill()),s.alignment===0&&a.restore()}paintRoundedRectangle(t,e,s,i,n,a){const o=t.x,h=t.y,l=t.width,u=t.height;let c=t.radius;const d=Math.min(l,u)/2;c=c>d?d:c,s.alignment===0&&a.save(),a.beginPath(),a.moveTo(o,h+c),a.lineTo(o,h+u-c),a.quadraticCurveTo(o,h+u,o+c,h+u),a.lineTo(o+l-c,h+u),a.quadraticCurveTo(o+l,h+u,o+l,h+u-c),a.lineTo(o+l,h+c),a.quadraticCurveTo(o+l,h,o+l-c,h),a.lineTo(o+c,h),a.quadraticCurveTo(o,h,o,h+c),a.closePath(),s.alignment===0&&a.clip(),e.visible&&(a.globalAlpha=e.alpha*n,a.fillStyle=i,a.fill()),s.alignment===0&&a.restore()}setPatternTransform(t,e){if(this._svgMatrix!==!1){if(!this._svgMatrix){const s=document.createElementNS("http://www.w3.org/2000/svg","svg");if(s!=null&&s.createSVGMatrix&&(this._svgMatrix=s.createSVGMatrix()),!this._svgMatrix||!t.setTransform){this._svgMatrix=!1;return}}this._svgMatrix.a=e.a,this._svgMatrix.b=e.b,this._svgMatrix.c=e.c,this._svgMatrix.d=e.d,this._svgMatrix.e=e.tx,this._svgMatrix.f=e.ty,t.setTransform(this._svgMatrix.inverse())}}destroy(){this.renderer=null,this._svgMatrix=null,this._tempMatrix=null}}xa.extension={name:"graphics",type:D.CanvasRendererPlugin},U.add(xa),Object.defineProperties(N,{MESH_CANVAS_PADDING:{get(){return At.defaultCanvasPadding},set(r){At.defaultCanvasPadding=r}}}),Xe.prototype._renderCanvas=function(r,t){r.plugins.mesh.render(t)},Qs.prototype._cachedTint=16777215,Qs.prototype._tintedCanvas=null,Qs.prototype._canvasUvs=null,Qs.prototype._renderCanvas=function(r){const t=r.canvasContext.activeContext,e=this.worldTransform,s=this.tintValue!==16777215,i=this.texture;if(!i.valid)return;s&&this._cachedTint!==this.tintValue&&(this._cachedTint=this.tintValue,this._tintedCanvas=_t.getTintedCanvas(this,this.tintValue));const n=s?this._tintedCanvas:i.baseTexture.getDrawableSource();this._canvasUvs||(this._canvasUvs=[0,0,0,0,0,0,0,0]);const a=this.vertices,o=this._canvasUvs,h=s?0:i.frame.x,l=s?0:i.frame.y,u=h+i.frame.width,c=l+i.frame.height;o[0]=h,o[1]=h+this._leftWidth,o[2]=u-this._rightWidth,o[3]=u,o[4]=l,o[5]=l+this._topHeight,o[6]=c-this._bottomHeight,o[7]=c;for(let d=0;d<8;d++)o[d]*=i.baseTexture.resolution;t.globalAlpha=this.worldAlpha,r.canvasContext.setBlendMode(this.blendMode),r.canvasContext.setContextTransform(e,this.roundPixels);for(let d=0;d<3;d++)for(let f=0;f<3;f++){const p=f*2+d*8,m=Math.max(1,o[f+1]-o[f]),g=Math.max(1,o[d+5]-o[d+4]),_=Math.max(1,a[p+10]-a[p]),x=Math.max(1,a[p+11]-a[p+1]);t.drawImage(n,o[f],o[d+4],m,g,a[p],a[p+1],_,x)}};let ku=!1;At.prototype._cachedTint=16777215,At.prototype._tintedCanvas=null,At.prototype._cachedTexture=null,At.prototype._renderCanvas=function(r){this.shader.uvMatrix&&(this.shader.uvMatrix.update(),this.calculateUvs()),this.material._renderCanvas?this.material._renderCanvas(r,this):ku||(ku=!0,globalThis.console.warn("Mesh with custom shaders are not supported in CanvasRenderer."))},At.prototype._canvasPadding=null,At.defaultCanvasPadding=0,Object.defineProperty(At.prototype,"canvasPadding",{get(){var r;return(r=this._canvasPadding)!=null?r:At.defaultCanvasPadding},set(r){this._canvasPadding=r}}),yu.prototype._renderCanvas=function(r){this.autoUpdate&&this.geometry.getBuffer("aVertexPosition").update(),this.shader.update&&this.shader.update(),this.calculateUvs(),this.material._renderCanvas(r,this)},xu.prototype._renderCanvas=function(r){(this.autoUpdate||this.geometry._width!==this.shader.texture.height)&&(this.geometry._width=this.shader.texture.height,this.geometry.update()),this.shader.update&&this.shader.update(),this.calculateUvs(),this.material._renderCanvas(r,this)};class ba{constructor(t){this.renderer=t}render(t){const e=this.renderer,s=t.worldTransform;e.canvasContext.activeContext.globalAlpha=t.worldAlpha,e.canvasContext.setBlendMode(t.blendMode),e.canvasContext.setContextTransform(s,t.roundPixels),t.drawMode!==Ot.TRIANGLES?this._renderTriangleMesh(t):this._renderTriangles(t)}_renderTriangleMesh(t){const e=t.geometry.buffers[0].data.length;for(let s=0;s<e-2;s++){const i=s*2;this._renderDrawTriangle(t,i,i+2,i+4)}}_renderTriangles(t){const e=t.geometry.getIndex().data,s=e.length;for(let i=0;i<s;i+=3){const n=e[i]*2,a=e[i+1]*2,o=e[i+2]*2;this._renderDrawTriangle(t,n,a,o)}}_renderDrawTriangle(t,e,s,i){var n;const a=this.renderer.canvasContext.activeContext,o=t.geometry.buffers[0].data,{uvs:h,texture:l}=t;if(!l.valid)return;const u=t.tintValue!==16777215,c=l.baseTexture,d=c.width,f=c.height;t._cachedTexture&&t._cachedTexture.baseTexture!==c&&(t._cachedTint=16777215,(n=t._cachedTexture)==null||n.destroy(),t._cachedTexture=null,t._tintedCanvas=null),u&&t._cachedTint!==t.tintValue&&(t._cachedTint=t.tintValue,t._cachedTexture=t._cachedTexture||new L(c),t._tintedCanvas=_t.getTintedCanvas({texture:t._cachedTexture},t.tintValue));const p=u?t._tintedCanvas:c.getDrawableSource(),m=h[e]*c.width,g=h[s]*c.width,_=h[i]*c.width,x=h[e+1]*c.height,y=h[s+1]*c.height,b=h[i+1]*c.height;let T=o[e],S=o[s],A=o[i],w=o[e+1],R=o[s+1],M=o[i+1];const H=t.canvasPadding/this.renderer.canvasContext.activeResolution;if(H>0){const{a:ht,b:F,c:O,d:Z}=t.worldTransform,Q=(T+S+A)/3,J=(w+R+M)/3;let st=T-Q,et=w-J,it=ht*st+O*et,lt=F*st+Z*et,vt=Math.sqrt(it*it+lt*lt),nt=1+H/vt;T=Q+st*nt,w=J+et*nt,st=S-Q,et=R-J,it=ht*st+O*et,lt=F*st+Z*et,vt=Math.sqrt(it*it+lt*lt),nt=1+H/vt,S=Q+st*nt,R=J+et*nt,st=A-Q,et=M-J,it=ht*st+O*et,lt=F*st+Z*et,vt=Math.sqrt(it*it+lt*lt),nt=1+H/vt,A=Q+st*nt,M=J+et*nt}a.save(),a.beginPath(),a.moveTo(T,w),a.lineTo(S,R),a.lineTo(A,M),a.closePath(),a.clip();const B=m*y+x*_+g*b-y*_-x*g-m*b,E=T*y+x*A+S*b-y*A-x*S-T*b,I=m*S+T*_+g*A-S*_-T*g-m*A,V=m*y*A+x*S*_+T*g*b-T*y*_-x*g*A-m*S*b,q=w*y+x*M+R*b-y*M-x*R-w*b,j=m*R+w*_+g*M-R*_-w*g-m*M,W=m*y*M+x*R*_+w*g*b-w*y*_-x*g*M-m*R*b;a.transform(E/B,q/B,I/B,j/B,V/B,W/B),a.drawImage(p,0,0,d*c.resolution,f*c.resolution,0,0,d,f),a.restore(),this.renderer.canvasContext.invalidateBlendMode()}renderMeshFlat(t){const e=this.renderer.canvasContext.activeContext,s=t.geometry.getBuffer("aVertexPosition").data,i=s.length/2;e.beginPath();for(let n=1;n<i-2;++n){const a=n*2,o=s[a],h=s[a+1],l=s[a+2],u=s[a+3],c=s[a+4],d=s[a+5];e.moveTo(o,h),e.lineTo(l,u),e.lineTo(c,d)}e.fillStyle="#FF0000",e.fill(),e.closePath()}destroy(){this.renderer=null}}ba.extension={name:"mesh",type:D.CanvasRendererPlugin},U.add(ba);const Gu=16;function yg(r,t){const e=r;if(t instanceof X){const s=t.source,i=s.width===0?e.canvas.width:Math.min(e.canvas.width,s.width),n=s.height===0?e.canvas.height:Math.min(e.canvas.height,s.height);return e.ctx.drawImage(s,0,0,i,n,0,0,e.canvas.width,e.canvas.height),!0}return!1}class Ta extends Js{constructor(t){super(t),this.uploadHookHelper=this,this.canvas=N.ADAPTER.createCanvas(Gu,Gu),this.ctx=this.canvas.getContext("2d"),this.registerUploadHook(yg)}destroy(){super.destroy(),this.ctx=null,this.canvas=null}}Ta.extension={name:"prepare",type:D.CanvasRendererSystem},U.add(Ta),Ut.prototype._tintedCanvas=null,Ut.prototype._renderCanvas=function(r){r.plugins.sprite.render(this)};const $u=new tt;class Ea{constructor(t){this.renderer=t}render(t){const e=t._texture,s=this.renderer,i=s.canvasContext.activeContext,n=s.canvasContext.activeResolution;if(!e.valid)return;const a=e._frame.width,o=e._frame.height;let h=e._frame.width,l=e._frame.height;e.trim&&(h=e.trim.width,l=e.trim.height);let u=t.transform.worldTransform,c=0,d=0;const f=e.baseTexture.getDrawableSource();if(e.orig.width<=0||e.orig.height<=0||!e.valid||!f)return;s.canvasContext.setBlendMode(t.blendMode,!0),i.globalAlpha=t.worldAlpha;const p=e.baseTexture.scaleMode===Bt.LINEAR,m=s.canvasContext.smoothProperty;m&&i[m]!==p&&(i[m]=p),e.trim?(c=e.trim.width/2+e.trim.x-t.anchor.x*e.orig.width,d=e.trim.height/2+e.trim.y-t.anchor.y*e.orig.height):(c=(.5-t.anchor.x)*e.orig.width,d=(.5-t.anchor.y)*e.orig.height),e.rotate&&(u.copyTo($u),u=$u,at.matrixAppendRotationInv(u,e.rotate,c,d),c=0,d=0),c-=h/2,d-=l/2,s.canvasContext.setContextTransform(u,t.roundPixels,1),t.roundPixels&&(c=c|0,d=d|0);const g=e.baseTexture.resolution,_=s.canvasContext._outerBlend;_&&(i.save(),i.beginPath(),i.rect(c*n,d*n,h*n,l*n),i.clip()),t.tint!==16777215?((t._cachedTint!==t.tintValue||t._tintedCanvas.tintId!==t._texture._updateID)&&(t._cachedTint=t.tintValue,t._tintedCanvas=_t.getTintedCanvas(t,t.tintValue)),i.drawImage(t._tintedCanvas,0,0,Math.floor(a*g),Math.floor(o*g),Math.floor(c*n),Math.floor(d*n),Math.floor(h*n),Math.floor(l*n))):i.drawImage(f,e._frame.x*g,e._frame.y*g,Math.floor(a*g),Math.floor(o*g),Math.floor(c*n),Math.floor(d*n),Math.floor(h*n),Math.floor(l*n)),_&&i.restore(),s.canvasContext.setBlendMode(C.NORMAL)}destroy(){this.renderer=null}}return Ea.extension={name:"sprite",type:D.CanvasRendererPlugin},U.add(Ea),v.ALPHA_MODES=wt,v.AbstractMultiResource=Ln,v.AccessibilityManager=Jn,v.AlphaFilter=Hh,v.AnimatedSprite=pi,v.Application=sl,v.ArrayResource=Ph,v.Assets=Xs,v.AssetsClass=Ml,v.Attribute=Es,v.BLEND_MODES=C,v.BUFFER_BITS=dr,v.BUFFER_TYPE=jt,v.BackgroundSystem=Is,v.BaseImageResource=re,v.BasePrepare=Js,v.BaseRenderTexture=Ur,v.BaseTexture=X,v.BatchDrawCall=Sr,v.BatchGeometry=nn,v.BatchRenderer=Ee,v.BatchShaderGenerator=th,v.BatchSystem=cn,v.BatchTextureArray=Nr,v.BitmapFont=xe,v.BitmapFontData=tr,v.BitmapText=lg,v.BlobResource=jl,v.BlurFilter=Vh,v.BlurFilterPass=ti,v.Bounds=Ls,v.BrowserAdapter=Oa,v.Buffer=dt,v.BufferResource=Ts,v.BufferSystem=Fn,v.CLEAR_MODES=Vt,v.COLOR_MASK_BITS=Da,v.Cache=Se,v.CanvasContextSystem=Vn,v.CanvasExtract=va,v.CanvasGraphicsRenderer=xa,v.CanvasMaskSystem=jn,v.CanvasMeshRenderer=ba,v.CanvasObjectRendererSystem=Xn,v.CanvasPrepare=Ta,v.CanvasRenderer=Ns,v.CanvasResource=Un,v.CanvasSpriteRenderer=Ea,v.Circle=Rr,v.Color=Y,v.ColorMatrixFilter=ei,v.CompressedTextureResource=Ce,v.Container=Ct,v.ContextSystem=Ps,v.CountLimiter=bu,v.CubeResource=Dh,v.DEG_TO_RAD=Ho,v.DRAW_MODES=Ot,v.DisplacementFilter=jh,v.DisplayObject=ot,v.ENV=be,v.Ellipse=Ir,v.EventBoundary=Wh,v.EventSystem=si,v.ExtensionType=D,v.Extract=nu,v.FORMATS=P,v.FORMATS_TO_COMPONENTS=Yl,v.FXAAFilter=Xh,v.FederatedDisplayObject=Zh,v.FederatedEvent=Je,v.FederatedMouseEvent=Hs,v.FederatedPointerEvent=kt,v.FederatedWheelEvent=He,v.FillStyle=Ks,v.Filter=Et,v.FilterState=ih,v.FilterSystem=_n,v.Framebuffer=Lr,v.FramebufferSystem=vn,v.GC_MODES=fr,v.GLFramebuffer=nh,v.GLProgram=gh,v.GLTexture=Vr,v.GRAPHICS_CURVES=Um,v.GenerateTextureSystem=wn,v.Geometry=fe,v.GeometrySystem=xn,v.Graphics=as,v.GraphicsData=qs,v.GraphicsGeometry=du,v.HTMLText=vg,v.HTMLTextStyle=yi,v.IGLUniformData=zd,v.INSTALLED=wr,v.INTERNAL_FORMATS=Tt,v.INTERNAL_FORMAT_TO_BYTES_PER_PIXEL=zs,v.ImageBitmapResource=$e,v.ImageResource=dn,v.LINE_CAP=ye,v.LINE_JOIN=Mt,v.LineStyle=di,v.LoaderParserPriority=$t,v.MASK_TYPES=pt,v.MIPMAP_MODES=Ht,v.MSAA_QUALITY=ft,v.MaskData=hh,v.MaskSystem=bn,v.Matrix=tt,v.Mesh=At,v.MeshBatchUvs=fu,v.MeshGeometry=Zs,v.MeshMaterial=Xe,v.MultisampleSystem=Bn,v.NineSlicePlane=Qs,v.NoiseFilter=zh,v.ObjectRenderer=Cs,v.ObjectRendererSystem=Nn,v.ObservablePoint=pe,v.PI_2=As,v.PRECISION=Rt,v.ParticleContainer=Lh,v.ParticleRenderer=Wn,v.PlaneGeometry=gu,v.PluginSystem=En,v.Point=K,v.Polygon=Be,v.Prepare=pa,v.Program=se,v.ProjectionSystem=An,v.Quad=rh,v.QuadUv=mn,v.RAD_TO_DEG=$o,v.RENDERER_TYPE=cr,v.Rectangle=z,v.RenderTexture=Yt,v.RenderTexturePool=pn,v.RenderTextureSystem=Sn,v.Renderer=zr,v.ResizePlugin=ta,v.Resource=Qe,v.RopeGeometry=_u,v.RoundedRectangle=Pr,v.Runner=Pt,v.SAMPLER_TYPES=k,v.SCALE_MODES=Bt,v.SHAPES=rt,v.SVGResource=Wr,v.ScissorSystem=fh,v.Shader=Wt,v.ShaderSystem=Cn,v.SimpleMesh=yu,v.SimplePlane=vu,v.SimpleRope=xu,v.Sprite=Ut,v.SpriteMaskFilter=oh,v.Spritesheet=ma,v.StartupSystem=Ds,v.State=ee,v.StateSystem=Eh,v.StencilSystem=Tn,v.SystemManager=Rn,v.TARGETS=Me,v.TEXT_GRADIENT=$s,v.TYPES=$,v.TYPES_TO_BYTES_PER_COMPONENT=aa,v.TYPES_TO_BYTES_PER_PIXEL=ql,v.TemporaryDisplayObject=Bh,v.Text=Jr,v.TextFormat=er,v.TextMetrics=ge,v.TextStyle=_e,v.Texture=L,v.TextureGCSystem=Ae,v.TextureMatrix=$r,v.TextureSystem=In,v.TextureUvs=fn,v.Ticker=bt,v.TickerPlugin=Dn,v.TilingSprite=qr,v.TilingSpriteRenderer=zn,v.TimeLimiter=Jm,v.Transform=Dr,v.TransformFeedback=mf,v.TransformFeedbackSystem=Pn,v.UPDATE_PRIORITY=me,v.UniformGroup=Lt,v.VERSION=gf,v.VideoResource=$n,v.ViewSystem=Bs,v.ViewableBuffer=Ar,v.WRAP_MODES=Zt,v.XMLFormat=mi,v.XMLStringFormat=gi,v.accessibleTarget=Qh,v.autoDetectFormat=Cu,v.autoDetectRenderer=Rh,v.autoDetectResource=sn,v.cacheTextureArray=Dl,v.canUseNewCanvasBlendModes=Hn,v.canvasUtils=_t,v.checkDataUrl=Ve,v.checkExtension=ve,v.checkMaxIfStatementsInShader=Lo,v.convertToList=Gt,v.copySearchParams=ii,v.createStringVariations=nl,v.createTexture=ss,v.createUBOElements=yh,v.curves=Re,v.defaultFilterVertex=On,v.defaultVertex=Ih,v.detectAvif=Bl,v.detectCompressedTextures=Vl,v.detectDefaults=Ll,v.detectMp4=kl,v.detectOgv=Gl,v.detectWebm=Ul,v.detectWebp=Fl,v.extensions=U,v.filters=Kn,v.generateProgram=_h,v.generateUniformBufferSync=bh,v.getFontFamilyName=dl,v.getTestContext=Wo,v.getUBOData=xh,v.graphicsUtils=$m,v.groupD8=at,v.isMobile=Xt,v.isSingleItem=Vs,v.loadBitmapFont=Nu,v.loadDDS=Jl,v.loadImageBitmap=xl,v.loadJson=hl,v.loadKTX=su,v.loadSVG=El,v.loadTextures=js,v.loadTxt=ll,v.loadVideo=Cl,v.loadWebFont=fl,v.parseDDS=zl,v.parseKTX=Kl,v.resolveCompressedTextureUrl=ru,v.resolveTextureUrl=$l,v.settings=N,v.spritesheetAsset=Su,v.uniformParsers=ke,v.unsafeEvalSupported=Jo,v.utils=Do,v}({});
// Disabled to avoid warnings in Chrome (see https://github.com/4ian/GDevelop/pull/3947)
// //# sourceMappingURL=pixi-legacy.min.js.map