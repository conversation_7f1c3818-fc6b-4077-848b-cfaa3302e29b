# GDevelop Core (library to create, manipulate and export games)

This is the core library used to handle games, build platforms, extensions and tools for GDevelop. The editor is based on classes and tools offered by this library.

GDevelop Core is a portable C++ library, compiled to be used in JavaScript in the editor.

## 1) Getting started 🤓

First, take a look at the _[README.md](../README.md)_ at the root of the repository and the [developer documentation](https://docs.gdevelop.io/).

## 2) How to contribute 😎

Any contribution is welcome! Whether you want to submit a bug report, a feature request
or any pull request so as to add a nice feature, do not hesitate to get in touch.

-   Check [the **roadmap** for ideas and features planned](https://trello.com/b/qf0lM7k8/gdevelop-roadmap).

-   Follow the [Development](https://github.com/4ian/GDevelop/tree/master/newIDE#development) section of the README to set up GDevelop and start modifying either **the editor** or **[the game engine/extensions](https://github.com/4ian/GDevelop/tree/master/newIDE#development-of-the-game-engine-or-extensions)**.

-   To submit your changes, you have first to create a Fork on GitHub (use the Fork button on the top right), then [create a Pull Request](https://help.github.com/articles/creating-a-pull-request-from-a-fork/).

## License

GDevelop Core is distributed under the MIT license: see [LICENSE.md](LICENSE.md) for
more information.
