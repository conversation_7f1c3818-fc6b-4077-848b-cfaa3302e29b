{"name": "tilemap", "version": "0.0.0", "license": "MIT", "description": "", "main": "dist/TileMapHelper.js", "types": "dist/index.d.ts", "files": ["src", "dist"], "scripts": {"test": "karma start --browsers ChromeHeadless --single-run", "test:watch": "karma start --browsers ChromeHeadless", "test:firefox": "karma start --browsers Firefox --single-run", "test:firefox:watch": "karma start --browsers Firefox", "tsc": "tsc", "build": "rollup -c", "dev": "rollup -c -w", "prepublishOnly": "npm run build", "format": "prettier --write \"src/**/*.ts\"", "check-format": "prettier --list-different \"src/**/*.ts\""}, "dependencies": {"@types/offscreencanvas": "^2019.6.4", "pako": "^2.0.4", "pixi.js": "7.4.2"}, "devDependencies": {"@rollup/plugin-node-resolve": "13.3.0", "@rollup/plugin-typescript": "8.3.3", "@types/expect.js": "0.3.32", "@types/mocha": "5.2.7", "expect.js": "0.3.1", "karma": "6.4.3", "karma-chrome-launcher": "3.2.0", "karma-firefox-launcher": "2.1.3", "karma-mocha": "1.3.0", "karma-typescript": "5.5.4", "mocha": "6.2.3", "prettier": "2.1.2", "rollup": "2.79.1", "rollup-plugin-terser": "7.0.2", "ts-loader": "9.5.1", "tslib": "2.6.2", "typescript": "5.4.5"}, "repository": {"type": "git", "url": ""}, "keywords": [], "author": "", "contributors": [], "bugs": {"url": ""}, "homepage": ""}