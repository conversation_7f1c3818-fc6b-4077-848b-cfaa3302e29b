// @flow
import { Trans } from '@lingui/macro';
import React, { useState, useEffect } from 'react';
import Text from '../../UI/Text';
import { ProjectApi } from '../../Utils/GDevelopServices/ApiConfigs';
import { Column, Line, Spacer } from '../../UI/Grid';
import AlertMessage from '../../UI/AlertMessage';
import RaisedButton from '../../UI/RaisedButton';
import TextField from '../../UI/TextField';
import PublishFeeConfirmationModal from '../../UI/PublishFeeConfirmationModal';
import axios from 'axios';
import { getWalletAuthorizationHeader, isWalletAuthenticated } from '../../Utils/GDevelopServices/WalletAuthentication';
import { useWalletUser } from '../../Utils/store/WalletUserContext';
import { getAdminSettings } from '../../Utils/GDevelopServices/User';
import { useWallet } from '@solana/wallet-adapter-react';

/**
 * Uploads game details to the server
 * @param {Object} gameDetails - The game details to upload
 * @param {string} gameDetails.gameName - The name of the game
 * @param {string} gameDetails.gameDescription - The description of the game
 * @param {File} gameDetails.gameThumbnail - The thumbnail image for the game
 * @param {string} gameDetails.gameLink - The link to the game
 * @param {string} gameDetails.gameId - The UUID of the original game (to link saved and published games)
 * @returns {Promise<void>}
 */
export const uploadGameDetails = async ({
  gameName,
  gameDescription,
  gameThumbnail,
  gameLink,
  gameId,
  setError,
}) => {
  const formData = new FormData();
  formData.append('GameName', gameName);
  formData.append('GameDescription', gameDescription);
  formData.append('GameThumbnail', gameThumbnail);
  formData.append('GameURL', gameLink);

  // Add the game UUID to link the published game to the original saved game
  if (gameId) {
    formData.append('GameId', gameId);
  }

  try {
    const authHeader = getWalletAuthorizationHeader();
    await axios.post(`${ProjectApi.baseUrl}/publish`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: authHeader,
      },
    });

    return true;
  } catch (error) {
    if (
      error.response &&
      (error.response.status === 400 || error.response.status === 500)
    ) {
      setError('Please check your input fields, ensure they are correct');
    } else {
      setError(error.message);
    }
    console.error(error);
    return false;
  }
};

/**
 * Component for publishing a game without creating a token
 */
export const PublishGameForm = ({ gameLink, gameId, onSuccess }) => {
  const [gameName, setGameName] = useState('');
  const [gameDescription, setGameDescription] = useState('');
  const [gameThumbnail, setGameThumbnail] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [isCheckingBalance, setIsCheckingBalance] = useState(false);
  const [publishFeeUSD, setPublishFeeUSD] = useState(null);
  const [insufficientFunds, setInsufficientFunds] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const { fetchProfile, profile } = useWalletUser();
  const wallet = useWallet();

  // Fetch admin settings and check balance
  useEffect(() => {
    const checkBalanceAndSettings = async () => {
      setIsCheckingBalance(true);
      try {
        // Fetch admin settings to get the publish fee
        const settings = await getAdminSettings();
        if (settings && settings.publishFeeUSD) {
          setPublishFeeUSD(settings.publishFeeUSD);
        } else {
          // Default fee if not found in settings
          setPublishFeeUSD(0.01);
        }

        // Check if user has sufficient funds in their profile balance
        if (profile && profile.balance !== undefined && settings && settings.publishFeeUSD) {
          const userBalance = profile.balance;
          const requiredBalance = settings.publishFeeUSD;

          if (userBalance < requiredBalance) {
            setInsufficientFunds(true);
            setError(
              `Insufficient balance. You need at least ${requiredBalance.toFixed(2)} credits but have ${userBalance.toFixed(2)} credits`
            );
          } else {
            setInsufficientFunds(false);
            setError('');
          }
        }
      } catch (err) {
        console.error('Error checking balance or admin settings:', err);
        setError('Failed to check balance or publishing fee');
      } finally {
        setIsCheckingBalance(false);
      }
    };

    checkBalanceAndSettings();
  }, [profile]);

  const handleThumbnailChange = e => {
    if (e.target.files && e.target.files[0]) {
      setGameThumbnail(e.target.files[0]);
    }
  };

  const handlePublishClick = () => {
    // Check balance before proceeding
    if (insufficientFunds) {
      setError(
        `Insufficient balance. You need at least ${publishFeeUSD?.toFixed(2)} credits but have ${profile?.balance?.toFixed(2)} credits to publish your game.`
      );
      return;
    }

    // Show the confirmation modal instead of proceeding directly
    setShowConfirmationModal(true);
  };

  const publishGame = async () => {
    // Close the modal
    setShowConfirmationModal(false);
    
    setLoading(true);
    setError('');

    try {
      const result = await uploadGameDetails({
        gameName,
        gameDescription,
        gameThumbnail,
        gameLink,
        gameId, // Pass the game UUID to link with the saved game
        setError,
      });
      await fetchProfile();

      if (result) {
        setLoading(false);
        setSuccess(true);

        // Refresh the games list to show the newly published game
        try {
          if (isWalletAuthenticated() && wallet.publicKey) {
            const authHeader = getWalletAuthorizationHeader();
            if (authHeader) {
              // This will trigger a refresh of the games list in the UI when the user navigates back to the dashboard
              await axios.get(`${ProjectApi.baseUrl}/getUserPublishedGames`, {
                headers: {
                  Authorization: authHeader,
                },
              });
              console.log('Refreshed games list after publishing');
            }
          }
        } catch (error) {
          console.error('Error refreshing games list:', error);
        }

        if (onSuccess) onSuccess();
      } else {
        setLoading(false);
      }
    } catch (error) {
      setError(error.message || 'Failed to publish game');
      console.error(error);
      setLoading(false);
    }
  };

  return !success ? (
    <Column>
      <Text size="sub-title" style={{ textAlign: 'center', marginTop: 10 }}>
        <Trans>Enter your game details</Trans>
      </Text>
      <Spacer />
      <TextField
        floatingLabelText={<Trans>Game Name</Trans>}
        fullWidth
        onChange={(e, value) => setGameName(value)}
        value={gameName}
      />
      <TextField
        floatingLabelText={<Trans>Game Description</Trans>}
        fullWidth
        multiline
        rows={4}
        onChange={(e, value) => setGameDescription(value)}
        value={gameDescription}
      />
      <Spacer />
      <Text>
        <Trans>Game Thumbnail</Trans>
      </Text>
      <input type="file" accept="image/*" onChange={handleThumbnailChange} />
      <Spacer />
      {error && <AlertMessage kind="error">{error}</AlertMessage>}
      <Spacer />
      <Line justifyContent="center">
        <RaisedButton
          label={
            isCheckingBalance ? (
              <Trans>Checking Balance...</Trans>
            ) : !loading ? (
              <Trans>Publish Game</Trans>
            ) : (
              <Trans>Publishing...</Trans>
            )
          }
          primary
          id="publish-game-button"
          onClick={handlePublishClick}
          disabled={loading || isCheckingBalance || insufficientFunds || !gameName || !gameDescription || !gameThumbnail}
        />
        
        {/* Balance Confirmation Modal */}
        <PublishFeeConfirmationModal
          open={showConfirmationModal}
          onClose={() => setShowConfirmationModal(false)}
          onConfirm={publishGame}
          currentBalance={profile?.balance || 0}
          publishFee={publishFeeUSD || 0}
          insufficientFunds={insufficientFunds}
        />
      </Line>
    </Column>
  ) : (
    <Column>
      <Text size="sub-title" style={{ textAlign: 'center', marginTop: 10 }}>
        <Trans>
          Congratulations! Your game has been successfully published and is now
          available in the Play tab.
        </Trans>
      </Text>
    </Column>
  );
};

export const PublishGameExplanationHeader = () => {
  return (
    <Column noMargin>
      <Line>
        <Text align="center">
          <Trans>This will export and deploy your game to Sonic Cloud.</Trans>
        </Text>
      </Line>
    </Column>
  );
};

export const publishGameExporter = {
  key: 'publishgame-only',
  tabName: <Trans>Publish Game</Trans>,
  name: <Trans>Deploy your Game to Cloud without creating a token</Trans>,
  helpPage: '/publishing/Publish_html5_game_in_a_local_folder',
};
