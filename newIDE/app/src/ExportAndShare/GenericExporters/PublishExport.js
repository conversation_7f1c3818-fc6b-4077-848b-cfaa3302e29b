import { Trans } from '@lingui/macro';
import React, { useState, useEffect } from 'react';
import Text from '../../UI/Text';
import {
  GDevelopTokenStorage,
  ProjectApi,
} from '../../Utils/GDevelopServices/ApiConfigs';
import PublishFeeConfirmationModal from '../../UI/PublishFeeConfirmationModal';
import { Column, Line, Spacer } from '../../UI/Grid';
import AlertMessage from '../../UI/AlertMessage';
import RaisedButton from '../../UI/RaisedButton';
import Link from '../../UI/Link';
import TextField from '../../UI/TextField';
import { useWallet } from '@solana/wallet-adapter-react';
import {
  Connection,
  PublicKey,
  SystemProgram,
  VersionedTransaction,
  Transaction,
  Keypair,
  LAMPORTS_PER_SOL,
} from '@solana/web3.js';
import axios from 'axios';
import { Program, AnchorProvider } from '@coral-xyz/anchor';
import { uploadGameDetails as uploadGameToServer } from '../ShareDialog/GamePublisher';
import { useWalletUser } from '../../Utils/store/WalletUserContext';
import { getAdminSettings } from '../../Utils/GDevelopServices/User';
import { DEFAULT_USD_FEE } from '../../Utils/const';
import {
  isWalletAuthenticated,
  getWalletAuthorizationHeader,
} from '../../Utils/GDevelopServices/WalletAuthentication';
import { GDevelopProjectApi } from '../../Utils/GDevelopServices/ApiConfigs';

const FEE_RECIPIENT_PUBLIC_KEY = new PublicKey(
  'BjhyGmPToJaUtBDy2frA6PSoyw42ETHe4j4Q4TeWLWMj' // Replace with actual fee recipient public key
);
const web3Connection = new Connection(
  'https://mainnet.helius-rpc.com/?api-key=c5f044d1-1562-46cf-adfa-f247f435cd6b',
  'confirmed'
);
const api_baseurl = 'https://engine-be.sonicengine.net';

const PROGRAM_ID = new PublicKey(
  'BcjPFT8JtRywB6R68PiTZowaaz1b9runSR9U2o16jJyN'
);
const IDL = {
  address: 'BcjPFT8JtRywB6R68PiTZowaaz1b9runSR9U2o16jJyN',
  metadata: {
    name: 'game_manager',
    version: '0.1.0',
    spec: '0.1.0',
    description: 'Created with Anchor',
  },
  instructions: [
    {
      name: 'deploy_game',
      discriminator: [105, 217, 28, 20, 150, 155, 121, 119],
      accounts: [
        {
          name: 'game',
          writable: true,
          pda: {
            seeds: [
              {
                kind: 'const',
                value: [103, 97, 109, 101],
              },
              {
                kind: 'arg',
                path: 'title',
              },
              {
                kind: 'account',
                path: 'user',
              },
            ],
          },
        },
        {
          name: 'user',
          writable: true,
          signer: true,
        },
        {
          name: 'system_program',
          address: '11111111111111111111111111111111',
        },
      ],
      args: [
        {
          name: 'title',
          type: 'string',
        },
        {
          name: 'description',
          type: 'string',
        },
      ],
    },
  ],
  accounts: [
    {
      name: 'Game',
      discriminator: [27, 90, 166, 125, 74, 100, 121, 18],
    },
  ],
  types: [
    {
      name: 'Game',
      type: {
        kind: 'struct',
        fields: [
          {
            name: 'title',
            type: 'string',
          },
          {
            name: 'description',
            type: 'string',
          },
          {
            name: 'authority',
            type: 'pubkey',
          },
        ],
      },
    },
  ],
};

export const deployTokenAndCreateToken = async (
  tokenData,
  userPublicKey,
  gameId,
  wallet,
  setError,
  setLoading,
  imageFile,
  setSuccess
) => {
  if (!wallet.connected) {
    setError('Please connect your wallet first');
    setLoading(false);
    throw new Error('Wallet not connected');
  }

  try {
    console.log('[1] Received TokenData and UserPublicKey:', { userPublicKey });
    // Create FormData to send image with the request
    const formData = new FormData();
    formData.append('tokenData', JSON.stringify(tokenData));
    formData.append('userPublicKey', userPublicKey);
    formData.append('gameId', gameId);
    if (imageFile) {
      formData.append('image', imageFile);
    }
    console.log('[2] FormData:', `${GDevelopTokenStorage.baseUrl}/create`);

    const backendResponse = await axios.post(
      `${GDevelopTokenStorage.baseUrl}/create`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    console.log('[2] Received response from backend:', backendResponse.data);

    if (backendResponse.data.error) {
      throw new Error(`Backend error: ${backendResponse.data.error}`);
    }

    const { data, mintKeypair, tokenId } = backendResponse.data;

    if (!data || typeof data !== 'string') {
      throw new Error(
        `Invalid transaction data: expected a base64 string, got ${JSON.stringify(
          data
        )}`
      );
    }

    if (!mintKeypair || !mintKeypair.publicKey || !mintKeypair.secretKey) {
      throw new Error('Invalid mintKeypair: missing publicKey or secretKey');
    }

    if (!tokenId) {
      throw new Error('Token ID not returned from backend');
    }

    const transactionBuffer = Buffer.from(data, 'base64');
    const versionedTx = VersionedTransaction.deserialize(
      new Uint8Array(transactionBuffer)
    );
    console.log('[3] Deserialized versioned transaction');

    console.log(
      '[4] Signatures before wallet signing:',
      versionedTx.signatures.map((sig, i) => ({
        index: i,
        account:
          versionedTx.message.staticAccountKeys[i]?.toBase58() || 'undefined',
        signature: sig.toString(),
      }))
    );

    const {
      blockhash: feeBlockhash,
      lastValidBlockHeight,
    } = (await web3Connection.getLatestBlockhashAndContext()).value;
    console.log('[5] Got latest blockhash for fee transaction:', feeBlockhash);

    // Use the wallet from useWallet hook instead of Phantom
    if (!wallet.connected) {
      try {
        await wallet.connect();
      } catch (error) {
        throw new Error(
          'Failed to connect wallet: ' + (error.message || String(error))
        );
      }
    }

    const connectedPublicKey = wallet.publicKey.toString();
    console.log(`[6] Connected to wallet: ${connectedPublicKey}`);

    if (connectedPublicKey !== userPublicKey) {
      throw new Error(
        `Connected wallet (${connectedPublicKey}) does not match expected ${userPublicKey}`
      );
    }

    const userPubkey = new PublicKey(userPublicKey);
    const balanceBefore = await web3Connection.getBalance(userPubkey);
    console.log(
      `[7] Balance before transactions: ${balanceBefore / LAMPORTS_PER_SOL} SOL`
    );

    const balanceAfterFee = await web3Connection.getBalance(userPubkey);
    console.log(
      `[11] Balance after fee transaction: ${balanceAfterFee /
        LAMPORTS_PER_SOL} SOL`
    );

    try {
      await axios.post(
        `${GDevelopTokenStorage.baseUrl}/${tokenId}/game-deployed`,
        {
          isGameDeployed: true,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'ngrok-skip-browser-warning': 'true',
          },
        }
      );
      //
      console.log('[12] Game deployed status updated in backend');
    } catch (error) {
      console.error('[ERROR] Failed to update game deployed status:', error);
      throw new Error(
        'Failed to update game deployed status: ' +
          (error.message || String(error))
      );
    }

    const {
      blockhash: createBlockhash,
    } = (await web3Connection.getLatestBlockhashAndContext()).value;
    console.log(
      '[13] Got latest blockhash for create token transaction:',
      createBlockhash
    );

    versionedTx.message.recentBlockhash = createBlockhash;
    console.log('[14] Updated create token transaction with new blockhash');

    const mintSecretKey = Buffer.from(mintKeypair.secretKey, 'base64');
    const recreatedMintKeypair = Keypair.fromSecretKey(mintSecretKey);
    versionedTx.sign([recreatedMintKeypair]);
    console.log('[15] Signed create token transaction with mint keypair');

    console.log('[16] Requesting user signature from wallet for create token');
    let signedTx;
    try {
      // Use wallet.signTransaction instead of phantom.signTransaction
      signedTx = await wallet.signTransaction(versionedTx);
      console.log('[17] Create token transaction signed by user wallet');
      console.log(
        '[18] Final signatures:',
        signedTx.signatures.map((sig, i) => ({
          index: i,
          account:
            signedTx.message.staticAccountKeys[i]?.toBase58() || 'undefined',
          signature:
            Buffer.from(sig)
              .toString('hex')
              .substring(0, 20) + '...',
        }))
      );
    } catch (error) {
      console.error('[ERROR] Failed to sign create token transaction:', error);
      throw new Error(
        'Failed to sign create token transaction with wallet: ' +
          (error.message || String(error))
      );
    }

    console.log('[19] Sending create token transaction to network');
    const signature = await web3Connection.sendTransaction(signedTx, {
      skipPreflight: false,
      preflightCommitment: 'confirmed',
      maxRetries: 5,
    });

    console.log('[20] Create token transaction sent successfully:', signature);

    // await web3Connection.confirmTransaction(signature, 'confirmed');
    console.log(
      '[21] Create token transaction confirmed. Signature:',
      signature
    );

    try {
      await axios.post(
        `${GDevelopTokenStorage.baseUrl}/${tokenId}/deploy`,
        {
          createTokenHash: signature,
          createTransactionLink: `https://solscan.io/tx/${signature}?cluster=mainnet-beta`,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
      console.log('[22] Token deployment updated in backend');
    } catch (error) {
      console.error('[ERROR] Failed to update token deployment:', error);
      throw new Error(
        'Failed to update token deployment: ' + (error.message || String(error))
      );
    }

    const balanceAfterCreate = await web3Connection.getBalance(userPubkey);
    console.log(
      `[23] Balance after create token transaction: ${balanceAfterCreate /
        LAMPORTS_PER_SOL} SOL`
    );

    return {
      data: signature,
      createTransactionLink: `https://solscan.io/tx/${signature}?cluster=mainnet-beta`,
      tokenMint: mintKeypair.publicKey,
      tokenId,
    };
  } catch (error) {
    console.error('Create token error:', error);
    throw error;
  }
};

export const ExplanationHeader = () => {
  return (
    <Column noMargin>
      <Line>
        <Text align="center">
          <Trans>
            This will export and deploy your game to Sonic Cloud and your token
            to pump.fun.
          </Trans>
        </Text>
      </Line>
    </Column>
  );
};

export const DoneFooter = ({
  renderGameButton,
  gameLink,
  project,
  onGameDeployed,
}) => {
  const [gameName, setGameName] = useState('');
  const [gameDescription, setGameDescription] = useState('');
  const [gameThumbnail, setGameThumbnail] = useState(null);
  const [symbol, setSymbol] = useState('');
  const [website, setWebsite] = useState('');
  const [twitter, setTwitter] = useState('');
  const [telegram, setTelegram] = useState('');
  const [devSupply, setDevSupply] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [txSignature, setTxSignature] = useState(null);
  const [isCheckingBalance, setIsCheckingBalance] = useState(false);
  const [publishFeeUSD, setPublishFeeUSD] = useState(null);
  const [insufficientFunds, setInsufficientFunds] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const wallet = useWallet();
  const { fetchProfile, profile } = useWalletUser();

  // Check balance against admin settings
  useEffect(
    () => {
      const checkBalanceAndSettings = async () => {
        if (!wallet.connected) {
          setError('Please connect your wallet first');
          return;
        }

        setIsCheckingBalance(true);
        try {
          // Fetch admin settings to get the publish fee
          const settings = await getAdminSettings();
          if (settings && settings.publishFeeUSD) {
            setPublishFeeUSD(settings.publishFeeUSD);
          } else {
            // Default fee if not found in settings
            setPublishFeeUSD(DEFAULT_USD_FEE);
          }

          // Check if user has sufficient funds in their profile balance
          if (
            profile &&
            profile.balance !== undefined &&
            settings &&
            settings.publishFeeUSD
          ) {
            const userBalance = profile.balance;
            const requiredBalance = settings.publishFeeUSD;

            if (userBalance < requiredBalance) {
              setInsufficientFunds(true);
              setError(
                `Insufficient balance. You need at least ${requiredBalance.toFixed(
                  2
                )} credits but have ${userBalance.toFixed(2)} credits`
              );
            } else {
              setInsufficientFunds(false);
              setError(null);
            }
          }
        } catch (err) {
          console.error('Error checking balance or admin settings:', err);
          setError('Failed to check balance or publishing fee');
        } finally {
          setIsCheckingBalance(false);
        }
      };

      checkBalanceAndSettings();
    },
    [wallet.connected, profile]
  );

  const handleThumbnailChange = event => {
    if (event.target.files && event.target.files[0]) {
      setGameThumbnail(event.target.files[0]);
    }
  };
  const gameId = project ? project.getProjectUuid() : null;

  // Import the uploadGameDetails function from GamePublisher at the top of the file
  const uploadGameDetails = async () => {
    setError(null);
    setLoading(true);
    console.log(
      {
        cc: wallet.connected,
        gameName,
        gameDescription,
        gameThumbnail,
        gameLink,
      },
      'tolllll'
    );
    if (!wallet.connected) {
      setError('Please connect your wallet first');
      setLoading(false);
      return false;
    }
    if (!gameId) {
      setError('This game is not saved yet');
      console.log('game id not found', { gameId, renderGameButton });
      setLoading(false);
      return false;
    }

    if (!gameName || !gameDescription || !gameThumbnail) {
      setError('Please complete all fields');
      setLoading(false);
      return false;
    }

    try {
      // Use the imported function from GamePublisher to ensure consistent behavior
      const result = await uploadGameToServer({
        gameName,
        gameDescription,
        gameThumbnail,
        gameLink,
        gameId,
        setError,
      });
      await fetchProfile();

      // Refresh the games list to show the newly published game
      // We'll use a direct API call to refresh the games list since we can't use hooks here
      try {
        if (isWalletAuthenticated() && wallet.publicKey) {
          const authHeader = getWalletAuthorizationHeader();
          if (authHeader) {
            // This will trigger a refresh of the games list in the UI when the user navigates back to the dashboard
            await axios.get(
              `${GDevelopProjectApi.baseUrl}/getUserPublishedGames`,
              {
                headers: {
                  Authorization: authHeader,
                },
              }
            );
            console.log('Refreshed games list after publishing');
          }
        }
      } catch (error) {
        console.error('Error refreshing games list:', error);
      }

      return result;
    } catch (error) {
      if (
        error.response &&
        (error.response.status === 400 || error.response.status === 500)
      ) {
        setError('Please check your input fields, ensure they are correct');
      } else {
        setError(error.message);
      }
      console.error(error);
      setLoading(false);
      return false;
    }
  };

  const deployGameAndToken = async () => {
    if (!wallet.connected) {
      setError('Please connect your wallet first');
      return;
    }

    // Check balance again before proceeding
    if (insufficientFunds) {
      setError(
        `Insufficient balance. You need at least ${publishFeeUSD?.toFixed(
          2
        )} credits but have ${profile?.balance?.toFixed(
          2
        )} credits to publish your game.`
      );
      return;
    }

    if (
      !gameName ||
      !gameDescription ||
      !gameThumbnail ||
      !devSupply ||
      !symbol
    ) {
      setError(
        'Please complete all required fields (Name, Description, Thumbnail, Symbol, Dev Supply)'
      );
      return;
    }
    
    // Show the confirmation modal instead of proceeding directly
    setShowConfirmationModal(true);
  };
  
  const handleConfirmDeploy = async () => {
    // Close the modal
    setShowConfirmationModal(false);
    
    setError(null);
    setLoading(true);

    const tokenData = {
      name: gameName,
      description: gameDescription,
      symbol: symbol,
      website: website || '',
      twitter: twitter || '',
      telegram: telegram || '',
      devSupply: parseInt(devSupply) || 1000,
    };

    try {
      await uploadGameDetails();

      const tokenResult = await deployTokenAndCreateToken(
        tokenData,
        wallet.publicKey.toString(),
        gameId,
        wallet,
        setError,
        setLoading,
        gameThumbnail, // Pass the image file
        setSuccess
      );
      if (!tokenResult) {
        throw new Error('Token creation failed');
      }

      setLoading(false);
      setSuccess(true);
      setTxSignature(tokenResult.createTransactionLink);

      // Call the refresh callback if provided
      if (onGameDeployed) {
        try {
          await onGameDeployed();
        } catch (error) {
          console.error('Error calling onGameDeployed callback:', error);
        }
      }
    } catch (error) {
      setError(error.message || 'Failed to deploy game and token');
      console.error(error);
      setLoading(false);
    }
  };

  // Show wallet connection error first if not connected
  if (!wallet.connected) {
    return (
      <Column noMargin alignItems="center">
        <AlertMessage kind="error">
          <Trans>Please connect your wallet to continue</Trans>
        </AlertMessage>
        <Spacer />
        <Text>
          <Trans>
            You need to connect your wallet to deploy your game and create a
            token.
          </Trans>
        </Text>
      </Column>
    );
  }

  // Show balance checking state
  if (isCheckingBalance) {
    return (
      <Column noMargin alignItems="center">
        <Text>
          <Trans>Checking wallet balance...</Trans>
        </Text>
      </Column>
    );
  }

  return !success ? (
    <Column noMargin alignItems="center">
      <Text>
        Please complete this form to deploy your game and token.
      </Text>
      {profile.balance !== null && (
        <Line justifyContent="center" style={{ marginTop: 10 }}>
          <Text>
            Wallet Balance: {profile.balance.toFixed(4)} USD
          </Text>
        </Line>
      )}
      <Spacer />
      <Text>
        <Trans>Game Name</Trans>
      </Text>
      <TextField
        placeholder={<Trans>Game Name</Trans>}
        onChange={(_, value) => setGameName(value)}
        style={{ width: 300 }}
        value={gameName}
      />
      <Spacer />
      <Text>
        <Trans>Game Description</Trans>
      </Text>
      <TextField
        placeholder={<Trans>Game Description</Trans>}
        onChange={(_, value) => setGameDescription(value)}
        value={gameDescription}
        style={{ width: 300 }}
        multiline
      />
      <Spacer />
      <Text>
        <Trans>Game Thumbnail (image)</Trans>
      </Text>
      <input type="file" accept="image/*" onChange={handleThumbnailChange} />
      <Spacer />
      <Text>
        <Trans>Token Symbol</Trans>
      </Text>
      <TextField
        placeholder={<Trans>Token Symbol (e.g., MYTK)</Trans>}
        onChange={(_, value) => setSymbol(value)}
        style={{ width: 300 }}
        value={symbol}
      />
      <Spacer />
      <Text>
        <Trans>Website (optional)</Trans>
      </Text>
      <TextField
        placeholder={<Trans>Website URL</Trans>}
        onChange={(_, value) => setWebsite(value)}
        style={{ width: 300 }}
        value={website}
      />
      <Spacer />
      <Text>
        <Trans>Twitter (optional)</Trans>
      </Text>
      <TextField
        placeholder={<Trans>Twitter Handle or URL</Trans>}
        onChange={(_, value) => setTwitter(value)}
        style={{ width: 300 }}
        value={twitter}
      />
      <Spacer />
      <Text>
        <Trans>Telegram (optional)</Trans>
      </Text>
      <TextField
        placeholder={<Trans>Telegram URL</Trans>}
        onChange={(_, value) => setTelegram(value)}
        style={{ width: 300 }}
        value={telegram}
      />
      <Spacer />
      <Text>
        <Trans>Developer Supply</Trans>
      </Text>
      <TextField
        placeholder={<Trans>Developer Supply (e.g., 1000)</Trans>}
        onChange={(_, value) => setDevSupply(value)}
        style={{ width: 300 }}
        value={devSupply}
        type="number"
      />
      <Spacer />
      {error && <AlertMessage kind="error">{error}</AlertMessage>}
      <Spacer />
      <Line justifyContent="center">
        <RaisedButton
          label={
            isCheckingBalance ? (
              <Trans>Checking Balance...</Trans>
            ) : !loading ? (
              <Trans>Build game</Trans>
            ) : (
              <Trans>Building...</Trans>
            )
          }
          primary
          id={`token-launch-export-button`}
          onClick={deployGameAndToken}
          disabled={
            loading ||
            isCheckingBalance ||
            insufficientFunds ||
            !gameName ||
            !gameDescription ||
            !gameThumbnail ||
            !devSupply ||
            !symbol
          }
        />
      </Line>
      
      {/* Balance Confirmation Modal */}
      <PublishFeeConfirmationModal
        open={showConfirmationModal}
        onClose={() => setShowConfirmationModal(false)}
        onConfirm={handleConfirmDeploy}
        currentBalance={profile?.balance || 0}
        publishFee={publishFeeUSD || 0}
        insufficientFunds={insufficientFunds}
      />
    </Column>
  ) : (
    <Column>
      <Text size="sub-title" style={{ textAlign: 'center', marginTop: 10 }}>
        <Trans>
          Congratulations! Your game has been successfully published and is now
          available in the Play tab.
        </Trans>
      </Text>
      {txSignature && (
        <Line justifyContent="center" style={{ marginTop: 10 }}>
          <Link
            href={txSignature}
            onClick={() =>
              window.open(txSignature, '_blank')
            }
          >
            <Text>
              <Trans>View Transaction</Trans>
            </Text>
          </Link>
        </Line>
      )}
    </Column>
  );
};

export const ExportFlow = ({
  disabled,
  launchExport,
  isExporting,
  exportPipelineName,
  exportStep,
  project,
  onSaveProject,
  isSavingProject,
  gameAndBuildsManager,
  onGameDeployed,
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [isCheckingBalance, setIsCheckingBalance] = useState(false);
  const [publishFeeUSD, setPublishFeeUSD] = useState(null);
  const [insufficientFunds, setInsufficientFunds] = useState(false);
  const [error, setError] = useState(null);
  const { profile } = useWalletUser();

  // Check balance against admin settings
  useEffect(
    () => {
      const checkBalanceAndSettings = async () => {
        setIsCheckingBalance(true);
        try {
          // Fetch admin settings to get the publish fee
          const settings = await getAdminSettings();
          if (settings && settings.publishFeeUSD) {
            setPublishFeeUSD(settings.publishFeeUSD);
          } else {
            // Default fee if not found in settings
            setPublishFeeUSD(0.01);
          }

          // Check if user has sufficient funds in their profile balance
          if (
            profile &&
            profile.balance !== undefined &&
            settings &&
            settings.publishFeeUSD
          ) {
            const userBalance = profile.balance;
            const requiredBalance = settings.publishFeeUSD;

            if (userBalance < requiredBalance) {
              setInsufficientFunds(true);
              setError(
                `Insufficient balance. You need at least ${requiredBalance.toFixed(
                  2
                )} credits but have ${userBalance.toFixed(2)} credits`
              );
            } else {
              setInsufficientFunds(false);
              setError(null);
            }
          }
        } catch (err) {
          console.error('Error checking balance or admin settings:', err);
          setError('Failed to check balance or publishing fee');
        } finally {
          setIsCheckingBalance(false);
        }
      };

      checkBalanceAndSettings();
    },
    [profile]
  );

  const handleExport = async () => {
    // Check balance before proceeding
    if (insufficientFunds) {
      alert(
        `Insufficient balance. You need at least ${publishFeeUSD?.toFixed(
          2
        )} credits but have ${profile?.balance?.toFixed(
          2
        )} credits to publish your game.`
      );
      return;
    }
    
    // Check if the project needs to be saved first
    if (project) {
      setIsSaving(true);
      try {
        // If gameAndBuildsManager is available, we can use it to save directly to the cloud
        if (gameAndBuildsManager && gameAndBuildsManager.saveProjectToCloud) {
          await gameAndBuildsManager.saveProjectToCloud();
        } else if (onSaveProject) {
          // Fallback to regular save if direct cloud save is not available
          await onSaveProject();
        }
      } catch (error) {
        console.error('Error saving project before export:', error);
      } finally {
        setIsSaving(false);
      }
    }

    // Launch the export after saving (or if saving failed)
    launchExport();
  };

  return exportStep !== 'done' ? (
    <Column>
      {error && <AlertMessage kind="error">{error}</AlertMessage>}
      <Line justifyContent="center">
        <RaisedButton
          label={
            isCheckingBalance ? (
              <Trans>Checking Balance...</Trans>
            ) : isSaving ? (
              <Trans>Saving...</Trans>
            ) : !isExporting ? (
              <Trans>Build game</Trans>
            ) : (
              <Trans>Building...</Trans>
            )
          }
          primary
          id={`launch-export-${exportPipelineName}-button`}
          onClick={handleExport}
          disabled={
            disabled ||
            isExporting ||
            isSaving ||
            isCheckingBalance ||
            insufficientFunds ||
            isSavingProject
          }
        />
      </Line>
    </Column>
  ) : null;
};

export const html5Exporter = {
  key: 'publishgame',
  tabName: <Trans>Deploy Game and Token</Trans>,
  name: <Trans>Deploy your Game to Cloud and create token on Pump Fun</Trans>,
  helpPage: '/publishing/Publish_html5_game_in_a_local_folder',
};

export const publishGameOnlyExporter = {
  key: 'publishgame-only',
  tabName: <Trans>Publish Game</Trans>,
  name: <Trans>Deploy your Game to Cloud without creating a token</Trans>,
  helpPage: '/publishing/Publish_html5_game_in_a_local_folder',
};
