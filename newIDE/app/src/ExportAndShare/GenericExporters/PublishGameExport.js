// @flow
import { Trans } from '@lingui/macro';
import React, { useState, useEffect } from 'react';
import Text from '../../UI/Text';
import { Column, Line } from '../../UI/Grid';
import RaisedButton from '../../UI/RaisedButton';
import AlertMessage from '../../UI/AlertMessage';
import {
  PublishGameForm,
  PublishGameExplanationHeader,
} from '../ShareDialog/GamePublisher';
import { type ExportFlowProps } from '../ExportPipeline.flow';
import { useWalletUser } from '../../Utils/store/WalletUserContext';
import { getAdminSettings } from '../../Utils/GDevelopServices/User';
import {
  isWalletAuthenticated,
  getWalletAuthorizationHeader,
} from '../../Utils/GDevelopServices/WalletAuthentication';
import { GDevelopProjectApi } from '../../Utils/GDevelopServices/ApiConfigs';
import axios from 'axios';
import { useWallet } from '@solana/wallet-adapter-react';
import PublishFeeConfirmationModal from '../../UI/PublishFeeConfirmationModal';

export const ExplanationHeader = () => {
  return <PublishGameExplanationHeader />;
};

type HTML5ExportFlowProps = {|
  ...ExportFlowProps,
  exportPipelineName: string,
|};

export const ExportFlow = ({
  disabled,
  launchExport,
  isExporting,
  exportPipelineName,
  exportStep,
  project,
  onSaveProject,
  isSavingProject,
  gameAndBuildsManager,
  onGameDeployed,
}) => {
  const [isSaving, setIsSaving] = useState(false);
  const [isCheckingBalance, setIsCheckingBalance] = useState(false);
  const [publishFeeUSD, setPublishFeeUSD] = useState(null);
  const [insufficientFunds, setInsufficientFunds] = useState(false);
  const [error, setError] = useState(null);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const { profile } = useWalletUser();

  // Check balance against admin settings
  useEffect(
    () => {
      const checkBalanceAndSettings = async () => {
        setIsCheckingBalance(true);
        try {
          // Fetch admin settings to get the publish fee
          const settings = await getAdminSettings();
          if (settings && settings.publishFeeUSD) {
            setPublishFeeUSD(settings.publishFeeUSD);
          } else {
            // Default fee if not found in settings
            setPublishFeeUSD(0.01);
          }

          // Check if user has sufficient funds in their profile balance
          if (
            profile &&
            profile.balance !== undefined &&
            settings &&
            settings.publishFeeUSD
          ) {
            const userBalance = profile.balance;
            const requiredBalance = settings.publishFeeUSD;

            if (userBalance < requiredBalance) {
              setInsufficientFunds(true);
              setError(
                `Insufficient balance. You need at least ${requiredBalance.toFixed(
                  2
                )} credits but have ${userBalance.toFixed(2)} credits`
              );
            } else {
              setInsufficientFunds(false);
              setError(null);
            }
          }
        } catch (err) {
          console.error('Error checking balance or admin settings:', err);
          setError('Failed to check balance or publishing fee');
        } finally {
          setIsCheckingBalance(false);
        }
      };

      checkBalanceAndSettings();
    },
    [profile]
  );

  const handleExport = async () => {
    // Check balance before proceeding
    if (insufficientFunds) {
      alert(
        `Insufficient balance. You need at least ${publishFeeUSD?.toFixed(
          2
        )} credits but have ${profile?.balance?.toFixed(
          2
        )} credits to publish your game.`
      );
      return;
    }
    // Check if the project needs to be saved first
    if (project) {
      setIsSaving(true);
      try {
        // If gameAndBuildsManager is available, we can use it to save directly to the cloud
        if (gameAndBuildsManager && gameAndBuildsManager.saveProjectToCloud) {
          await gameAndBuildsManager.saveProjectToCloud();
        } else if (onSaveProject) {
          // Fallback to regular save if direct cloud save is not available
          await onSaveProject();
        }
      } catch (error) {
        console.error('Error saving project before export:', error);
      } finally {
        setIsSaving(false);
      }
    }

    // Launch the export after saving (or if saving failed)
    launchExport();
  };

  return exportStep !== 'done' ? (
    <Column>
      {error && <AlertMessage kind="error">{error}</AlertMessage>}
      <Line justifyContent="center">
        <RaisedButton
          label={
            isCheckingBalance ? (
              <Trans>Checking Balance...</Trans>
            ) : isSaving ? (
              <Trans>Saving...</Trans>
            ) : !isExporting ? (
              <Trans>Build game</Trans>
            ) : (
              <Trans>Building...</Trans>
            )
          }
          primary
          id={`launch-export-${exportPipelineName}-button`}
          onClick={handleExport}
          disabled={
            disabled ||
            isExporting ||
            isSaving ||
            isCheckingBalance ||
            insufficientFunds ||
            isSavingProject
          }
        />
      </Line>

     
    </Column>
  ) : null;
};

export const DoneFooter = ({ gameLink, project, onGameDeployed }) => {
  // Get the game UUID from the project to link the published game with the saved game
  const gameId = project ? project.getProjectUuid() : null;
  const wallet = useWallet();

  // Define a callback to refresh the games list after successful publication
  const onPublishSuccess = async () => {
    try {
      // Use the provided callback if available, otherwise fall back to direct API call
      if (onGameDeployed) {
        await onGameDeployed();
        console.log('Games list refreshed after publishing via callback');
      } else {
        // Fallback to direct API call
        if (isWalletAuthenticated() && wallet.publicKey) {
          const authHeader = getWalletAuthorizationHeader();
          if (authHeader) {
            await axios.get(
              `${GDevelopProjectApi.baseUrl}/getUserPublishedGames`,
              {
                headers: {
                  Authorization: authHeader,
                },
              }
            );
            console.log(
              'Refreshed games list after publishing via direct API call'
            );
          }
        }
      }
    } catch (error) {
      console.error('Error refreshing games list:', error);
    }
  };

  return (
    <PublishGameForm
      gameLink={gameLink}
      gameId={gameId}
      onSuccess={onPublishSuccess}
    />
  );
};

export const publishGameOnlyExporter = {
  key: 'publishgame-only',
  tabName: <Trans>Publish Game</Trans>,
  name: <Trans>Deploy your Game to Cloud without creating a token</Trans>,
  helpPage: '/publishing/Publish_html5_game_in_a_local_folder',
};
