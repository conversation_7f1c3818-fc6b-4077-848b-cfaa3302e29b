// @flow
import axios from 'axios';
import { GDevelopTo, GDevelopWalletAuthApi } from './ApiConfigs';

/**
 * Request a nonce from the server to be used for wallet authentication
 */
export const requestNonce = async (walletAddress) => {
  try {
    console.log('Requesting nonce for address:', walletAddress);
    const response = await axios.post(
      `${GDevelopWalletAuthApi.baseUrl}/nonce`,
      { walletAddress }
    );
    console.log('Nonce response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error requesting nonce:', error);
    return {
      walletAddress,
      nonce: 'mock-nonce-123456',
      message: 'Sign this message to authenticate with <PERSON>D<PERSON>lop',
    };
  }
};

/**
 * Verify a signature and get a JWT token
 */
export const verifySignature = async ({
  walletAddress,
  signature,
  nonce,
}) => {
  try {
    console.log('Verifying signature:', { walletAddress, signature, nonce });
    const response = await axios.post(
      `${GDevelopWalletAuthApi.baseUrl}/verify`,
      { walletAddress, signature, nonce }
    );
    console.log('Verify response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error verifying signature:', error);
    return {
      walletAddress,
      token: null,
    };
  }
};

/**
 * Store the wallet auth token in localStorage
 */
export const storeAuthToken = (token) => {
  localStorage.setItem('wallet_auth_token', token);
  console.log('Stored auth token:', token);
};

/**
 * Retrieve the wallet auth token from localStorage
 */
export const getAuthToken = () => {
  const token = localStorage.getItem('wallet_auth_token');
  console.log('Retrieved auth token:', token ? token.slice(0, 10) + '...' : null);
  return token;
};

/**
 * Remove the wallet auth token from localStorage
 */
export const removeAuthToken = () => {
  localStorage.removeItem('wallet_auth_token');
  console.log('Removed auth token');
};

/**
 * Check if the user is authenticated with a wallet
 */
export const isWalletAuthenticated = () => {
  const isAuth = !!getAuthToken();
  return isAuth;
};

/**
 * Get an authorization header with the token
 */
export const getWalletAuthorizationHeader = () => {
  // Always fetch the latest token from localStorage
  const token = localStorage.getItem('wallet_auth_token');
  console.log('Creating auth header with token:', token ? 'exists' : 'missing');
  return token ? `Bearer ${token}` : null;
};

/**
 * Check if the current authentication token is valid
 * @returns {Promise<boolean>} True if the token is valid, false otherwise
 */
export const validateToken = async () => {
  try {
    const token = localStorage.getItem('wallet_auth_token');
    if (!token) return false;

    // Replace with your actual validation endpoint
    const response = await fetch('/api/auth/validate', {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (response.status === 401) {
      removeAuthToken();
      return false;
    }

    return response.ok;
  } catch (error) {
    console.error('Error validating token:', error);
    removeAuthToken();
    return false;
  }
}; 