import { removeAuthToken } from './WalletAuthentication';

/**
 * A wrapper around fetch that automatically handles authentication and token expiration
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} The fetch response
 */
import axios from 'axios';

export const authenticatedFetch = async (url, options = {}) => {
  // Get the latest token for each request
  const token = localStorage.getItem('wallet_auth_token');
  
  // Set up headers
  const headers = {
    'Content-Type': 'application/json',
    ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
    ...(options.headers || {}),
  };

  try {
    const response = await axios({
      url,
      method: options.method || 'GET',
      headers,
      data: options.body || options.data,
      params: options.params,
      ...options, // Spread any other axios-specific options
    });

    // Axios wraps the response in a data property
    return {
      ...response,
      json: async () => response.data,
      text: async () => JSON.stringify(response.data),
      ok: true,
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    };
  } catch (error) {
    // Handle 401 Unauthorized
    if (error.response && error.response.status === 401) {
      console.log('Authentication failed, logging out...');
      removeAuthToken();
      window.location.href = '/login'; // Adjust the login route as needed
      throw new Error('Authentication failed');
    }
    
    console.error('API request failed:', error);
    throw error;
  }
};

/**
 * Helper for making GET requests
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} The fetch response
 */
export const get = (url, options = {}) => {
  return authenticatedFetch(url, {
    ...options,
    method: 'GET',
  });
};

/**
 * Helper for making POST requests
 * @param {string} url - The URL to fetch
 * @param {Object} body - The request body
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} The fetch response
 */
export const post = (url, body, options = {}) => {
  return authenticatedFetch(url, {
    ...options,
    method: 'POST',
    body: JSON.stringify(body),
  });
};

/**
 * Helper for making PUT requests
 * @param {string} url - The URL to fetch
 * @param {Object} body - The request body
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} The fetch response
 */
export const put = (url, body, options = {}) => {
  return authenticatedFetch(url, {
    ...options,
    method: 'PUT',
    body: JSON.stringify(body),
  });
};

/**
 * Helper for making DELETE requests
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} The fetch response
 */
export const del = (url, options = {}) => {
  return authenticatedFetch(url, {
    ...options,
    method: 'DELETE',
  });
};

/**
 * Helper for making PATCH requests
 * @param {string} url - The URL to fetch
 * @param {Object} options - Fetch options
 * @returns {Promise<Response>} The fetch response
 */
export const patch = (url, options = {}) => {
  return authenticatedFetch(url, {
    ...options,
    method: 'PATCH',
  });
};
