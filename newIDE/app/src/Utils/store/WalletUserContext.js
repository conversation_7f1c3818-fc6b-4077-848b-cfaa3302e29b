// @flow
import * as React from 'react';
import { isWalletAuthenticated } from '../GDevelopServices/WalletAuthentication';
import { useWallet } from './SolanaWalletProvider';
import { get, patch } from '../GDevelopServices/ApiClient';
import { getWalletAuthToken } from '../GDevelopServices/WalletAuthentication';
import { GDevelopProfileApi } from '../GDevelopServices/ApiConfigs'; // Import base URL

const initialContext = {
  profile: null,
  loading: false,
  error: null,
  fetchProfile: async () => {},
  updateProfile: async () => {},
};

export const WalletUserContext = React.createContext(initialContext);

const WalletUserProvider = ({ children }) => {
  const [profile, setProfile] = React.useState(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState(null);
  const wallet = useWallet();
  const { connected } = wallet;

  const fetchProfile = React.useCallback(
    async () => {
      // Only fetch if wallet is connected and authenticated
      console.log('fetching this sheee', {
        connected,
        isWalletAuthenticated: isWalletAuthenticated(),
      });
      if (!connected || !isWalletAuthenticated()) return;

      setLoading(true);
      setError(null);

      try {
        // Use the API client to fetch the profile
        const response = await get(`${GDevelopProfileApi.baseUrl}/profile`);
        const data = await response.json();
        if (data) {
          setProfile(data);
        } else {
          setError(new Error('Failed to fetch profile data'));
        }
      } catch (err) {
        setError(err);
        console.error('Error fetching wallet user profile:', err);
      } finally {
        setLoading(false);
      }
    },
    [connected]
  );

  const updateProfile = React.useCallback(
    async data => {
      if (!connected || !isWalletAuthenticated()) return;

      setLoading(true);
      setError(null);

      try {
        // Use the API client to update the profile
        const response = await patch(`${GDevelopProfileApi.baseUrl}/profile`, data);
        const responseData = await response.json();
        if (responseData) {
          setProfile(responseData);
        } else {
          setError(new Error('Failed to update profile data'));
        }
      } catch (err) {
        setError(err);
        console.error('Error updating wallet user profile:', err);
      } finally {
        setLoading(false);
      }
    },
    [connected]
  );
  const isAuthenticated = isWalletAuthenticated();
  // Fetch profile when wallet is connected and authenticated
  React.useEffect(
    () => {
      console.log('getting profile', {
        connected,
        isAuthenticated,
        profile,
      });
      if (connected && isWalletAuthenticated()) {
        fetchProfile();
      } else {
        // Reset profile when wallet is disconnected
        setProfile(null);
      }
    },
    [connected, fetchProfile, isAuthenticated]
  );
  // Debug effect for profile state changes
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Profile state:', { connected, isAuthenticated, profile });
    }
  }, [connected, isAuthenticated, profile]);

  // Effect to handle initial profile fetch on mount and auth state changes
  React.useEffect(() => {
    if (connected && isWalletAuthenticated()) {
      fetchProfile();
    } else {
      // Reset profile when wallet is disconnected or not authenticated
      setProfile(null);
    }
  }, [connected, fetchProfile]);

  const contextValue = React.useMemo(
    () => ({
      profile,
      loading,
      error,
      fetchProfile,
      updateProfile,
    }),
    [profile, loading, error, fetchProfile, updateProfile]
  );

  return (
    <WalletUserContext.Provider value={contextValue}>
      {children}
    </WalletUserContext.Provider>
  );
};

export { WalletUserProvider };
export const useWalletUser = () => React.useContext(WalletUserContext);
