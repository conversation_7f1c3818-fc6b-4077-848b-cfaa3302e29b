import React from 'react';

const TokenHeader = ({ data, liveData, imageUrl }) => {
  // Format small prices
  const formatPrice = price => {
    if (typeof price !== 'number' || price === 0) return 'Not Available';
    if (price < 0.01) {
      return `$${price.toFixed(8)}`;
    }
    return `$${price.toFixed(2)}`;
  };
  // Format large numbers
  const formatLargeNumber = number => {
    if (typeof number !== 'number' || number === 0) return 'Not Available';
    if (number >= 1_000_000) {
      return `$${(number / 1_000_000).toFixed(2)}M`;
    } else if (number >= 1_000) {
      return `$${(number / 1_000).toFixed(2)}K`;
    }
    return `$${number.toFixed(2)}`;
  };

  return (
    <div className="token-header underline">
      <div className="token-icon-wrapper">
        <div className="token-icon">
          {imageUrl ? (
            <img
              src={imageUrl}
              alt={data?.name || ''}
              className="token-image"
            />
          ) : null}
        </div>
      </div>
      <div className="token-info">
        <h2 className="token-name">
          {data?.name || 'Unknown'} {data?.symbol || ''}
        </h2>
        <p className="token-subtext">
          Market cap: {formatLargeNumber(liveData?.marketCap)}
        </p>
        <p className="token-subtext">Price: {formatPrice(liveData?.price)}</p>
        <p className="token-subtext">
          Change:{' '}
          {typeof liveData?.change === 'number' ? (
            <span style={{ color: liveData.change >= 0 ? 'green' : 'red' }}>
              {liveData.change >= 0 ? '+' : ''}
              {liveData.change.toFixed(2)}%
            </span>
          ) : (
            'N/A'
          )}
        </p>
      </div>
    </div>
  );
};

export default TokenHeader;
