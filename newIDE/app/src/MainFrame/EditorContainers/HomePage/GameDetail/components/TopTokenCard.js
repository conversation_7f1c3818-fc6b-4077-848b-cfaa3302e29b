import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Copy from '../../../../../UI/CustomSvgIcons/Copy';
import Star from '../../../../../UI/CustomSvgIcons/Star';
import { ProgressBar } from './ProgressBar';
import { getWalletAuthorizationHeader } from '../../../../../Utils/GDevelopServices/WalletAuthentication';
import { GDevelopProjectApi } from '../../../../../Utils/GDevelopServices/ApiConfigs';

export const getLocalImageUrl = imageUrl => {
  if (!imageUrl) return null;

  // Check if the URL already has http:// or https://
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }

  // If it's just a path like "folder/file.jpg", prepend base URL
  return `https://engine-be.sonicengine.net${imageUrl}`;
};

export const TopTokenCard = ({
  token,
  isHighlighted,
  GameUrl,
  onFavoriteToggle,
}) => {
  const [isFavorite, setIsFavorite] = useState(token?.isFavouriteGame || false);
  const [showCopied, setShowCopied] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Initialize isFavorite from token prop whenever it changes
  useEffect(
    () => {
      console.log('Token favorite status:', token?.isFavouriteGame);
      setIsFavorite(!!token?.isFavouriteGame); // Convert to boolean with !!
    },
    [token?.isFavouriteGame]
  );

  // Fetch image from IPFS metadata
  useEffect(
    () => {
      const fetchIPFSImage = async () => {
        if (!token?.ipfsMetadataUri) {
          setImageUrl(getLocalImageUrl(token?.imageUrl));
          setImageLoading(false);
          return;
        }

        try {
          setImageLoading(true);
          const response = await fetch(token.ipfsMetadataUri);

          if (response.ok) {
            const metadata = await response.json();
            if (metadata.image) {
              setImageUrl(metadata.image);
            } else {
              // Fallback to local image
              setImageUrl(getLocalImageUrl(token?.imageUrl));
            }
          } else {
            // Fallback to local image
            setImageUrl(getLocalImageUrl(token?.imageUrl));
          }
        } catch (error) {
          console.error('Error fetching IPFS metadata:', error);
          // Fallback to local image
          setImageUrl(getLocalImageUrl(token?.imageUrl));
        } finally {
          setImageLoading(false);
        }
      };

      fetchIPFSImage();
    },
    [token?.ipfsMetadataUri, token?.imageUrl]
  );

  const toggleFavorite = async e => {
    e.stopPropagation(); // Prevent card click event from firing

    if (!token?.gameId || isUpdating) return;

    try {
      setIsUpdating(true);
      const newFavoriteState = !isFavorite;
      console.log(
        `Toggling favorite for ${token.gameId} to ${newFavoriteState}`
      );

      // Get authorization header
      const authHeader = getWalletAuthorizationHeader();
      if (!authHeader) {
        console.error('No authorization token available');
        return;
      }

      // Make API call to update favorite status
      const response = await axios.patch(
        `${GDevelopProjectApi.baseUrl}/game/${token.gameId}/favourite`,
        { isFavourite: newFavoriteState },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: authHeader,
          },
        }
      );

      if (response.status === 200) {
        console.log('Favorite status updated successfully');
        setIsFavorite(newFavoriteState);

        // Call the callback if provided to update parent component
        if (onFavoriteToggle) {
          onFavoriteToggle(token.gameId, newFavoriteState);
        }
      } else {
        console.error('Failed to update favorite status:', response);
      }
    } catch (error) {
      console.error('Error toggling favorite status:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCopy = () => {
    navigator.clipboard.writeText(
      'https://pump.fun/coin/' + token?.mintPublicKey
    );
    setShowCopied(true);
    setTimeout(() => setShowCopied(false), 2000);
  };

  // Helper function to handle local image URL
 

  // Generate placeholder with first letter
  const generatePlaceholder = () => {
    const firstLetter = token?.name?.charAt(0)?.toUpperCase() || '?';
    const colors = [
      '#FF6B6B',
      '#4ECDC4',
      '#45B7D1',
      '#96CEB4',
      '#FFEAA7',
      '#DDA0DD',
      '#98D8C8',
      '#F7DC6F',
      '#BB8FCE',
      '#85C1E9',
    ];
    const colorIndex = token?.name?.charCodeAt(0) % colors.length || 0;
    const backgroundColor = colors[colorIndex];

    return (
      <div
        style={{
          width: '100%',
          height: '100%',
          backgroundColor,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '2rem',
          fontWeight: 'bold',
          color: 'white',
          borderRadius: '8px',
        }}
      >
        {firstLetter}
      </div>
    );
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const renderImage = () => {
    console.log('imageLoading', { imageLoading, imageUrl, imageError });
    if (imageLoading) {
      return (
        <div
          style={{
            width: '100%',
            height: '100%',
            backgroundColor: '#f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderRadius: '8px',
          }}
        >
          <div
            style={{
              width: '20px',
              height: '20px',
              border: '2px solid #ddd',
              borderTop: '2px solid #666',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
            }}
          />
        </div>
      );
    }

    if (imageError || !imageUrl) {
      return generatePlaceholder();
    }

    return (
      <img
        src={imageUrl}
        alt={token?.name}
        onError={handleImageError}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          borderRadius: '8px',
        }}
      />
    );
  };

  return (
    <>
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
      <div
        onClick={() => {
          if (token?.mintPublicKey) {
            window.dispatchEvent(
              new CustomEvent('navigate-to-preview-game', {
                detail: { tokenId: { ...token, GameUrl } },
              })
            );

            window.history.pushState({}, '', `/game/${token.gameId}`);
          }
        }}
        className={`top-token-card ${
          isHighlighted ? 'highlighted-token-card' : ''
        }`}
      >
        <div className="top-token-content">
          <div className="top-token-image">{renderImage()}</div>
          <div className="top-token-details">
            <div className="top-token-header">
              <div>
                <h3 className="token-name">{token?.name}</h3>
                <p className="token-symbol">{token?.symbol}</p>
              </div>
              <div className="token-actions">
                <div className="token-status" />
                <button
                  onClick={toggleFavorite}
                  className="favorite-button"
                  disabled={isUpdating}
                >
                  <Star
                    fill={isFavorite ? '#FFD700' : 'none'} // Use gold color for favorites
                    stroke={isFavorite ? '#FFD700' : 'currentColor'} // Match stroke to fill for favorites
                    className={`h-6 w-6 ${isUpdating ? 'opacity-50' : ''}`}
                  />
                </button>
              </div>
            </div>

            <p className="token-description">
              {token?.description?.length > 50
                ? `${token?.description?.slice(0, 47)}...`
                : token?.description}
            </p>

            <div className="token-footer">
              <p
                className="token-marketcap cursor-pointer text-blue-600 underline"
                onClick={e => {
                  e.stopPropagation(); // Prevent card click event from firing
                  if (token?.mintPublicKey) {
                    window.open(
                      `https://pump.fun/coin/${token.mintPublicKey}`,
                      '_blank'
                    );
                  }
                }}
              >
                {token?.mintPublicKey?.slice(0, 3)}...
                {token?.mintPublicKey?.slice(-7)}
              </p>

              <div className="group" style={{ position: 'relative' }}>
                <button
                  onClick={e => {
                    e.stopPropagation(); // Prevent card click event from firing
                    handleCopy();
                  }}
                  className="copy-button"
                >
                  <Copy className="h-5 w-5 text-gray-700 group-hover:text-black" />
                </button>

                <div className="copy-tooltip">
                  {showCopied ? 'Copied!' : 'Copy'}
                </div>
              </div>
            </div>

            <div style={{ marginTop: '0.5rem' }}>
              <ProgressBar progress={(token?.pumpPercentage || 0) * 100} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

function formatNumber(num) {
  if (!num) return '0';
  if (num >= 1_000_000_000) return (num / 1_000_000_000).toFixed(1) + 'B';
  if (num >= 1_000_000) return (num / 1_000_000).toFixed(1) + 'M';
  if (num >= 1_000) return (num / 1_000).toFixed(1) + 'K';
  return num?.toString();
}
