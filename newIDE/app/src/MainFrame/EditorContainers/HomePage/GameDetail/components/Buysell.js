import React from 'react';

import TokenHeader from './TokenHeader.js';
import BondingCurve from './BondingCurve.js';
import StatsBoxes from './StatsBoxes.js';
import TokenDetails from './TokenDetails.js';
import TradeInterface from './TradeInterface.js';

const Buysell = ({tokenId,liveData,imageUrl}) => {
  return (
    <div className="token-card marketplace">
      <div className="token-card-content">
  
        <TradeInterface tokenId={tokenId} liveData={liveData} imageUrl={imageUrl}/>
      </div>
    </div>
  );
};

export default Buysell;
