import React, { useEffect, useState } from 'react';

import TokenHeader from './TokenHeader.js';
import BondingCurve from './BondingCurve.js';
import StatsBoxes from './StatsBoxes.js';
import TokenDetails from './TokenDetails.js';
import TradeInterface from './TradeInterface.js';
import { getLocalImageUrl } from './TopTokenCard.js';

const TokenCard = ({ data, liveData, imageUrl }) => {

  // const [liveData, setLiveData] = useState({
  //   price: null,
  //   change: null,
  //   marketCap: null,
  //   liquidity: null,
  //   fdv: null,
  //   totalSupply: null
  // });

  // useEffect(() => {
  //   if (!data?.mintPublicKey) return;

    // const ws = new WebSocket('ws://localhost:8080');
    // const ws = new WebSocket('wss://wsdata.sonicengine.net/');

  //   ws.onopen = () => {
  //     const subMsg = {
  //       op: 'subscribe',
  //       args: [{ address: data.mintPublicKey }],
  //     };
  //     ws.send(JSON.stringify(subMsg));
  //   };

  //   ws.onmessage = (event) => {
  //     try {
  //       const msg = JSON.parse(event.data);
        
    
  //       const tokenData = msg?.data?.tokenData?.data;
  //       console.log('liveData',tokenData);
    
  //       if (msg?.data?.tokenData?.success && tokenData) {
          
  //         setLiveData({
  //           price: tokenData.price ?? null,
  //           change: tokenData.price_change_percent ?? null,
  //           marketCap: tokenData.market_cap ?? null,
  //           liquidity: tokenData.liquidity ?? null,
  //           fdv: tokenData.fdv ?? null,
  //           totalSupply: tokenData.total_supply ?? null,
  //         });
  //       }
  //     } catch (err) {
  //       console.error('WebSocket error parsing message:', err);
  //     }
  //   };
    
  //   ws.onerror = (err) => console.error('WebSocket error:', err);
  //   ws.onclose = () => console.log('WebSocket closed');

  //   return () => ws.close();
  // }, [data?.mintPublicKey]);
 
  console.log('meee', imageUrl)
  return (
    <div className="token-card">
      <div className="token-card-content">
        <TokenHeader data={data} liveData={liveData} imageUrl={imageUrl} />
        {/* <BondingCurve liveData={liveData} /> */}
        <StatsBoxes liveData={liveData} />
        <TokenDetails data={data} liveData={liveData} imageUrl={imageUrl} />
      </div>
    </div>
  );
};

export default TokenCard;