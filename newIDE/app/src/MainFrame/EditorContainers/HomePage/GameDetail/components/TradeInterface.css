.amount-input {
  background: transparent;
  border: none;
  color: inherit;
  font-size: inherit;
  font-weight: inherit;
  text-align: right;
  width: 100%;
  /* width: 80px; */
  outline: none;
}

.amount-input:focus {
  border-bottom: 1px solid #4a90e2;
}

/* Hide spinner buttons on number input */
.amount-input::-webkit-outer-spin-button,
.amount-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* For Firefox */
.amount-input {
  -moz-appearance: textfield;
}

.balance-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  margin-bottom: 16px;
}

.balance-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-label {
  font-size: 14px;
  color: #a0a0a0;
}

.balance-value {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.price-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  margin-bottom: 16px;
}

.price-label {
  font-size: 14px;
  color: #a0a0a0;
}

.price-value {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
}

.receive-info {
  text-align: center;
  padding: 12px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #a0a0a0;
}

.receive-amount {
  font-weight: 600;
  color: #ffffff;
}

.submit-button {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.submit-buy {
  background-color: #4caf50;
  color: white;
}

.submit-sell {
  background-color: #f44336;
  color: white;
}

.submit-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.submit-buy:hover:not(.submit-disabled) {
  background-color: #45a049;
}

.submit-sell:hover:not(.submit-disabled) {
  background-color: #d32f2f;
}

.error-message {
  color: #f44336;
  font-size: 14px;
  margin-bottom: 12px;
  text-align: center;
  background-color: rgba(244, 67, 54, 0.1);
  padding: 8px;
  border-radius: 4px;
}

.token-card.marketplace {
  @media (min-width: 1200px) {
    max-height: 375px;
    overflow-y: scroll;
  }
}
