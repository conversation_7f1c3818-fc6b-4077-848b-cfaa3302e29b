import React from 'react';
import Copy from '../../../../../UI/CustomSvgIcons/Copy';
import OpenLink from '../../../../../UI/CustomSvgIcons/OpenLink';

const TokenDetails = ({ data,  }) => {
  const formatContractAddress = (address) => {
    if (!address) return '';
    return address.slice(0, 5) + '...' + address.slice(-5);
  };

  // Only show contract address
  const details = [
    { 
      label: 'Contract address:', 
      value: data?.mintPublic<PERSON>ey, 
      highlight: true, 
      link: `https://pump.fun/coin/${data?.mintPublicKey}`,
      copy: true
    },
    { label: 'Curve Type:', value: 'Constant Product Curve', highlight: false },
    { label: 'Trade Fee:', value: '1.25%', info: true, highlight: false },
    { label: 'Platform:', value: 'Sonic Engine', link: `https://sonicengine.net/`, highlight: false },
  ];
  
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="token-details">
      {details.map((detail, index) => {
        const displayValue = detail.label === 'Contract address:' && detail.value
          ? formatContractAddress(detail.value)
          : detail.value;
        return (<div key={index} className={`detail-row ${detail.underline ? 'underline' : ''}`}>
          <span className="detail-label">{detail.label}</span>
          <div className="detail-value-container">
            {detail.link ? (
              <a
                href={detail.link}
                target="_blank"
                rel="noopener noreferrer"
                className={`detail-link ${detail.highlight ? 'highlight' : ''}`}
              >
                {displayValue}
                <OpenLink className="openlinkicon" />
              </a>
            ) : (
              <span className={`detail-value ${detail.highlight ? 'highlight' : ''}`}>
                {detail.value}
              </span>
            )}
            {detail.copy && (
              <Copy 
                className="highlight" 
                height={10} 
                width={10} 
                onClick={() => copyToClipboard(detail.value)} 
              />
            )}
          </div>
        </div>
        );
      })}
    </div>
  );
};

export default TokenDetails;