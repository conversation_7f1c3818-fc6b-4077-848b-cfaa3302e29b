import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import React, { useState, useEffect } from 'react';
import * as web3 from '@solana/web3.js';
import bs58 from 'bs58';
import './TradeInterface.css';

const TradeInterface = ({ tokenId, liveData, imageUrl }) => {
  // console.log(liveData, 'liveData')
  const [activeTab, setActiveTab] = useState('buy');
  const [amount, setAmount] = useState(0.5);
  const [slippage, setSlippage] = useState(5);
  const [balance, setBalance] = useState(null);
  const [tokenBalance, setTokenBalance] = useState(null);
  const [solPrice, setSolPrice] = useState(null);
  const [estimatedReceive, setEstimatedReceive] = useState(null);
  const amountOptions = [0.1, 0.25, 0.5, 1, 2, 5];
  const slippageOptions = [1, 2, 3, 4, 5, 10];

  const wallet = useWallet();
  const connection = new web3.Connection(
    'https://mainnet.helius-rpc.com/?api-key=842a4764-1b78-430a-9e60-634143a32713',
    'confirmed'
  );
  const { publicKey, connected } = useWallet();

  // Fetch SOL price
  useEffect(() => {
    const fetchSolPrice = async () => {
      try {
        const response = await fetch(
          'https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd'
        );
        const data = await response.json();
        if (data && data.solana && data.solana.usd) {
          setSolPrice(data.solana.usd);
        }
      } catch (error) {
        console.error('Error fetching SOL price:', error);
      }
    };

    fetchSolPrice();
    // Refresh price every 60 seconds
    // const intervalId = setInterval(fetchSolPrice, 60000);

    // return () => clearInterval(intervalId);
  }, []);

  // Calculate estimated receive amount
  useEffect(
    () => {
      if (solPrice && liveData?.price) {
        if (activeTab === 'buy') {
          // When buying tokens with SOL
          const tokenPrice = liveData.price; // Token price in USD
          const solValue = amount * solPrice; // SOL value in USD
          const estimatedTokens = solValue / tokenPrice;
          setEstimatedReceive({
            amount: estimatedTokens,
            symbol: 'tokens',
          });
        } else {
          // When selling tokens for SOL
          const tokenPrice = liveData.price; // Token price in USD
          const tokenValue = amount * tokenPrice; // Token value in USD
          const estimatedSol = tokenValue / solPrice;
          setEstimatedReceive({
            amount: estimatedSol,
            symbol: 'SOL',
          });
        }
      }
    },
    [activeTab, amount, solPrice, liveData]
  );

  // Fetch SOL balance
  useEffect(() => {
    const getBalance = async () => {
      if (publicKey && connection) {
        try {
          const balance = await connection.getBalance(publicKey);
          setBalance(balance / web3.LAMPORTS_PER_SOL);
          console.log('sol balance', balance);
        } catch (error) {
          console.error('Error fetching balance:', error);
        }
      }
    };

    // Fetch token balance if we have a token ID
    const getTokenBalance = async () => {
      if (publicKey && connection && tokenId?.mintPublicKey) {
        try {
          // Create token public key
          const mint = new web3.PublicKey(tokenId.mintPublicKey);

          // Find the associated token account
          const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
            publicKey,
            { mint }
          );

          if (tokenAccounts.value.length > 0) {
            const tokenBalance =
              tokenAccounts.value[0].account.data.parsed.info.tokenAmount
                .uiAmount;
            setTokenBalance(tokenBalance);
          } else {
            setTokenBalance(0);
          }
        } catch (error) {
          console.error('Error fetching token balance:', error);
          setTokenBalance(0);
        }
      }
    };

    if (connected) {
      getBalance();
      getTokenBalance();

      // Set up interval to refresh balances
      // const intervalId = setInterval(() => {
      //   getBalance();
      //   getTokenBalance();
      // }, 30000); // Refresh every 30 seconds

      // return () => clearInterval(intervalId);
    } else {
      setBalance(null);
      setTokenBalance(null);
    }
    // }, [publicKey, connection, connected, tokenId]);
  }, []);

  const SwapPumpFun = async () => {
    try {
      if (!publicKey) {
        alert('Wallet not connected');
        return;
      }

      try {
        // setSwapping(true);

        const swapAmount =
          activeTab === 'buy'
            ? amount * Math.pow(10, 9)
            : amount * Math.pow(10, 6);

        const SOL_ADDRESS = '********************************';
        const fromToken =
          activeTab == 'buy' ? SOL_ADDRESS : tokenId?.mintPublicKey;
        const toToken =
          activeTab == 'buy' ? tokenId?.mintPublicKey : SOL_ADDRESS;

        // 2. If swapping FROM native SOL, wrap first:
        let actualFrom = fromToken;

        const quoteParams = {
          chainId: '501',
          amount: `${swapAmount}`,
          fromTokenAddress: actualFrom,
          toTokenAddress: toToken,
          slippage: slippage / 100,
          priceImpactProtectionPercentage: '1.0',
          userWalletAddress: wallet.publicKey.toBase58(),
        };

        // 4. Fetch quote + tx data from your backend
        const response = await fetch('https://swap.ammag.tech/api/swap/quote', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(quoteParams),
        });

        const { data } = await response.json();
        console.log('data', data);

        console.log('response', response);
        if (response.status === 200) {
          // We already parsed the response body with response.json() earlier
          // Use the data we already have instead of trying to read the body again
          const tx = web3.VersionedTransaction.deserialize(
            bs58.decode(data.transactionData)
          );

          if (!wallet?.signTransaction) {
            alert('Wallet not connected or signing not supported');
            // toast({
            //   title: 'Failed to swap',
            //   description: 'Wallet not connected or signing not supported',
            //   variant: "destructive"
            // });
            return;
          }
          const { blockhash } = await connection.getLatestBlockhash();
          tx.message.recentBlockhash = blockhash;
          const signedTx = await wallet.signTransaction(tx);
          const signature = await connection.sendTransaction(signedTx);

          console.log('https://solscan.io/tx/' + signature);
          alert(`Transaction successful! You ${
            activeTab === 'buy' ? 'bought' : 'sold'
          } ${amount} ${
            activeTab === 'buy' ? 'SOL' : 'tokens'
          } and will receive approximately ${
            estimatedReceive
              ? estimatedReceive.amount.toFixed(4) +
                ' ' +
                estimatedReceive.symbol
              : 'your tokens/SOL'
          }.
          
Transaction: https://solscan.io/tx/${signature}`);
          // toast({
          //   title: "Swap successful",
          //   description: `Transaction completed: ${signature.slice(0, 8)}...`,
          // });
        } else {
          console.error('Error:', response.status, response.statusText);
          alert('Error: ' + response.status + ' ' + response.statusText);

          // toast({
          //   title: 'Failed to swap',
          //   description: errorData?.message || "Swap failed",
          //   variant: "destructive"
          // });
        }
      } catch (error) {
        alert('Swap failed"');
        // RefetchData();
        // toast({
        //   title: "Swap failed",
        //   description: error instanceof Error ? error.message : "Failed to swap",
        //   variant: "destructive",
        // });
        console.error('Error signing transaction:', error);

        // setSwapping(false);
      }
    } catch (error) {
      console.error('Error signing transaction:', error);
    }
  };

  return (
    <div className="trade-interface">
      <div className="trade-tab-buttons">
        <button
          className={`trade-tab-button ${
            activeTab === 'buy' ? 'buy-active' : 'inactive'
          }`}
          onClick={() => setActiveTab('buy')}
        >
          Buy
        </button>
        <button
          className={`trade-tab-button ${
            activeTab === 'sell' ? 'sell-active' : 'inactive'
          }`}
          onClick={() => setActiveTab('sell')}
        >
          Sell
        </button>
      </div>

      {/* Balance Display */}
      <div className="balance-container">
        <div className="balance-item">
          <span className="balance-label">SOL Balance:</span>
          <span className="balance-value">
            {balance !== null ? balance.toFixed(4) : 'Connect wallet'}
          </span>
        </div>
        {tokenId?.mintPublicKey && (
          <div className="balance-item">
            <span className="balance-label">Token Balance:</span>
            <span className="balance-value">
              {tokenBalance !== null
                ? tokenBalance.toFixed(4)
                : 'Connect wallet'}
            </span>
          </div>
        )}
      </div>

      {/* SOL Price Display */}
      {solPrice && (
        <div className="price-info">
          <span className="price-label">SOL Price:</span>
          <span className="price-value">${solPrice.toFixed(2)}</span>
        </div>
      )}

      {/* Estimated Receive Amount */}
      <div className="receive-info">
        {estimatedReceive ? (
          <>
            You'll receive approximately{' '}
            <span className="receive-amount">
              {estimatedReceive.amount.toFixed(4)} {estimatedReceive.symbol}
            </span>
          </>
        ) : (
          'Calculating...'
        )}
      </div>

      {/* Amount Selection */}
      <div className="trade-section">
        <div className="trade-section-header">
          <span className="label">Amount</span>
          <div className="icon-text">
            {imageUrl && (
              <img
                src={imageUrl}
                alt={tokenId?.name}
                className="token-image"
                style={{ height: '35px', width: '35px' }}
              />
            )}

            <input
              type="number"
              className="value-text amount-input"
              value={amount}
              onChange={e => setAmount(Number(e.target.value))}
              step="0.1"
              min="0.1"
            />
          </div>
        </div>

        <div className="option-grid">
          {amountOptions.map(option => (
            <button
              key={option}
              className={`option-button ${
                amount === option ? 'option-selected' : ''
              }`}
              onClick={() => setAmount(option)}
            >
              {option}
            </button>
          ))}
        </div>
      </div>

      {/* Slippage Selection */}
      <div className="trade-section">
        <div className="trade-section-header2">
          <span className="label">Slippage %</span>
          <span className="value-text">{slippage}</span>
        </div>

        <div className="option-grid">
          {slippageOptions.map(option => (
            <button
              key={option}
              className={`option-button ${
                slippage === option ? 'option-selected' : ''
              }`}
              onClick={() => setSlippage(option)}
            >
              {option}%
            </button>
          ))}
        </div>
      </div>

      {/* Error message for insufficient balance */}
      {activeTab === 'buy' && balance !== null && balance < amount && (
        <div className="error-message">
          Insufficient SOL balance. You need {amount} SOL but have{' '}
          {balance.toFixed(4)} SOL.
        </div>
      )}

      {activeTab === 'sell' && tokenBalance !== null && tokenBalance < amount && (
        <div className="error-message">
          Insufficient token balance. You need {amount} tokens but have{' '}
          {tokenBalance.toFixed(4)} tokens.
        </div>
      )}

      <button
        onClick={SwapPumpFun}
        className={`submit-button ${
          activeTab === 'buy' ? 'submit-buy' : 'submit-sell'
        } ${
          (activeTab === 'buy' && (balance === null || balance < amount)) ||
          (activeTab === 'sell' &&
            (tokenBalance === null || tokenBalance < amount))
            ? 'submit-disabled'
            : ''
        }`}
        disabled={
          (activeTab === 'buy' && (balance === null || balance < amount)) ||
          (activeTab === 'sell' &&
            (tokenBalance === null || tokenBalance < amount))
        }
      >
        {activeTab === 'buy' ? 'Buy' : 'Sell'}
      </button>
    </div>
  );
};

export default TradeInterface;
