// @flow
import * as React from 'react';
import { useEffect, useState, useRef } from 'react';
import SectionContainer, { SectionRow } from '../SectionContainer';
import { ColumnStackLayout } from '../../../../UI/Layout';
import { Spacer } from '../../../../UI/Grid';
import Text from '../../../../UI/Text';
import { useWallet } from '@solana/wallet-adapter-react';
import GameDetail from './components/Hero';
import TokenCard from './components/TokenCard';
import './GameDetailPage.css';
import Buysell from './components/Buysell';
import { TokenSection } from './components/TopGameTokens';
import axios from 'axios';
import { ProjectApi } from '../../../../Utils/GDevelopServices/ApiConfigs';
import { getWalletAuthorizationHeader } from '../../../../Utils/GDevelopServices/WalletAuthentication';
import { getLocalImageUrl } from './components/TopTokenCard';

const styles = {
  iframe: {
    border: 0,
  },
};

const GameDetailPage = ({ tokenId }) => {
  const [highlightedTokenId, setHighlightedTokenId] = useState(null);
  const [tokens, setTokens] = useState([]);
  const [gameData, setGameData] = useState(null);

  const [imageUrl, setImageUrl] = useState('');
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [liveData, setLiveData] = useState({
    price: null,
    change: null,
    marketCap: null,
    liquidity: null,
    fdv: null,
    totalSupply: null,
  });
  const prevTokensRef = useRef([]);

  console.log('Game detail section::::::::::::::', tokenId);
  console.log('Game data state:', gameData);

  const path = window.location.pathname;
  const gameMatch = path.match(/\/game\/([\w-]+)/);
  // Use the ID from the URL path
  const gameId = gameMatch[1];

  console.log('this is the gameId', { gameId, path });
  // If we have a token ID from the URL path (/game/123)
  useEffect(
    () => {
      if (!gameId) return;
      fetchGameData(gameId);
    },
    [gameId]
  );

  useEffect(
    () => {
      if (!gameData?.tokens?.[0] && !gameData?.tokens?.[0]?.mintPublicKey)
        return;
      //
      // const ws = new WebSocket('ws://localhost:8080');
      const ws = new WebSocket('wss://wsdata.sonicengine.net/');

      ws.onopen = () => {
        const subMsg = {
          op: 'subscribe',
          args: [{ address: gameData?.tokens?.[0]?.mintPublicKey }],
        };
        ws.send(JSON.stringify(subMsg));
      };

      ws.onmessage = event => {
        try {
          const msg = JSON.parse(event.data);
          console.log('message', msg);
          const tokenData = msg?.data;

          if (msg?.success && tokenData) {
            setLiveData({
              price: tokenData.price ?? null,
              change: tokenData.price_change_percent ?? null,
              marketCap: tokenData.market_cap ?? null,
              liquidity: tokenData.liquidity ?? null,
              fdv: tokenData.fdv ?? null,
              totalSupply: tokenData.total_supply ?? null,
            });
          }
        } catch (err) {
          console.error('WebSocket error parsing message:', err);
        }
      };

      ws.onerror = err => console.error('WebSocket error:', err);
      ws.onclose = () => console.log('WebSocket closed');

      return () => ws.close(); // cleanup on unmount
    },
    [gameData]
  );

  // Function to fetch game data
  const fetchGameData = async id => {
    try {
      console.log('Fetching data for game:', id);

      // Get authorization header for wallet authentication
      const authHeader = getWalletAuthorizationHeader();
      const headers = authHeader ? { Authorization: authHeader } : {};

      // Fetch game data from the API
      const response = await axios.get(`${ProjectApi.baseUrl}/game/${id}`, {
        headers,
      });

      console.log('Game data fetched:', response.data);
      setGameData(response.data);

      // Keep existing dummy data for tokens for now
      setHighlightedTokenId(id);
      setTokens([
        {
          id: id,
          name: `Game ${id}`,
          image: 'https://via.placeholder.com/150',
        },
        // Add more dummy tokens if needed
      ]);
    } catch (error) {
      console.error('Error fetching game data:', error);
      setGameData(null);
    }
  };

  // WebSocket connection to fetch game data
  useEffect(() => {
    const ws = new WebSocket('wss://wslauncher.sonicengine.net/');

    ws.onopen = () => {
      console.log('WebSocket connected to wslauncher.sonicengine.net');
    };

    ws.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        if (message.type === 'gameData') {
          const gameData = message.data;

          // Extract tokens[0] from each game
          const newTokensData = gameData
            .filter((game) => Array.isArray(game.tokens) && game.tokens.length > 0)
            .map((game) => game.tokens[0]);

          const newTokenIds = new Set(newTokensData.map((token) => token.id));

          // Detect new tokens for highlighting
          let highlightId = null;
          for (const token of newTokensData) {
            if (!prevTokensRef.current.some((prevToken) => prevToken.id === token.id)) {
              highlightId = token.id;
              break; // Highlight only the first new token
            }
          }

          // If no new tokens found, check for position changes (optional)
          if (!highlightId && newTokensData.length > 0 && prevTokensRef.current.length > 0) {
            // Check if first token changed (most common case for highlighting top movers)
            if (newTokensData[0].id !== prevTokensRef.current[0].id) {
              highlightId = newTokensData[0].id;
            }
          }

          // Update states
          setTokens(newTokensData); // Set tokens for TokenSection
          setHighlightedTokenId(highlightId);
          prevTokensRef.current = newTokensData;

          // Clear highlight after 1.5 seconds
          if (highlightId) {
            setTimeout(() => {
              setHighlightedTokenId(null);
            }, 1500);
          }
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };

    ws.onclose = () => {
      console.log('WebSocket disconnected. Reconnecting...');
      setTimeout(() => {
        // Reconnect logic (could be extracted into a function if needed)
      }, 5000);
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
    };

    return () => ws.close(); // Cleanup on unmount
  }, []);
  useEffect(
    () => {
      const fetchIPFSImage = async () => {
        console.log(gameData?.tokens?.[0],'gameData?.tokens?.[0] meeee')
        if (!gameData?.tokens?.[0]?.ipfsMetadataUri) {
          setImageUrl(getLocalImageUrl(gameData?.tokens?.[0]?.imageUrl));
          setImageLoading(false);
          return;
        }

        try {
          setImageLoading(true);
          const response = await fetch(gameData?.tokens?.[0].ipfsMetadataUri);

          if (response.ok) {
            const metadata = await response.json();
            if (metadata.image) {
              console.log(metadata.image,'meeee')
              setImageUrl(metadata.image);
            } else {
              // Fallback to local image
              setImageUrl(getLocalImageUrl(gameData?.tokens?.[0]?.imageUrl));
            }
          } else {
            // Fallback to local image
            setImageUrl(getLocalImageUrl(gameData?.tokens?.[0]?.imageUrl));
          }
        } catch (error) {
          console.error('Error fetching IPFS metadata:', error);
          // Fallback to local image
          setImageUrl(getLocalImageUrl(gameData?.tokens?.[0]?.imageUrl));
        } finally {
          setImageLoading(false);
        }
      };

      fetchIPFSImage();
    },
    [gameData?.tokens?.[0]?.ipfsMetadataUri, gameData?.tokens?.[0]?.imageUrl]
  );

  return (
    <SectionContainer flexBody>
      <SectionRow expand>
        <ColumnStackLayout noMargin>
          <div className="game-detail-container">
            <div
              className={`game-detail-main ${!gameData?.tokens?.[0] ? 'full' : ''}`}
            >
              <GameDetail data={gameData} />
            </div>
            {gameData?.tokens?.[0] && (
              <div className="game-detail-sidebar">
                <TokenCard data={gameData.tokens[0]} imageUrl={imageUrl} liveData={liveData} />
                <Buysell tokenId={gameData.tokens[0]} imageUrl={imageUrl} liveData={liveData} />
              </div>
            )}
          </div>

          <div>
            <TokenSection
              title="New"
              subtitle="Games Token"
              tokens={tokens}
              className="mt-16"
              highlightedTokenId={highlightedTokenId}
            />
          </div>
        </ColumnStackLayout>
      </SectionRow>
    </SectionContainer>
  );
};

export default GameDetailPage;
