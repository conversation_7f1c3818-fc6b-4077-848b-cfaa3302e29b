// @flow
import * as React from 'react';
import { t, Trans } from '@lingui/macro';
import { I18n } from '@lingui/react';
import { type I18n as I18nType } from '@lingui/core';
import Tooltip from '@material-ui/core/Tooltip';
import {
  ColumnStackLayout,
  LineStackLayout,
  ResponsiveLineStackLayout,
} from '../UI/Layout';
import {
  type FileMetadata,
  type FileMetadataAndStorageProviderName,
  type StorageProvider,
} from '../ProjectsStorage';
import FlatButton from '../UI/FlatButton';
import Text from '../UI/Text';
import { GameThumbnail } from './GameThumbnail';
import { type MenuItemTemplate } from '../UI/Menu/Menu.flow';
import {
  getGameMainImageUrl,
  getGameUrl,
  type Game,
} from '../Utils/GDevelopServices/Game';
import { FileCopyOutlined, Check } from '@material-ui/icons';
import Card from '../UI/Card';
import GDevelopThemeContext from '../UI/Theme/GDevelopThemeContext';
import { useResponsiveWindowSize } from '../UI/Responsive/ResponsiveWindowMeasurer';
import { useWallet } from '@solana/wallet-adapter-react';
import { useWalletUser } from '../Utils/store/WalletUserContext';
import { deployTokenAndCreateToken } from '../ExportAndShare/GenericExporters/PublishExport';
import Dialog from '../UI/Dialog';
import CircularProgress from '../UI/CircularProgress';
import TextField from '../UI/TextField';
import axios from 'axios';
import { GDevelopSubscriptionApi } from '../Utils/GDevelopServices/ApiConfigs';
import { getAdminSettings } from '../Utils/GDevelopServices/User';
import { getWalletAuthorizationHeader } from '../Utils/GDevelopServices/WalletAuthentication';
import Visibility from '../UI/CustomSvgIcons/Visibility';
import VisibilityOff from '../UI/CustomSvgIcons/VisibilityOff';
import DollarCoin from '../UI/CustomSvgIcons/DollarCoin';
import Cross from '../UI/CustomSvgIcons/Cross';
import Messages from '../UI/CustomSvgIcons/Messages';
import GameLinkAndShareIcons from './GameLinkAndShareIcons';
import { getStorageProviderByInternalName } from '../MainFrame/EditorContainers/HomePage/CreateSection/utils';
import useOnResize from '../Utils/UseOnResize';
import useForceUpdate from '../Utils/UseForceUpdate';
import RaisedButtonWithSplitMenu from '../UI/RaisedButtonWithSplitMenu';
import { type LastModifiedInfoByProjectId } from '../MainFrame/EditorContainers/HomePage/CreateSection/utils';
import AuthenticatedUserContext from '../Profile/AuthenticatedUserContext';
import useAlertDialog from '../UI/Alert/useAlertDialog';
import LastModificationInfo from '../MainFrame/EditorContainers/HomePage/CreateSection/LastModificationInfo';
import optionalRequire from '../Utils/OptionalRequire';
import RaisedButton from '../UI/RaisedButton';
import { Column, Line, Spacer } from '../UI/Grid';
import ElementWithMenu from '../UI/Menu/ElementWithMenu';
import IconButton from '../UI/IconButton';
import ThreeDotsMenu from '../UI/CustomSvgIcons/ThreeDotsMenu';
import WarningRound from '../UI/CustomSvgIcons/WarningRound';
import PreferencesContext from '../MainFrame/Preferences/PreferencesContext';
import { textEllipsisStyle } from '../UI/TextEllipsis';
import FileWithLines from '../UI/CustomSvgIcons/FileWithLines';
import TextButton from '../UI/TextButton';
import { getRelativeOrAbsoluteDisplayDate } from '../Utils/DateDisplay';
import { LoadingManager } from 'three';
// It's important to use remote and not electron for folder actions,
// otherwise they will be opened in the background.
// See https://github.com/electron/electron/issues/4349#issuecomment-777475765
const remote = optionalRequire('@electron/remote');
const shell = remote ? remote.shell : null;
const path = optionalRequire('path');

export const getThumbnailWidth = ({ isMobile }: {| isMobile: boolean |}) =>
  isMobile ? undefined : Math.min(245, Math.max(130, window.innerWidth / 4));

export const getProjectDisplayDate = (i18n: I18nType, date: number) =>
  getRelativeOrAbsoluteDisplayDate({
    i18n,
    dateAsNumber: date,
    sameDayFormat: 'todayAndHour',
    dayBeforeFormat: 'yesterdayAndHour',
    relativeLimit: 'currentWeek',
    sameWeekFormat: 'thisWeek',
  });
export const getDetailedProjectDisplayDate = (i18n: I18nType, date: number) => {
  return i18n.date(date, {
    dateStyle: 'short',
    timeStyle: 'short',
  });
};

const getNoProjectAlertMessage = () => {
  if (!remote) {
    // Trying to open a local project from the web app of the mobile app.
    return t`Looks like your project isn't there!${'\n\n'}Your project must be stored on your computer.`;
  } else {
    return t`We couldn't find your project.${'\n\n'}If your project is stored on a different computer, launch GDevelop on that computer.${'\n'}Otherwise, use the "Open project" button and find it in your filesystem.`;
  }
};

const styles = {
  tooltipButtonContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'stretch',
  },
  buttonsContainer: {
    display: 'flex',
    flexShrink: 0,
    flexDirection: 'column',
    justifyContent: 'center',
  },
  iconAndText: { display: 'flex', gap: 2, alignItems: 'flex-start' },
  title: {
    ...textEllipsisStyle,
    overflowWrap: 'break-word',
  },
  projectFilesButton: { minWidth: 32 },
  fileIcon: {
    width: 16,
    height: 16,
  },
};

const locateProjectFile = (file: FileMetadataAndStorageProviderName) => {
  if (!shell) return;
  shell.showItemInFolder(path.resolve(file.fileMetadata.fileIdentifier));
};
const getFileNameWithoutExtensionFromPath = (path: string) => {
  // Normalize path separators for cross-platform compatibility
  const normalizedPath = path.replace(/\\/g, '/');

  // Extract file name
  const fileName = normalizedPath.split('/').pop();

  // Handle dotfiles and files without extensions
  if (fileName) {
    const parts = fileName.split('.');
    return parts.length > 1 ? parts.slice(0, -1).join('.') : fileName;
  }

  return '';
};

const getProjectItemLabel = (
  file: FileMetadataAndStorageProviderName,
  storageProviders: Array<StorageProvider>,
  i18n: I18nType
): string => {
  const fileMetadataName = file.fileMetadata.name || '-';
  const name =
    file.storageProviderName === 'LocalFile'
      ? getFileNameWithoutExtensionFromPath(file.fileMetadata.fileIdentifier) ||
        fileMetadataName
      : fileMetadataName;
  const storageProvider = getStorageProviderByInternalName(
    storageProviders,
    file.storageProviderName
  );
  return i18n._(
    `${name} (${
      storageProvider ? i18n._(storageProvider.name) : file.storageProviderName
    })`
  );
};

export type DashboardItem = {|
  game?: Game, // A project can not be published, and thus not have a game.
  projectFiles?: Array<FileMetadataAndStorageProviderName>, // A game can have no or multiple projects.
|};

type Props = {|
  dashboardItem: DashboardItem,
  storageProviders: Array<StorageProvider>,
  isCurrentProjectOpened: boolean,
  onOpenGameManager: ({ game: Game, widgetToScrollTo?: 'projects' }) => void,
  onOpenProject: (file: FileMetadataAndStorageProviderName) => Promise<void>,
  onUnregisterGame: () => Promise<void>,
  disabled: boolean,
  canSaveProject: boolean,
  askToCloseProject: () => Promise<boolean>,
  closeProject: () => Promise<void>,
  onSaveProject: () => Promise<void>,
  lastModifiedInfoByProjectId: LastModifiedInfoByProjectId,
  currentFileMetadata: ?FileMetadata,
  onRefreshGames: () => Promise<void>,
  onDeleteCloudProject: (
    file: FileMetadataAndStorageProviderName
  ) => Promise<void>,
  onRegisterProject: (
    file: FileMetadataAndStorageProviderName
  ) => Promise<?Game>,
|};

const GameDashboardCard = ({
  dashboardItem,
  storageProviders,
  isCurrentProjectOpened,
  onOpenGameManager,
  onOpenProject,
  onUnregisterGame,
  disabled,
  canSaveProject,
  askToCloseProject,
  closeProject,
  onSaveProject,
  lastModifiedInfoByProjectId,
  currentFileMetadata,
  onRefreshGames,
  onDeleteCloudProject,
  onRegisterProject,
  setGlobelSessionId,
}: Props) => {
  useOnResize(useForceUpdate());
  const projectsList = React.useMemo(() => dashboardItem.projectFiles || [], [
    dashboardItem.projectFiles,
  ]);
  console.log('dddd dasshhhhh boaaaddd', { diG: dashboardItem });
  const game = dashboardItem.game;
  // Use the latest (last) project file instead of the first one
  // Comment: We only care about the latest file, not multiple files
  const projectFileMetadataAndStorageProviderName = projectsList.length
    ? projectsList[projectsList.length - 1]
    : null;
  const lastModifiedInfo = projectFileMetadataAndStorageProviderName
    ? lastModifiedInfoByProjectId[
        projectFileMetadataAndStorageProviderName.fileMetadata.fileIdentifier
      ]
    : null;

  // Token related state
  const [isDeployingToken, setIsDeployingToken] = React.useState(false);
  const [tokenDeployError, setTokenDeployError] = React.useState(null);
  const [tokenDeploySuccess, setTokenDeploySuccess] = React.useState(false);
  const [copiedTokenAddress, setCopiedTokenAddress] = React.useState(false);
  const [showTokenDialog, setShowTokenDialog] = React.useState(false);
  const [forceUpdate, setForceUpdate] = React.useState(0);

  // Renew subscription state
  const [isRenewing, setIsRenewing] = React.useState(false);
  const [publishFeeUSD, setPublishFeeUSD] = React.useState(null);
  const [showRenewConfirmation, setShowRenewConfirmation] = React.useState(
    false
  );

  // Cancel subscription state
  const [isCanceling, setIsCanceling] = React.useState(false);
  const [showCancelConfirmation, setShowCancelConfirmation] = React.useState(
    false
  );

  // Token form state - initialize with game data
  const [tokenName, setTokenName] = React.useState(game?.gameName || '');
  const [tokenDescription, setTokenDescription] = React.useState(
    game?.description || ''
  );
  const [tokenSymbol, setTokenSymbol] = React.useState(
    game?.gameName ? game.gameName.substring(0, 4).toUpperCase() : ''
  );
  const [tokenWebsite, setTokenWebsite] = React.useState('');
  const [tokenTwitter, setTokenTwitter] = React.useState('');
  const [tokenTelegram, setTokenTelegram] = React.useState('');
  const [tokenDevSupply, setTokenDevSupply] = React.useState('1000');
  const [tokenImage, setTokenImage] = React.useState(null);

  // Update form when game changes
  React.useEffect(
    () => {
      if (game && showTokenDialog) {
        setTokenName(game.gameName || '');
        setTokenDescription(game.description || '');
        setTokenSymbol(
          game.gameName ? game.gameName.substring(0, 4).toUpperCase() : ''
        );
      }
    },
    [game, showTokenDialog]
  );

  const wallet = useWallet();
  const { profile: walletProfile, fetchProfile } = useWalletUser();

  const authenticatedUser = React.useContext(AuthenticatedUserContext);
  const { profile, onOpenLoginDialog } = authenticatedUser;

  // Fetch admin settings to get publish fee
  React.useEffect(() => {
    const fetchAdminSettings = async () => {
      try {
        console.log('fetchAdminSettings');
        const settings = await getAdminSettings();
        console.log({ settings }, 'settings');
        if (settings && settings.publishFeeUSD) {
          console.log('settings.publishFeeUSD', settings.publishFeeUSD);
          setPublishFeeUSD(settings.publishFeeUSD);
        }
      } catch (error) {
        console.error('Error fetching admin settings:', error);
      }
    };

    fetchAdminSettings();
  }, []);

  // Function to handle showing renew confirmation
  const handleRenewSubscription = () => {
    if (!game || !game.id) {
      showAlert({
        title: t`Game not found`,
        message: t`Cannot renew subscription for this game. Game information is missing.`,
      });
      return;
    }

    // Check if user has sufficient balance
    if (walletProfile && publishFeeUSD) {
      const userBalance = walletProfile.balance || 0;
      if (userBalance < publishFeeUSD) {
        showAlert({
          title: t`Insufficient Balance`,
          message: t`You need at least ${publishFeeUSD.toFixed(
            2
          )} credits but have ${userBalance.toFixed(
            2
          )} credits to renew your game subscription.`,
        });
        return;
      }
    }

    // Show confirmation modal
    setShowRenewConfirmation(true);
  };

  // Function to actually process the renewal after confirmation
  const confirmRenewal = async () => {
    setShowRenewConfirmation(false);
    setIsRenewing(true);

    try {
      const authHeader = getWalletAuthorizationHeader();
      if (!authHeader) {
        throw new Error('Authentication required');
      }

      const response = await axios.post(
        `${GDevelopSubscriptionApi.baseUrl}/${game.id}/renew`,
        {},
        {
          headers: {
            Authorization: authHeader,
          },
        }
      );

      if (response.data && response.data.success) {
        showAlert({
          title: t`Subscription Renewed`,
          message: t`Your game subscription has been successfully renewed!`,
        });
        // Refresh the games list to update the status
        await fetchProfile();
        await onRefreshGames();
      } else {
        throw new Error(
          response.data.message || 'Failed to renew subscription'
        );
      }
    } catch (error) {
      console.error('Error renewing subscription:', error);
      showAlert({
        title: t`Renewal Failed`,
        message: t`Failed to renew subscription: ${error.message ||
          'Unknown error'}`,
      });
    } finally {
      setIsRenewing(false);
    }
  };

  // Function to handle showing cancel confirmation
  const handleCancelSubscription = () => {
    if (!game || !game.id) {
      showAlert({
        title: t`Game not found`,
        message: t`Cannot cancel subscription for this game. Game information is missing.`,
      });
      return;
    }

    // Show confirmation modal
    setShowCancelConfirmation(true);
  };

  // Function to actually process the cancellation after confirmation
  const confirmCancellation = async () => {
    setShowCancelConfirmation(false);
    setIsCanceling(true);

    try {
      const authHeader = getWalletAuthorizationHeader();
      if (!authHeader) {
        throw new Error('Authentication required');
      }

      const response = await axios.post(
        `${GDevelopSubscriptionApi.baseUrl}/${game.id}/cancel`,
        {},
        {
          headers: {
            Authorization: authHeader,
          },
        }
      );

      if (response.data && response.data.success) {
        showAlert({
          title: t`Subscription Canceled`,
          message: t`Your game subscription has been successfully canceled!`,
        });
        // Refresh the games list to update the status
        await fetchProfile();
        await onRefreshGames();
      } else {
        throw new Error(
          response.data.message || 'Failed to cancel subscription'
        );
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      showAlert({
        title: t`Cancellation Failed`,
        message: t`Failed to cancel subscription: ${error.message ||
          'Unknown error'}`,
      });
    } finally {
      setIsCanceling(false);
    }
  };
  const { removeRecentProjectFile } = React.useContext(PreferencesContext);
  const {
    showAlert,
    showConfirmation,
    showDeleteConfirmation,
  } = useAlertDialog();

  const isPublishedOnGdGames = !!game && game.publicWebBuildId;

  const gameUrl = isPublishedOnGdGames ? game?.gameURL : null;
  // const gameUrl = isPublishedOnGdGames ? getGameUrl(game) : null;

  const gameThumbnailUrl = React.useMemo(() => game?.gameThumbnail, [game]);
  const gameName = game
    ? game.gameName
    : projectFileMetadataAndStorageProviderName
    ? projectFileMetadataAndStorageProviderName.fileMetadata.name
    : null;

  const { isMobile, windowSize, isLandscape } = useResponsiveWindowSize();
  const isSmallOrMediumScreen =
    windowSize === 'small' || windowSize === 'medium';
  const gdevelopTheme = React.useContext(GDevelopThemeContext);
  const itemStorageProvider = projectFileMetadataAndStorageProviderName
    ? getStorageProviderByInternalName(
        storageProviders,
        projectFileMetadataAndStorageProviderName.storageProviderName
      )
    : null;
  const status =
    projectFileMetadataAndStorageProviderName?.fileMetadata?.status;

  const renderPublicInfo = () => {
    // For archived games, show different icon and text
    const isArchived = game && game.isArchived;
    const DiscoverabilityIcon = isArchived
      ? VisibilityOff
      : game && gameUrl
      ? Visibility
      : VisibilityOff;
    const AdsIcon = game && game.displayAdsOnGamePage ? DollarCoin : Cross;
    const PlayerFeedbackIcon =
      game && game.acceptsGameComments ? Messages : Cross;
    const textProps = {
      color: 'secondary',
      size: 'body-small',
      noMargin: true,
    };
    const iconProps = {
      htmlColor: gdevelopTheme.text.color.secondary,
      fontSize: 'small',
    };
    return (
      <ResponsiveLineStackLayout alignItems="center" noColumnMargin>
        <div style={styles.iconAndText}>
          <DiscoverabilityIcon {...iconProps} />
          <Text {...textProps}>
            {isArchived ? (
              <Trans>Archived</Trans>
            ) : game && game.discoverable && gameUrl ? (
              <Trans>Public on gd.games</Trans>
            ) : gameUrl ? (
              <Trans>Published</Trans>
            ) : (
              <Trans>Not published</Trans>
            )}
          </Text>
        </div>
        {/* {game && (
          <div style={styles.iconAndText}>
            <AdsIcon {...iconProps} />
            <Text {...textProps}>
              {game.displayAdsOnGamePage ? (
                <Trans>Ad revenue sharing on</Trans>
              ) : (
                <Trans>Ad revenue sharing off</Trans>
              )}
            </Text>
          </div>
        )}
        {game && (
          <div style={styles.iconAndText}>
            <PlayerFeedbackIcon {...iconProps} />
            <Text {...textProps}>
              {game.acceptsGameComments ? (
                <Trans>Player feedback on</Trans>
              ) : (
                <Trans>Player feedback off</Trans>
              )}
            </Text>
          </div>
        )} */}
      </ResponsiveLineStackLayout>
    );
  };

  const renderTitle = () => (
    <Line noMargin expand alignItems="center">
      <Text size="block-title" noMargin style={styles.title}>
        {gameName || <Trans>Unknown game</Trans>}
      </Text>
      {/* Comment: Multiple project files logic commented out - we only show the latest file
      {projectsList.length >= 2 && game && (
        <>
          <Spacer />
          <Tooltip title={<Trans>{projectsList.length} projects</Trans>}>
            <div style={styles.tooltipButtonContainer}>
              <TextButton
                onClick={() =>
                  onOpenGameManager({ game, widgetToScrollTo: 'projects' })
                }
                icon={<FileWithLines style={styles.fileIcon} />}
                label={
                  <Text noMargin color="secondary">
                    {projectsList.length}
                  </Text>
                }
                disabled={disabled}
                style={styles.projectFilesButton}
              />
            </div>
          </Tooltip>
        </>
      )}
      */}
    </Line>
  );

  const renderLastModification = (i18n: I18nType) =>
    projectFileMetadataAndStorageProviderName ? (
      <LastModificationInfo
        file={projectFileMetadataAndStorageProviderName}
        lastModifiedInfo={lastModifiedInfo}
        storageProvider={itemStorageProvider}
        authenticatedUser={authenticatedUser}
        currentFileMetadata={currentFileMetadata}
        textColor="secondary"
        textSize="body-small"
        textPrefix={isCurrentProjectOpened ? null : <Trans>Last edited:</Trans>}
      />
    ) : game ? (
      <LineStackLayout noMargin expand>
        <Text color="secondary" noMargin size="body-small">
          {!itemStorageProvider && isCurrentProjectOpened ? (
            <Trans>Draft created:</Trans>
          ) : (
            <Trans>Last edited:</Trans>
          )}
        </Text>
        <Tooltip
          placement="right"
          title={getDetailedProjectDisplayDate(
            i18n,
            (() => {
              let updatedAt = game.updatedAt;
              if (!updatedAt) return 0;
              if (typeof updatedAt === 'string') {
                const ms = Date.parse(updatedAt);
                return isNaN(ms) ? 0 : ms;
              }
              if (typeof updatedAt === 'number') {
                // If it's in seconds (10 digits), convert to ms
                return updatedAt < 1e12 ? updatedAt * 1000 : updatedAt;
              }
              return 0;
            })()
          )}
        >
          <Text color="secondary" noMargin size="body-small">
            {getProjectDisplayDate(
              i18n,
              (() => {
                let updatedAt = game.updatedAt;
                if (!updatedAt) return 0;
                if (typeof updatedAt === 'string') {
                  const ms = Date.parse(updatedAt);
                  return isNaN(ms) ? 0 : ms;
                }
                if (typeof updatedAt === 'number') {
                  // If it's in seconds (10 digits), convert to ms
                  return updatedAt < 1e12 ? updatedAt * 1000 : updatedAt;
                }
                return 0;
              })()
            )}
          </Text>
        </Tooltip>
      </LineStackLayout>
    ) : null;

  const renderStorageProvider = (i18n: I18nType) => {
    const icon = itemStorageProvider ? (
      itemStorageProvider.renderIcon ? (
        itemStorageProvider.renderIcon({
          size: 'small',
        })
      ) : null
    ) : (
      <WarningRound />
    );
    const name = itemStorageProvider ? (
      i18n._(itemStorageProvider.name)
    ) : isCurrentProjectOpened ? (
      <Trans>Project not saved</Trans>
    ) : (
      <Trans>Project not found</Trans>
    );

    return (
      <Line noMargin alignItems="center">
        {icon && (
          <>
            {icon}
            <Spacer />
          </>
        )}
        <Text noMargin color="secondary">
          {name}
        </Text>
      </Line>
    );
  };

  const renderThumbnail = () => (
    <GameThumbnail
      gameName={gameName || 'unknown game'}
      gameId={game ? game.id : undefined}
      thumbnailUrl={gameThumbnailUrl}
      background="light"
      width={getThumbnailWidth({ isMobile })}
    />
  );

  // Comment: Multiple project files context menu logic commented out - we only use the latest file
  const buildOpenProjectContextMenu = (
    i18n: I18nType
  ): Array<MenuItemTemplate> => {
    const actions = [];
    /* Comment: Multiple project files logic commented out
    if (projectsList.length > 1) {
      actions.push(
        ...projectsList.slice(0, 3).map(fileMetadataAndStorageProviderName => {
          return {
            label: getProjectItemLabel(
              fileMetadataAndStorageProviderName,
              storageProviders,
              i18n
            ),
            click: () => onOpenProject(fileMetadataAndStorageProviderName),
          };
        })
      );

      if (game) {
        actions.push(
          ...[
            { type: 'separator' },
            {
              label: i18n._(t`See all in the game dashboard`),
              click: () =>
                onOpenGameManager({ game, widgetToScrollTo: 'projects' }),
            },
          ]
        );
      }
    }
    */

    return actions;
  };

  const renderAdditionalActions = () => {
    return (
      <ElementWithMenu
        element={
          <IconButton size="small" disabled={disabled}>
            <ThreeDotsMenu />
          </IconButton>
        }
        buildMenuTemplate={(i18n: I18nType) => {
          const actions = [];

          // Close action
          if (isCurrentProjectOpened) {
            actions.push({
              label: i18n._(t`Close project`),
              click: async () => {
                await askToCloseProject();
              },
            });
          }

          // Management actions.
          if (projectsList.length === 0) {
            // No management possible, it's a game without a project found.
          }

          if (projectsList.length === 1) {
            // Use the latest (last) project file instead of the first one
            const file = projectsList[projectsList.length - 1];
            if (file && file.storageProviderName === 'LocalFile') {
              actions.push({
                label: i18n._(t`Show in local folder`),
                click: () => locateProjectFile(file),
              });
            }
          }

          /* Comment: Multiple project files logic commented out - we only use the latest file
          if (projectsList.length > 1) {
            // If there are multiple projects, suggest opening the game dashboard.
            actions.push({
              label: i18n._(t`See all projects`),
              click: game
                ? () =>
                    onOpenGameManager({ game, widgetToScrollTo: 'projects' })
                : undefined,
            });
          }
          */

          // Delete actions.
          // Don't allow removing project if opened, as it would not result in any change in the list.
          // (because an opened project is always displayed)
          // Comment: Multiple project files logic commented out - we only use the latest file
          if (isCurrentProjectOpened /* || projectsList.length > 1 */) {
            // No delete action possible.
          } else {
            if (actions.length > 0) {
              actions.push({
                type: 'separator',
              });
            }

            actions.push({
              label: i18n._(t`Delete`),
              click: async () => {
                // Extract word translation to ensure it is not wrongly translated in the sentence.
                const translatedConfirmText = i18n._(t`delete`);

                const answer = await showDeleteConfirmation({
                  title: t`Delete game`,
                  message: t`Your game will be deleted. This action is irreversible. Do you want to continue?`,
                  confirmButtonLabel: t`Delete game`,
                  fieldMessage: t`To confirm, type "${translatedConfirmText}"`,
                  confirmText: translatedConfirmText,
                });
                if (!answer) return;

                // If the game is registered, unregister it.
                // If it fails, this will throw, to prevent deleting a game with leaderboards or not owned.
                if (game) {
                  try {
                    await onUnregisterGame();
                  } catch (error) {
                    console.error('Unable to unregister the game.', error);
                    // Alert is handled by onUnregisterGame. Just ensure we don't continue.
                    return;
                  }
                }

                // If there is a project file (local or cloud), remove it.
                // Use the latest (last) project file instead of the first one
                const file = projectsList[projectsList.length - 1];
                if (file) {
                  if (file.storageProviderName === 'Cloud') {
                    await onDeleteCloudProject(file);
                  } else {
                    await removeRecentProjectFile(file);
                  }
                }

                await onRefreshGames();
              },
            });
          }

          return actions;
        }}
      />
    );
  };

  const onManageGame = React.useCallback(
    async () => {
      if (game) {
        onOpenGameManager({ game });
        return;
      } else {
        if (!profile) {
          onOpenLoginDialog();
          return;
        }
        const answer = await showConfirmation({
          title: t`Manage game online`,
          message: t`This game is not registered online. Do you want to register it to access the online features?`,
          confirmButtonLabel: t`Continue`,
        });
        if (!answer) return;

        // Use the latest (last) project file instead of the first one
        const latestProject = projectsList[projectsList.length - 1];
        const registeredGame = await onRegisterProject(latestProject);
        if (!registeredGame) return;

        await onRefreshGames();
        onOpenGameManager({ game: registeredGame });
      }
    },
    [
      game,
      onOpenGameManager,
      showConfirmation,
      onRegisterProject,
      projectsList,
      onRefreshGames,
      onOpenLoginDialog,
      profile,
    ]
  );

  const renderButtons = ({ fullWidth }) => {
    const isArchived = game && game.isArchived;

    // For archived games, show Renew button
    if (isArchived) {
      return (
        <div style={styles.buttonsContainer}>
          <LineStackLayout noMargin>
            <RaisedButton
              primary
              fullWidth={fullWidth}
              label={
                publishFeeUSD === null ? (
                  <Trans>Loading...</Trans>
                ) : isRenewing ? (
                  <Trans>Renewing...</Trans>
                ) : (
                  <Trans>Renew Subscription</Trans>
                )
              }
              onClick={handleRenewSubscription}
              disabled={disabled || isRenewing || publishFeeUSD === null}
              icon={isRenewing ? <CircularProgress size={16} /> : null}
            />
          </LineStackLayout>
        </div>
      );
    }

    // For non-archived games, show normal Open/Save button
    const openProjectLabel = isCurrentProjectOpened ? (
      <Trans>Save Project</Trans>
    ) : (
      <Trans>Open in Editor</Trans>
    );
    const mainAction = isCurrentProjectOpened
      ? onSaveProject
      : projectsList.length > 0
      ? () => {
          // Use the latest (last) project file instead of the first one
          const latestProject = projectsList[projectsList.length - 1];
          onOpenProject(latestProject);

          setGlobelSessionId(latestProject?.fileMetadata?.sessionId);
        }
      : () => {
          showAlert({
            title: t`No project to open`,
            message: getNoProjectAlertMessage(),
          });
        };

    return (
      <div style={styles.buttonsContainer}>
        <LineStackLayout noMargin>
          {/*<FlatButton
            primary
            fullWidth={fullWidth}
            label={<Trans>Manage</Trans>}
            onClick={onManageGame}
            disabled={disabled}
          />*/}
          {/* Comment: Multiple project files logic commented out - we only use the latest file
          {projectsList.length < 2 ? (
            <RaisedButton
              primary
              fullWidth={fullWidth}
              label={openProjectLabel}
              onClick={mainAction}
              disabled={disabled || (isCurrentProjectOpened && !canSaveProject)}
            />
          ) : (
            <RaisedButtonWithSplitMenu
              primary
              fullWidth={fullWidth}
              label={openProjectLabel}
              onClick={mainAction}
              buildMenuTemplate={i18n => buildOpenProjectContextMenu(i18n)}
              disabled={disabled || (isCurrentProjectOpened && !canSaveProject)}
            />
          )}
          */}
          {game && gameUrl && !isArchived && (
            <RaisedButton
              primary
              label={<Trans>Play Game</Trans>}
              onClick={handlePlayGame}
              // disabled={disabled}
            />
          )}
          <RaisedButton
            primary
            fullWidth={fullWidth}
            label={openProjectLabel}
            onClick={mainAction}
            disabled={disabled || (isCurrentProjectOpened && !canSaveProject)}
          />

          {/* Play button for published games */}

          {/* Cancel subscription button for non-archived, non-cancelled published games */}
          {game && gameUrl && !isArchived && (
            <FlatButton
              label={
                game.isCancelled ? (
                  <Trans>Subscription Cancelled</Trans>
                ) : isCanceling ? (
                  <Trans>Canceling...</Trans>
                ) : (
                  <Trans>Cancel Subscription</Trans>
                )
              }
              onClick={handleCancelSubscription}
              disabled={disabled || isCanceling || game.isCancelled}
              icon={isCanceling ? <CircularProgress size={16} /> : null}
            />
          )}
        </LineStackLayout>
      </div>
    );
  };

  // Function to handle opening token deployment dialog
  const handleDeployToken = () => {
    if (!wallet.connected) {
      showAlert({
        title: t`Wallet not connected`,
        message: t`Please connect your wallet to deploy a token for this game.`,
      });
      return;
    }

    if (!game || !game.id) {
      showAlert({
        title: t`Game not found`,
        message: t`Cannot deploy a token for this game. Game information is missing.`,
      });
      return;
    }

    // Open the token deployment dialog
    setShowTokenDialog(true);
  };

  // Function to handle actual token deployment
  const handleConfirmTokenDeployment = async () => {
    if (!tokenName.trim() || !tokenDescription.trim() || !tokenSymbol.trim()) {
      setTokenDeployError(
        'Please fill in all required fields (Name, Description, Symbol)'
      );
      return;
    }

    setIsDeployingToken(true);
    setTokenDeployError(null);

    try {
      // Token data from form
      const tokenData = {
        name: tokenName.trim(),
        description: tokenDescription.trim(),
        symbol: tokenSymbol.trim().toUpperCase(),
        website: tokenWebsite.trim(),
        twitter: tokenTwitter.trim(),
        telegram: tokenTelegram.trim(),
        devSupply: parseInt(tokenDevSupply) || 1000,
      };

      // Deploy the token
      const result = await deployTokenAndCreateToken(
        tokenData,
        wallet.publicKey.toString(),
        game.id,
        wallet,
        setTokenDeployError,
        setIsDeployingToken,
        tokenImage, // Pass the image file
        setTokenDeploySuccess
      );

      if (result) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        await onRefreshGames();
        setTokenDeploySuccess(true);
        setShowTokenDialog(false);
        setForceUpdate(prev => prev + 1);

        // Note: Token info is now stored in game.tokens[0] instead of project metadata
        // The onRefreshGames() call above will fetch the updated game data with the new token
      }
    } catch (error) {
      console.error('Error deploying token:', error);
      setTokenDeployError(error.message || 'Failed to deploy token');
    } finally {
      setIsDeployingToken(false);
    }
  };

  // Function to handle image file selection
  const handleImageChange = event => {
    if (event.target.files && event.target.files[0]) {
      setTokenImage(event.target.files[0]);
    }
  };

  // Function to handle play button click (similar to TopTokenCard onClick)
  const handlePlayGame = () => {
    // Check if token info is available in game.tokens[0]
    const tokenInfo = game?.tokens?.[0]?.mintPublicKey || game?.tokens?.[0]?.tokenId || game?.tokens?.[0]?.id;

    if (tokenInfo) {
      window.dispatchEvent(
        new CustomEvent('navigate-to-preview-game', {
          detail: { tokenId: { mintPublicKey: tokenInfo, GameUrl: gameUrl, GameThumbnail: gameThumbnailUrl} },
        })
      );
      window.history.pushState({}, '', `/game/${game.gameId}`);
    } else if (gameUrl) {
      window.dispatchEvent(
        new CustomEvent('navigate-to-preview-game', {
          detail: { tokenId: {  GameUrl: gameUrl, GameThumbnail: gameThumbnailUrl} },
        })
      );
      window.history.pushState({}, '', `/game/${game.gameId}`);
    }
  };

  // Function to render token ID if available
  const renderTokenId = () => {
    // Check if token info is available in game.tokens[0]
    const tokenInfo = game?.tokens?.[0]?.mintPublicKey || game?.tokens?.[0]?.tokenId || game?.tokens?.[0]?.id;

    if (tokenInfo) {
      return (
        <LineStackLayout noMargin alignItems="center">
          <Text color="secondary" size="body-small" noMargin>
            <Trans>Token Address:</Trans>
          </Text>
          <Spacer />
          <GameLinkAndShareIcons
            url={`https://pump.fun/coin/${tokenInfo}`}
            display={isSmallOrMediumScreen ? 'column' : 'line'}
          />
          {/* <LineStackLayout noMargin alignItems="center">
            <TextButton
              onClick={() => {
                window.open(`https://pump.fun/coin/${tokenInfo}`, '_blank');
              }}
              label={
                <Text color="primary" size="body-small" noMargin>
                  {tokenInfo.substring(0, 6)}...
                  {tokenInfo.substring(tokenInfo.length - 4)}
                </Text>
              }
            />
            <Tooltip
              title={
                copiedTokenAddress ? (
                  <Trans>Copied!</Trans>
                ) : (
                  <Trans>Copy to clipboard</Trans>
                )
              }
              placement="top"
            >
              <IconButton
                size="small"
                onClick={() => {
                  navigator.clipboard.writeText(tokenInfo);
                  setCopiedTokenAddress(true);
                  setTimeout(() => setCopiedTokenAddress(false), 2000);
                }}
              >
                {copiedTokenAddress ? (
                  <Check fontSize="small" />
                ) : (
                  <FileCopyOutlined fontSize="small" />
                )}
              </IconButton>
            </Tooltip>
          </LineStackLayout> */}
        </LineStackLayout>
      );
    } else if (game && gameUrl && !game.isArchived) {
      // Show deploy button for published games without tokens (but not for archived games)
      return (
        <LineStackLayout noMargin alignItems="center">
          <Text color="secondary" size="body-small" noMargin>
            <Trans>No token deployed</Trans>
          </Text>
          <Spacer />
          <FlatButton
            primary
            label={<Trans>Deploy Token</Trans>}
            onClick={handleDeployToken}
            disabled={isDeployingToken || !wallet.connected}
            icon={isDeployingToken ? <CircularProgress size={16} /> : null}
          />
        </LineStackLayout>
      );
    }

    return null;
  };

  // Token deployment form dialog
  const renderTokenDeploymentDialog = () => (
    <Dialog
      title={<Trans>Deploy Token for {game?.gameName || 'Game'}</Trans>}
      open={showTokenDialog}
      maxWidth="md"
      onClose={() => {
        if (!isDeployingToken) {
          setShowTokenDialog(false);
          setTokenDeployError(null);
        }
      }}
      actions={[
        <FlatButton
          key="cancel"
          label={<Trans>Cancel</Trans>}
          onClick={() => {
            setShowTokenDialog(false);
            setTokenDeployError(null);
          }}
          disabled={isDeployingToken}
        />,
        <FlatButton
          key="deploy"
          label={
            isDeployingToken ? (
              <Trans>Deploying...</Trans>
            ) : (
              <Trans>Deploy Token</Trans>
            )
          }
          primary
          onClick={handleConfirmTokenDeployment}
          disabled={
            isDeployingToken ||
            !tokenName.trim() ||
            !tokenDescription.trim() ||
            !tokenSymbol.trim()
          }
        />,
      ]}
    >
      <ColumnStackLayout noMargin>
        {tokenDeployError && (
          <Text color="error" size="body-small">
            <Trans>Error: {tokenDeployError}</Trans>
          </Text>
        )}

        {isDeployingToken ? (
          <LineStackLayout alignItems="center" justifyContent="center">
            <CircularProgress />
            <Spacer />
            <Text>
              <Trans>Deploying token, please wait...</Trans>
            </Text>
          </LineStackLayout>
        ) : (
          <ColumnStackLayout noMargin>
            <Text size="body" color="secondary">
              <Trans>
                Fill in the details for your game token. Required fields are
                marked with *
              </Trans>
            </Text>

            <TextField
              floatingLabelText={<Trans>Token Name *</Trans>}
              value={tokenName}
              onChange={(e, value) => setTokenName(value)}
              fullWidth
              maxLength={32}
              helperMarkdownText={
                <Trans>The name of your token (max 32 characters)</Trans>
              }
            />

            <TextField
              floatingLabelText={<Trans>Token Symbol *</Trans>}
              value={tokenSymbol}
              onChange={(e, value) => setTokenSymbol(value.toUpperCase())}
              fullWidth
              maxLength={10}
              helperMarkdownText={
                <Trans>
                  Short symbol for your token (max 10 characters, e.g., GAME)
                </Trans>
              }
            />

            <TextField
              floatingLabelText={<Trans>Description *</Trans>}
              value={tokenDescription}
              onChange={(e, value) => setTokenDescription(value)}
              fullWidth
              multiline
              rows={3}
              maxLength={200}
              helperMarkdownText={
                <Trans>Describe your token (max 200 characters)</Trans>
              }
            />

            <TextField
              floatingLabelText={<Trans>Website URL</Trans>}
              value={tokenWebsite}
              onChange={(e, value) => setTokenWebsite(value)}
              fullWidth
              helperMarkdownText={
                <Trans>Optional: Your game or project website</Trans>
              }
            />

            <TextField
              floatingLabelText={<Trans>Twitter Handle</Trans>}
              value={tokenTwitter}
              onChange={(e, value) => setTokenTwitter(value)}
              fullWidth
              helperMarkdownText={
                <Trans>Optional: Twitter handle (without @)</Trans>
              }
            />

            <TextField
              floatingLabelText={<Trans>Telegram</Trans>}
              value={tokenTelegram}
              onChange={(e, value) => setTokenTelegram(value)}
              fullWidth
              helperMarkdownText={
                <Trans>Optional: Telegram group or channel</Trans>
              }
            />

            <TextField
              floatingLabelText={<Trans>Developer Supply</Trans>}
              value={tokenDevSupply}
              onChange={(e, value) => setTokenDevSupply(value)}
              fullWidth
              type="number"
              helperMarkdownText={
                <Trans>
                  Number of tokens reserved for the developer (default: 1000)
                </Trans>
              }
            />

            <LineStackLayout noMargin alignItems="center">
              <Text size="body-small" color="secondary">
                <Trans>Token Image (optional):</Trans>
              </Text>
              <input
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                style={{ marginLeft: 8 }}
              />
            </LineStackLayout>

            {tokenImage && (
              <Text size="body-small" color="secondary">
                <>Selected: {tokenImage.name}</>
              </Text>
            )}
          </ColumnStackLayout>
        )}
      </ColumnStackLayout>
    </Dialog>
  );

  // Token deployment status dialog
  const renderTokenStatusDialog = () => (
    <Dialog
      title={
        tokenDeployError ? (
          <Trans>Token Deployment Error</Trans>
        ) : (
          <Trans>Token Deployment Success</Trans>
        )
      }
      open={!!tokenDeployError || tokenDeploySuccess}
      maxWidth="md"
      onClose={() => {
        setTokenDeployError(null);
        setTokenDeploySuccess(false);
      }}
      actions={[
        <FlatButton
          key="close"
          label={<Trans>Close</Trans>}
          onClick={() => {
            setTokenDeployError(null);
            setTokenDeploySuccess(false);
          }}
        />,
      ]}
    >
      {tokenDeployError ? (
        <Text>
          <Trans>Error deploying token: {tokenDeployError}</Trans>
        </Text>
      ) : tokenDeploySuccess ? (
        <Text>
          <Trans>
            Token successfully deployed! The game card will be updated with the
            token address.
          </Trans>
        </Text>
      ) : null}
    </Dialog>
  );

  return (
    <I18n>
      {({ i18n }) => (
        <>
          {renderTokenDeploymentDialog()}
          {renderTokenStatusDialog()}
          <Card
            background={'medium'}
            isHighlighted={isCurrentProjectOpened}
            padding={isMobile ? 8 : 16}
          >
            {isMobile && !isLandscape ? (
              <ColumnStackLayout>
                <Column noMargin>
                  <LineStackLayout noMargin justifyContent="space-between">
                    {renderTitle()}
                    {renderAdditionalActions()}
                  </LineStackLayout>
                  {renderLastModification(i18n)}
                </Column>
                <LineStackLayout noMargin>
                  {renderThumbnail()}
                  {renderPublicInfo()}
                </LineStackLayout>
                {renderTokenId()}
                {renderButtons({ fullWidth: true })}
              </ColumnStackLayout>
            ) : (
              <LineStackLayout noMargin>
                {renderThumbnail()}
                <ColumnStackLayout
                  expand
                  justifyContent="space-between"
                  noOverflowParent
                >
                  <ColumnStackLayout noMargin>
                    <LineStackLayout
                      noMargin
                      justifyContent="space-between"
                      alignItems="flex-start"
                    >
                      <ColumnStackLayout noMargin>
                        {renderLastModification(i18n)}
                        {renderTitle()}
                      </ColumnStackLayout>
                      <LineStackLayout noMargin>
                        {!isSmallOrMediumScreen && renderStorageProvider(i18n)}
                        {renderAdditionalActions()}
                      </LineStackLayout>
                    </LineStackLayout>
                    {renderPublicInfo()}
                  </ColumnStackLayout>
                  <ColumnStackLayout noMargin>
                    {renderTokenId()}
                    <LineStackLayout noMargin justifyContent="flex-end">
                      {renderButtons({ fullWidth: false })}
                    </LineStackLayout>
                  </ColumnStackLayout>
                </ColumnStackLayout>
              </LineStackLayout>
            )}
          </Card>

          {/* Renew Confirmation Modal */}
          {showRenewConfirmation && (
            <Dialog
              title={<Trans>Confirm Subscription Renewal</Trans>}
              actions={[
                <FlatButton
                  key="cancel"
                  label={<Trans>Cancel</Trans>}
                  onClick={() => setShowRenewConfirmation(false)}
                />,
                <RaisedButton
                  key="confirm"
                  label={<Trans>Confirm Renewal</Trans>}
                  primary
                  onClick={confirmRenewal}
                />,
              ]}
              open={showRenewConfirmation}
              onRequestClose={() => setShowRenewConfirmation(false)}
              maxWidth="md"
            >
              <ColumnStackLayout noMargin>
                <Text>
                  <Trans>
                    Are you sure you want to renew the subscription for "
                    {game?.gameName || 'this game'}"?
                  </Trans>
                </Text>

                {walletProfile && publishFeeUSD && (
                  <ColumnStackLayout noMargin>
                    <Spacer />
                    <Line
                      noMargin
                      alignItems="center"
                      justifyContent="space-between"
                    >
                      <Text color="secondary">
                        <Trans>Current Balance:</Trans>
                      </Text>
                      <Text>
                        {(walletProfile.balance || 0).toFixed(2)} balance
                      </Text>
                    </Line>

                    <Line
                      noMargin
                      alignItems="center"
                      justifyContent="space-between"
                    >
                      <Text color="secondary">
                        <Trans>Renewal Cost:</Trans>
                      </Text>
                      <Text>-{publishFeeUSD.toFixed(2)} balance</Text>
                    </Line>

                    <Line
                      noMargin
                      alignItems="center"
                      justifyContent="space-between"
                    >
                      <Text color="secondary">
                        <Trans>Balance After Renewal:</Trans>
                      </Text>
                      <Text style={{ fontWeight: 'bold' }}>
                        {((walletProfile.balance || 0) - publishFeeUSD).toFixed(
                          2
                        )}{' '}
                        balance
                      </Text>
                    </Line>
                  </ColumnStackLayout>
                )}
              </ColumnStackLayout>
            </Dialog>
          )}

          {/* Cancel Confirmation Modal */}
          {showCancelConfirmation && (
            <Dialog
              title={<Trans>Confirm Subscription Cancellation</Trans>}
              actions={[
                <FlatButton
                  key="cancel"
                  label={<Trans>Keep Subscription</Trans>}
                  onClick={() => setShowCancelConfirmation(false)}
                />,
                <RaisedButton
                  key="confirm"
                  label={<Trans>Cancel Subscription</Trans>}
                  primary
                  onClick={confirmCancellation}
                />,
              ]}
              open={showCancelConfirmation}
              onRequestClose={() => setShowCancelConfirmation(false)}
              maxWidth="md"
            >
              <ColumnStackLayout noMargin>
                <Text>
                  <Trans>
                    Are you sure you want to cancel the subscription for "
                    {game?.gameName || 'this game'}"?
                  </Trans>
                </Text>
                <Text color="secondary" size="body-small">
                  <Trans>
                    This action will cancel your game subscription. Your game
                    will be archived and no longer accessible to players.
                  </Trans>
                </Text>
              </ColumnStackLayout>
            </Dialog>
          )}
        </>
      )}
    </I18n>
  );
};

export default GameDashboardCard;
