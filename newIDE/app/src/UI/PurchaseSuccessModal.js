// @flow
import * as React from 'react';
import Dialog from './Dialog';
import { Column, Line } from './Grid';
import Text from './Text';
import RaisedButton from './RaisedButton';
import IconButton from './IconButton';
import { LineStackLayout, ColumnStackLayout } from './Layout';
import { Trans } from '@lingui/macro';
import Check from './CustomSvgIcons/Check';
import Copy from './CustomSvgIcons/Copy';
import { copyTextToClipboard } from '../Utils/Clipboard';
import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles(theme => ({
  checkIconContainer: {
    width: 60,
    height: 60,
    borderRadius: '50%',
    border: '5px solid #4ab0e4',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing(1),
    backgroundColor: 'transparent',
  },
  checkIcon: {
    fontSize: 40,
    fontweigth: 'bold',
    stroke: 5,
    color: '#4ab0e4',
  },
  transactionContainer: {
    backgroundColor: 'rgba(74, 176, 228, 0.1)',
    border: '1px solid #4ab0e4',
    borderRadius: 8,
    paddingInline: '6px',
  },
  transactionId: {
    fontFamily: 'monospace',
    fontSize: '0.9rem',
    wordBreak: 'break-all',
    color: '#4ab0e4',
  },
  okButton: {
    backgroundColor: '#4ab0e4',
    color: '#ffffff',
    minWidth: 120,
    '&:hover': {
      backgroundColor: '#3a9bd4',
    },
  },
  copyButton: {
    color: '#4ab0e4',
    marginLeft: theme.spacing(1),
  },
}));

type Props = {|
  open: boolean,
  onClose: () => void,
  amount: number,
  solAmount: number,
  transactionId: string,
  title?: React.Node,
  amountLabel?: string, // e.g., "credits", "balance", etc.
|};

const PurchaseSuccessModal = ({
  open,
  onClose,
  amount,
  solAmount,
  transactionId,
  title,
  amountLabel = 'balance',
}: Props) => {
  const classes = useStyles();
  const [copied, setCopied] = React.useState(false);

  const handleCopyTransactionId = React.useCallback(
    () => {
      copyTextToClipboard(transactionId);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    },
    [transactionId]
  );

  const dialogActions = [
    <RaisedButton
      key="ok"
      label={<Trans>OK</Trans>}
      primary
      onClick={onClose}
      className={classes.okButton}
    />,
  ];

  return (
    <Dialog
      title=""
      open={open}
      onRequestClose={onClose}
      actions={dialogActions}
      maxWidth="xs"
      cannotBeDismissed
      flexColumnBody
      propStyles={{
        background: '#25252D',
        borderRadius: '30px',
      }}
    >
      <ColumnStackLayout alignItems="center" noMargin>
        {/* Success Icon */}
        <div className={classes.checkIconContainer}>
          <Check className={classes.checkIcon} />
        </div>

        {/* Title */}
        <Text size="section-title" color="primary" align="center">
          {title || <Trans>Purchase Successful</Trans>}
        </Text>

        {/* Success Message */}
        <Text size="body" align="center" color="secondary">
          Successfully purchased {amount} {amountLabel} with{' '}
          {solAmount.toFixed(4)} SOL
        </Text>
        <Text size="body" align="center" color="secondary" />
        <Text size="section-title">
          <Trans>SOL Transaction:</Trans>
        </Text>
        {/* Transaction ID Section */}
        <div className={classes.transactionContainer}>
          <ColumnStackLayout noMargin>
            <LineStackLayout alignItems="center" noMargin>
              <Text
                className={classes.transactionId}
                allowSelection
                style={{
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  maxWidth: 320, // adjust as needed for your layout
                  display: 'block',
                  fontSize: '12px',
                }}
              >
                {transactionId}
              </Text>
              <IconButton
                size="small"
                onClick={handleCopyTransactionId}
                className={classes.copyButton}
                tooltip={copied ? 'Copied!' : 'Copy to clipboard'}
              >
                {copied ? (
                  <Check fontSize="small" />
                ) : (
                  <Copy fontSize="small" />
                )}
              </IconButton>
            </LineStackLayout>
          </ColumnStackLayout>
        </div>
      </ColumnStackLayout>
    </Dialog>
  );
};

export default PurchaseSuccessModal;
