// @flow
import * as React from 'react';
import { Trans } from '@lingui/macro';
import { getGravatarUrl } from '../GravatarUrl';
import RaisedButton from '../RaisedButton';
import { shortenString } from '../../Utils/StringHelpers';
import TextButton from '../TextButton';
import { LineStackLayout } from '../Layout';
import AuthenticatedUserContext from '../../Profile/AuthenticatedUserContext';
import CircularProgress from '../CircularProgress';
import { SubscriptionSuggestionContext } from '../../Profile/Subscription/SubscriptionSuggestionContext';
import { hasValidSubscriptionPlan } from '../../Utils/GDevelopServices/Usage';
import CrownShining from '../CustomSvgIcons/CrownShining';
import UserAvatar from './UserAvatar';
import { useResponsiveWindowSize } from '../Responsive/ResponsiveWindowMeasurer';
import IconButton from '../IconButton';
import FlatButton from '../FlatButton';
import { useMetaMask } from '../../Hooks/useMetaMask';
import useAuth from '../../Utils/IIAuth';
import { useAuthStore } from '../../Utils/store/AuthContext';
import { LoginEnum } from '../../Utils/constant/fantasticonst';
import Dialog from '../Dialog';
import { AccountIdentifier, SubAccount } from '@dfinity/ledger-icp';
import { FileCopyOutlined, Check, ExitToApp, Person } from '@material-ui/icons';
import {
  WalletDisconnectButton,
  WalletMultiButton,
} from '@solana/wallet-adapter-react-ui';
import { useWallet } from '@solana/wallet-adapter-react';
import {
  requestNonce,
  verifySignature,
  storeAuthToken,
  getAuthToken,
  removeAuthToken,
  isWalletAuthenticated,
} from '../../Utils/GDevelopServices/WalletAuthentication';
import Text from '../Text';
import { useWalletUser } from '../../Utils/store/WalletUserContext';
import Popover from '@material-ui/core/Popover';
import Menu from '@material-ui/core/Menu';
import MenuItem from '@material-ui/core/MenuItem';
import ListItemIcon from '@material-ui/core/ListItemIcon';
import ListItemText from '@material-ui/core/ListItemText';
import Divider from '@material-ui/core/Divider';
import WalletUserProfileDialog from './WalletUserProfileDialog';

const styles = {
  buttonContainer: { flexShrink: 0 },
  walletAvatar: {
    width: 36,
    height: 36,
    borderRadius: '50%',
    backgroundColor: '#333',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: 'white',
    overflow: 'hidden',
  },
  avatarIcon: {
    fontSize: 20,
    color: 'white',
    cursor: 'pointer',
  },
  menuItem: {
    paddingTop: 6,
    paddingBottom: 6,
  },
  addressText: {
    fontSize: '0.9em',
  },
  balanceContainer: {
    padding: '8px 16px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  balanceLabel: {
    fontSize: '0.8em',
    opacity: 0.7,
  },
  balanceValue: {
    fontSize: '1.2em',
    fontWeight: 'bold',
  },
};

// Stub function for getSubAccountBalance since it was removed
const getSubAccountBalance = async (identity, actor) => {
  console.log('getSubAccountBalance called with', identity, actor);
  return {
    account: {
      toHex: () => '0x123456789abcdef',
    },
    balance: 0,
  };
};

const GetPremiumButton = () => {
  const { openSubscriptionDialog } = React.useContext(
    SubscriptionSuggestionContext
  );
  return (
    <RaisedButton
      icon={<CrownShining />}
      onClick={() => {
        openSubscriptionDialog({
          analyticsMetadata: {
            reason: 'Account get premium',
            recommendedPlanId: 'gdevelop_silver',
          },
        });
      }}
      id="get-premium-button"
      label={<Trans>Get premium</Trans>}
      color="premium"
    />
  );
};

type Props = {|
  onOpenProfile: () => void,
|};

const DepositModal = ({ onClose, auth }) => {
  const [acc, setAcc] = React.useState('...');
  const [balance, setBalance] = React.useState(0);
  const [copied, setCopied] = React.useState(false);

  const fetch = async () => {
    const { account, balance } = await getSubAccountBalance(
      auth.identity,
      auth.actor
    );
    console.log(account, account.toHex(), 'acc');
    setAcc(account ? account.toHex() : '...');
    setBalance(balance);
  };

  const handleCopyAddress = () => {
    navigator.clipboard
      .writeText(acc)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 3000);
      })
      .catch(() => {
        // Handle copy error if needed
      });
  };

  React.useEffect(() => {
    fetch();
    const interval = setInterval(fetch, 3000); // Poll every 3 seconds

    return () => clearInterval(interval); // Cleanup interval on unmount
  }, []);

  return (
    <Dialog
      title={
        <div style={{ textAlign: 'center', padding: '16px 0 8px 0' }}>
          <Trans>Deposit ICP</Trans>
        </div>
      }
      actions={[
        <FlatButton
          label={<Trans>Close</Trans>}
          onClick={onClose}
          key="close"
        />,
      ]}
      open
      onRequestClose={onClose}
      maxWidth="sm"
    >
      <div style={{ padding: '0 24px' }}>
        <Text style={{ marginBottom: 16 }}>
          <Trans>Deposit ICP from your wallet to this address:</Trans>
        </Text>

        <div
          style={{
            backgroundColor: 'rgba(0, 0, 0, 0.05)',
            border: '2px solid rgba(0, 0, 0, 0.2)',
            borderRadius: 8,
            padding: 16,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
            gap: 8,
          }}
        >
          <Text
            style={{
              fontSize: '1.1em',
              fontWeight: 500,
              letterSpacing: '0.5px',
              wordBreak: 'break-all',
              flex: 1,
              minWidth: '200px',
            }}
          >
            {acc.split('').map((char, index) => (
              <span
                key={index}
                style={{
                  color: /\d/.test(char)
                    ? 'inherit'
                    : 'rgba(255, 255, 255, 0.7)',
                }}
              >
                {char}
              </span>
            ))}
          </Text>
          {acc !== '...' && (
            <IconButton
              size="small"
              onClick={handleCopyAddress}
              style={{ marginLeft: 8 }}
            >
              {copied ? (
                <Check fontSize="small" />
              ) : (
                <FileCopyOutlined fontSize="small" />
              )}
            </IconButton>
          )}
        </div>

        <Text
          style={{
            marginTop: 24,
            textAlign: 'center',
            fontWeight: 500,
          }}
        >
          <Trans>Current Balance:</Trans> {balance} ICP
        </Text>
      </div>
    </Dialog>
  );
};

const UserChip = ({ onOpenProfile }: Props) => {
  const authenticatedUser = React.useContext(AuthenticatedUserContext);
  const {
    profile,
    onOpenCreateAccountDialog,
    onOpenLoginDialog,
    loginState,
    subscription,
  } = authenticatedUser;
  const wallet = useWallet();
  const { connected, publicKey } = wallet;
  const [isAuthenticating, setIsAuthenticating] = React.useState(false);
  const [isWalletAuth, setIsWalletAuth] = React.useState(
    isWalletAuthenticated()
  );
  const [authError, setAuthError] = React.useState(null);
  const { profile: walletProfile, fetchProfile } = useWalletUser();

  const { login, logout, initAuth } = useAuth();
  const {
    state: { auth },
  } = useAuthStore();
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [userProfileOpen, setUserProfileOpen] = React.useState(false);
  const [copiedAddress, setCopiedAddress] = React.useState(false);

  const truncateAddress = address => {
    if (!address) return '';
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const isPremium = hasValidSubscriptionPlan(subscription);
  const { isMobile } = useResponsiveWindowSize();

  React.useEffect(() => {
    if (auth) {
      initAuth();
    }
  }, []);

  // Check wallet authentication status on component mount and when wallet connection changes
  React.useEffect(
    () => {
      // Update authentication status whenever connected state changes
      setIsWalletAuth(isWalletAuthenticated());
    },
    [connected]
  );

  // Check if connected wallet supports message signing
  const walletSupportsSignMessage = React.useMemo(
    () => {
      return (
        wallet && wallet.signMessage && typeof wallet.signMessage === 'function'
      );
    },
    [wallet]
  );

  // Add use effect to check wallet capabilities when connected
  React.useEffect(
    () => {
      if (connected && !walletSupportsSignMessage) {
        setAuthError('This wallet does not support message signing.');
      } else if (!connected) {
        // Clear error when disconnected
        setAuthError(null);
      }
    },
    [connected, walletSupportsSignMessage]
  );

  // Authenticate wallet when connected but not authenticated
  React.useEffect(
    () => {
      let isAttempting = false;
      let userRejected = false;

      const handleWalletAuthentication = async () => {
        // Prevent multiple simultaneous attempts and respect user rejection
        if (
          isAttempting ||
          userRejected ||
          isAuthenticating ||
          !connected ||
          !publicKey ||
          isWalletAuth ||
          !walletSupportsSignMessage
        ) {
          return;
        }

        isAttempting = true;
        try {
          setIsAuthenticating(true);
          setAuthError(null);

          // Get wallet address
          const walletAddress = publicKey.toString();

          // Request a nonce from the server
          const { nonce, message } = await requestNonce(walletAddress);

          // Request user to sign the message with proper error handling
          try {
            const encodedMessage = new TextEncoder().encode(message);
            const signature = await wallet.signMessage(encodedMessage);
            const signatureString = Buffer.from(signature).toString('hex');

            // Verify signature and get token
            const { token } = await verifySignature({
              walletAddress,
              signature: signatureString,
              nonce,
            });

            // Store the token
            storeAuthToken(token);
            await new Promise(resolve => setTimeout(resolve, 100));
            await fetchProfile();
            setIsWalletAuth(true);
          } catch (signError) {
            // Check for user rejection error specifically
            if (
              signError.message &&
              (signError.message.includes('User rejected') ||
                signError.message.includes('User denied') ||
                signError.message.includes('cancelled') ||
                signError.message.includes('canceled') ||
                signError.message.includes('rejected'))
            ) {
              userRejected = true;
              setAuthError(
                'Authentication cancelled. Click Connect to try again.'
              );
            } else {
              setAuthError(signError.message || 'Signature failed');
            }
            console.error('Wallet signature error:', signError);
          }
        } catch (error) {
          console.error('Wallet authentication error:', error);
          setAuthError(error.message || 'Authentication failed');
        } finally {
          setIsAuthenticating(false);
          isAttempting = false;
        }
      };

      if (
        connected &&
        publicKey &&
        !isWalletAuth &&
        !isAuthenticating &&
        !userRejected
      ) {
        handleWalletAuthentication();
      }

      // Clear the user rejection flag when wallet is disconnected
      if (!connected) {
        userRejected = false;
      }

      return () => {
        // Clean up
        isAttempting = false;
      };
    },
    [
      connected,
      publicKey,
      isWalletAuth,
      isAuthenticating,
      wallet,
      walletSupportsSignMessage,
    ]
  );

  // Handle wallet disconnect - clear all states and errors
  const handleWalletDisconnect = () => {
    removeAuthToken();
    setIsWalletAuth(false);
    setAuthError(null); // Clear any error messages
    wallet.disconnect();
    setAnchorEl(null); // Close menu
  };

  // Open wallet user menu
  const handleOpenMenu = event => {
    setAnchorEl(event.currentTarget);
  };

  // Close wallet user menu
  const handleCloseMenu = () => {
    setAnchorEl(null);
  };

  // Copy wallet address to clipboard
  const handleCopyAddress = () => {
    if (publicKey) {
      navigator.clipboard
        .writeText(publicKey.toString())
        .then(() => {
          setCopiedAddress(true);
          setTimeout(() => setCopiedAddress(false), 3000);
        })
        .catch(() => {
          // Handle clipboard error
        });
    }
  };

  // Open user profile dialog
  const handleOpenUserProfile = () => {
    setUserProfileOpen(true);
    handleCloseMenu();
  };

  // Modify the handleManualAuthenticate function to check if the wallet can sign
  const handleManualAuthenticate = async () => {
    if (!connected || !publicKey) {
      // Connect wallet first if not connected
      try {
        await wallet.connect();
        return; // Return here, the connection effect will trigger the authentication
      } catch (error) {
        console.error('Failed to connect wallet:', error);
        return;
      }
    }

    // Check if the wallet can sign messages
    if (!walletSupportsSignMessage) {
      setAuthError('This wallet does not su signing.');
      return;
    }

    try {
      setIsAuthenticating(true);
      setAuthError(null);

      // Get wallet address
      const walletAddress = publicKey.toString();

      // Request a nonce from the server
      const { nonce, message } = await requestNonce(walletAddress);

      // Request user to sign the message
      const encodedMessage = new TextEncoder().encode(message);
      const signature = await wallet.signMessage(encodedMessage);
      const signatureString = Buffer.from(signature).toString('hex');

      // Verify signature and get token
      const { token } = await verifySignature({
        walletAddress,
        signature: signatureString,
        nonce,
      });

      // Store the token
      storeAuthToken(token);
      setIsWalletAuth(true);
    } catch (error) {
      console.error('Manual wallet authentication error:', error);
      setAuthError(error.message || 'Authentication failed');
    } finally {
      setIsAuthenticating(false);
    }
  };

  return !profile && loginState === 'loggingIn' ? (
    <CircularProgress size={25} />
  ) : profile ? (
    <LineStackLayout noMargin>
      {!isMobile ? (
        <TextButton
          label={shortenString(profile.username || profile.email, 20)}
          onClick={onOpenProfile}
          allowBrowserAutoTranslate={false}
          icon={
            <UserAvatar
              iconUrl={getGravatarUrl(profile.email || '', { size: 50 })}
              isPremium={isPremium}
            />
          }
        />
      ) : (
        <IconButton size="small" onClick={onOpenProfile}>
          <UserAvatar
            iconUrl={getGravatarUrl(profile.email || '', { e: 50 })}
            isPremium={isPremium}
          />
        </IconButton>
      )}
      {isPremium ? null : <GetPremiumButton />}
    </LineStackLayout>
  ) : (
    <div style={styles.buttonContainer}>
      <LineStackLayout noMargin alignItems="center">
        {!connected ? (
          <WalletMultiButton
            onClick={wallet.connect}
            style={{
              backgroundColor: '#16FFB5',
              height: 28,
              borderRadius: 8,
              fontFamily: 'Space Grotesk',
            }}
          >
            {connected ? 'Disconnect' : 'Connect'}
          </WalletMultiButton>
        ) : (
          <LineStackLayout noMargin alignItems="center">
            {isAuthenticating ? (
              <CircularProgress size={20} style={{ marginRight: 8 }} />
            ) : (
              <>
                {isWalletAuth ? (
                  <>
                    <div
                      onClick={handleOpenMenu}
                      style={styles.walletAvatar}
                      aria-controls="wallet-menu"
                      aria-haspopup="true"
                    >
                      <Person style={styles.avatarIcon} />
                    </div>
                    <Menu
                      id="wallet-menu"
                      anchorEl={anchorEl}
                      keepMounted
                      open={Boolean(anchorEl)}
                      onClose={handleCloseMenu}
                    >
                      {/* Wallet balance */}
                      <div style={styles.balanceContainer}>
                        <span style={styles.balanceLabel}>
                          <Trans>Balance</Trans>
                        </span>
                        <span style={styles.balanceValue}>
                          {walletProfile?.balance
                            ? walletProfile?.balance?.toFixed(2)
                            : '0.00'}
                        </span>
                      </div>
                      <Divider />

                      {/* Wallet address */}
                      <MenuItem
                        onClick={handleCopyAddress}
                        style={styles.menuItem}
                      >
                        <ListItemIcon>
                          {copiedAddress ? (
                            <Check fontSize="small" />
                          ) : (
                            <FileCopyOutlined fontSize="small" />
                          )}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <span style={styles.addressText}>
                              {truncateAddress(publicKey?.toBase58())}
                            </span>
                          }
                          secondary={
                            copiedAddress ? (
                              <Trans>Copied!</Trans>
                            ) : (
                              <Trans>Copy address</Trans>
                            )
                          }
                        />
                      </MenuItem>

                      {/* Profile */}
                      <MenuItem
                        onClick={handleOpenUserProfile}
                        style={styles.menuItem}
                      >
                        <ListItemIcon>
                          <Person fontSize="small" />
                        </ListItemIcon>
                        <ListItemText primary={<Trans>Profile</Trans>} />
                      </MenuItem>

                      {/* Disconnect */}
                      <MenuItem
                        onClick={handleWalletDisconnect}
                        style={styles.menuItem}
                      >
                        <ListItemIcon>
                          <ExitToApp fontSize="small" />
                        </ListItemIcon>
                        <ListItemText primary={<Trans>Disconnect</Trans>} />
                      </MenuItem>
                    </Menu>
                  </>
                ) : (
                  <>
                    <FlatButton
                      label={
                        <span>{truncateAddress(publicKey?.toBase58())}</span>
                      }
                      primary
                      disabled
                    />
                    <FlatButton
                      label={<Trans>Authenticate</Trans>}
                      onClick={handleManualAuthenticate}
                      style={{
                        marginRight: 8,
                        backgroundColor: '#16FFB5',
                        height: 28,
                        borderRadius: 8,
                      }}
                    />
                  </>
                )}
              </>
            )}
          </LineStackLayout>
        )}
      </LineStackLayout>
      {/* {authError && (
        <Text size="body" style={{ color: 'red', marginTop: 4, fontSize: 12 }}>
          {authError}
        </Text>
      )} */}
      {isModalOpen && (
        <DepositModal onClose={() => setIsModalOpen(false)} auth={auth} />
      )}
      {userProfileOpen && (
        <WalletUserProfileDialog
          open={userProfileOpen}
          onClose={() => setUserProfileOpen(false)}
        />
      )}
    </div>
  );
};

export default UserChip;
