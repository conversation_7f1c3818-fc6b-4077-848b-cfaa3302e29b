// @flow
import * as React from 'react';
import Dialog from './Dialog';
import { Column, Line } from './Grid';
import Text from './Text';
import RaisedButton from './RaisedButton';
import { LineStackLayout, ColumnStackLayout } from './Layout';
import { Trans } from '@lingui/macro';
import { makeStyles } from '@material-ui/core/styles';

const useStyles = makeStyles(theme => ({
  balanceContainer: {
    backgroundColor: 'rgba(74, 176, 228, 0.1)',
    border: '1px solid #4ab0e4',
    borderRadius: 8,
    padding: theme.spacing(2),
    marginBottom: theme.spacing(2),
    width: '90%',
    marginInline: 'auto',
  },
  balanceRow: {
    display: 'flex',
    justifyContent: 'space-between',
    marginBottom: theme.spacing(1),
  },
  divider: {
    borderTop: '1px solid rgba(255, 255, 255, 0.2)',
    margin: `${theme.spacing(1)}px 0`,
    width: '100%',
  },
  confirmButton: {
    backgroundColor: '#4ab0e4',
    color: '#ffffff',
    minWidth: 120,
    '&:hover': {
      backgroundColor: '#3a9bd4',
    },
  },
  cancelButton: {
    marginRight: theme.spacing(2),
  },
  insufficientFunds: {
    color: '#ff5555',
    fontWeight: 'bold',
  }
}));

type Props = {|
  open: boolean,
  onClose: () => void,
  onConfirm: () => void,
  currentBalance: number,
  publishFee: number,
  insufficientFunds: boolean,
|};

const PublishFeeConfirmationModal = ({
  open,
  onClose,
  onConfirm,
  currentBalance,
  publishFee,
  insufficientFunds,
}) => {
  const classes = useStyles();
  const remainingBalance = currentBalance - publishFee;

  const dialogActions = [
    <RaisedButton
      key="cancel"
      label={<Trans>Cancel</Trans>}
      onClick={onClose}
      className={classes.cancelButton}
    />,
    <RaisedButton
      key="confirm"
      label={<Trans>Confirm & Publish</Trans>}
      primary
      onClick={onConfirm}
      className={classes.confirmButton}
      disabled={insufficientFunds}
    />,
  ];

  return (
    <Dialog
      title={<Trans>Confirm Publication</Trans>}
      open={open}
      onRequestClose={onClose}
      actions={dialogActions}
      maxWidth="sm"
      cannotBeDismissed
      flexColumnBody
    >
      <ColumnStackLayout noMargin>
        <Text size="body" align="center">
          <Trans>
            Publishing your game will incur the following fee:
          </Trans>
        </Text>
        
        <div className={classes.balanceContainer}>
          <div className={classes.balanceRow}>
            <Text>
              <Trans>Current Balance:</Trans>
            </Text>
            <Text>{currentBalance.toFixed(2)} USD</Text>
          </div>
          
          <div className={classes.balanceRow}>
            <Text>
              <Trans>Publish Fee:</Trans>
            </Text>
            <Text>-{publishFee.toFixed(2)} USD</Text>
          </div>
          
          <div className={classes.divider} />
          
          <div className={classes.balanceRow}>
            <Text>
              <Trans>Remaining Balance:</Trans>
            </Text>
            <Text 
              className={insufficientFunds ? classes.insufficientFunds : ''}
            >
              {remainingBalance.toFixed(2)} USD
            </Text>
          </div>
        </div>
        
        {insufficientFunds && (
          <Text size="body" align="center" className={classes.insufficientFunds}>
            <Trans>
              You don't have enough credits to publish your game. Please purchase more credits.
            </Trans>
          </Text>
        )}
      </ColumnStackLayout>
    </Dialog>
  );
};

export default PublishFeeConfirmationModal;
